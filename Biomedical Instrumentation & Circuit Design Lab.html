<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biomedical Instrumentation & Circuit Design Lab</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom CSS for elements that need more precise control */
        .circuit-component {
            cursor: grab;
            user-select: none;
            transition: transform 0.1s ease;
        }
        
        .circuit-component:active {
            cursor: grabbing;
        }
        
        .wire {
            position: absolute;
            background-color: #4ade80;
            height: 2px;
            transform-origin: left center;
            pointer-events: none;
            z-index: 10;
        }
        
        .terminal {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #f59e0b;
            position: absolute;
            cursor: pointer;
            z-index: 20;
        }
        
        .terminal:hover {
            transform: scale(1.5);
            background-color: #fbbf24;
        }
        
        .grid-bg {
            background-image: 
                linear-gradient(to right, rgba(55, 65, 81, 0.3) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(55, 65, 81, 0.3) 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        /* Dark theme scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1f2937;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #4b5563;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }
        
        /* Language switch animation */
        .lang-switch {
            transition: all 0.3s ease;
        }
        
        .lang-switch:hover {
            transform: scale(1.05);
        }
        
        /* Module card hover effect */
        .module-card {
            transition: all 0.2s ease;
        }
        
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
        }
        
        /* Tooltip styles */
        .tooltip {
            position: relative;
        }
        
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 120px;
            background-color: #111827;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        /* Base64 encoded icons */
        .icon-resistor {
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTEwLDJIMTJWNEgxNVY2SDEyVjRIMTBWNkg3VjRIMTBWMk0xOSw0VjZIMTZWNEgxOU0yMCw0VjJIMThWNEgyME0xOSwxNlYxOEgxNlYxNkgxOV0vPjwvc3ZnPg==");
        }
        
        .icon-capacitor {
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTQsNFYySDEwVjRIN1YxMUgxMFYxM0g0VjExSDdWNkg0VjRNMTQsMTNWMTFIMTdWNkgxNFY0SDE3VjJIMTBWNkg3VjRIMTBWMkgxOFY0SDIxVjZIMThWMTFIMjFWMTNIMThWMTZIMjFWMTlIMThWMjFIMTBWMTlIMTdWMTZIMTBWMTlIN1YxN0gxMFYxNUg0VjE3SDdWMjBINFYxN0g3VjE2SDRWMTNIN1YxNkgxMFYxM00xNCwxN1YxNkgxN1YxN0gxNFoiLz48L3N2Zz4=");
        }
        
        .icon-opamp {
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTE1LDE1VjE4SDE4VjE1SDE1TTE1LDZWOEgxOFY2SDE1TTE5LDNIMTVDMTMuOSwzIDEzLDMuOSAxMyw1VjE5QzEzLDIwLjEgMTMuOSwyMSAxNSwyMUgxOUMyMC4xLDIxIDIxLDIwLjEgMjEsMTlWNUMyMSwzLjkgMjAuMSwzIDE5LDNNNy4zLDUuOEw4LjcsNy4yTDcuMyw4LjZMNS45LDcuMkw3LjMsNS44TTcuMywxNi44TDguNywxNS40TDcuMywxNC4wTDUuOSwxNS40TDcuMywxNi44TTkuOCwxMy4wTDExLjIsMTEuNkw5LjgsMTAuMkw4LjQsMTEuNkw5LjgsMTMuME05LjgsMTguOEwxMS4yLDE3LjRMOS44LDE2LjBMOC40LDE3LjRMOS44LDE4LjgiLz48L3N2Zz4=");
        }
        
        .icon-ground {
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTEyLDNMMiwyMVYyM0gyMlYyMUwxMiwzTTExLDkuOTlMMTQuNTMsMTdIOS40N0wxMSw5Ljk5WiIvPjwvc3ZnPg==");
        }
        
        .icon-signal {
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTQsMlYyMEgyVjJINFBNMTYsMlYyMEgxNFYySDE2TTIwLDZWMThIMThWNkgxNU0xNSwxMFYxNEgxM1YxMEgxNU05LDZWMThIN1Y2SDlNMTIsNlYxOEgxMFY2SDEyWiIvPjwvc3ZnPg==");
        }
        
        .icon-probe {
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTEyLDJBMTAsMTAgMCAwLDEgMjIsMTJBMTAsMTAgMCAwLDEgMTIsMjJBMTAsMTAgMCAwLDEgMiwxMkExMCwxMCAwIDAsMSAxMiwyTTEyLDRBNiw2IDAgMCwwIDYsMTBNMTIsMjBBNiw2IDAgMCwwIDE4LDE0TTEyLDE2QTQsNCAwIDAsMSA4LDEyQTQsNCAwIDAsMSAxMiw4QTQsNCAwIDAsMSAxNiwxMkE0LDQgMCAwLDEgMTIsMTZNNiwyMkwxNiwxMk0xNiwxMkwxMiwxNk0xMiwxNkw2LDIyTTYsMjJMMiwxOE0yLDE4TDEyLDhNMTIsOEwxNiwxMk0xNiwxMkwyMiw2TTIyLDZMMTgsMk0xOCwyTDEyLDhNMTIsOEw2LDIiLz48L3N2Zz4=");
        }
        
        /* ECG signal waveform */
        .ecg-waveform {
            position: relative;
            height: 100px;
            width: 100%;
            overflow: hidden;
            background-color: #1f2937;
        }
        
        .ecg-line {
            position: absolute;
            height: 2px;
            background-color: #3b82f6;
            top: 50%;
            width: 100%;
            transform: translateY(-50%);
        }
        
        .ecg-peak {
            position: absolute;
            width: 2px;
            background-color: #3b82f6;
        }
        
        /* Ripple effect for buttons */
        .ripple {
            position: relative;
            overflow: hidden;
        }
        
        .ripple:after {
            content: "";
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform .5s, opacity 1s;
        }
        
        .ripple:active:after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }
        
        /* Custom animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes slideInRight {
            from { transform: translateX(20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .slide-in-right {
            animation: slideInRight 0.3s ease-out;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: static;
                height: auto;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .circuit-container {
                flex-direction: column;
            }
            
            .component-palette, .properties-panel {
                width: 100%;
                height: auto;
                max-height: 200px;
                overflow-y: auto;
            }
            
            .circuit-canvas {
                width: 100%;
                height: 300px;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen flex flex-col">
    <!-- Header with language switcher -->
    <header class="bg-gray-800 shadow-lg">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                    </svg>
                </div>
                <h1 class="text-xl md:text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent" data-lang-key="title">Biomedical Instrumentation & Circuit Design Lab</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button id="langToggle" class="lang-switch px-3 py-1 bg-gray-700 rounded-md hover:bg-gray-600 transition flex items-center">
                    <span id="currentLang" class="font-medium">EN</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                </button>
                <button id="themeToggle" class="p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Main content area -->
    <div class="flex flex-1">
        <!-- Sidebar navigation -->
        <aside class="sidebar w-64 bg-gray-800 shadow-md hidden md:block">
            <nav class="p-4">
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-2 text-blue-400" data-lang-key="navigation">Navigation</h2>
                    <ul class="space-y-1">
                        <li>
                            <button id="homeBtn" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                <span data-lang-key="home">Home</span>
                            </button>
                        </li>
                        <li>
                            <button id="modulesBtn" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                <span data-lang-key="modules">Modules</span>
                            </button>
                        </li>
                        <li>
                            <button id="circuitDesignBtn" class="w-full text-left px-3 py-2 rounded bg-gray-700 hover:bg-gray-600 transition flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                </svg>
                                <span data-lang-key="circuitDesign">Circuit Design</span>
                            </button>
                        </li>
                        <li>
                            <button id="simulationsBtn" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 010-7.072m12.728 0l-4.242 4.242m-8.486 0l4.242-4.242" />
                                </svg>
                                <span data-lang-key="simulations">Simulations</span>
                            </button>
                        </li>
                        <li>
                            <button id="resourcesBtn" class="w-full text-left px-3 py-2 rounded hover:bg-gray-700 transition flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                                <span data-lang-key="resources">Resources</span>
                            </button>
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h2 class="text-lg font-semibold mb-2 text-blue-400" data-lang-key="difficulty">Difficulty Level</h2>
                    <div class="space-y-1">
                        <label class="flex items-center space-x-2 px-3 py-1 rounded hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-4 w-4 text-blue-500 rounded focus:ring-blue-500" checked id="beginnerFilter">
                            <span data-lang-key="beginner">Beginner</span>
                        </label>
                        <label class="flex items-center space-x-2 px-3 py-1 rounded hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-4 w-4 text-green-500 rounded focus:ring-green-500" checked id="intermediateFilter">
                            <span data-lang-key="intermediate">Intermediate</span>
                        </label>
                        <label class="flex items-center space-x-2 px-3 py-1 rounded hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-4 w-4 text-red-500 rounded focus:ring-red-500" checked id="advancedFilter">
                            <span data-lang-key="advanced">Advanced</span>
                        </label>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Mobile menu button -->
        <button id="mobileMenuBtn" class="md:hidden fixed bottom-4 right-4 z-50 bg-blue-600 p-3 rounded-full shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
        </button>

        <!-- Mobile menu -->
        <div id="mobileMenu" class="fixed inset-0 bg-gray-900 bg-opacity-90 z-40 hidden md:hidden">
            <div class="flex justify-end p-4">
                <button id="closeMobileMenu" class="text-gray-300 hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex flex-col items-center justify-center h-full p-4">
                <nav class="w-full max-w-md">
                    <ul class="space-y-6 text-center">
                        <li>
                            <button id="mobileHomeBtn" class="text-xl font-medium px-4 py-2 rounded-lg hover:bg-gray-800 transition w-full" data-lang-key="home">Home</button>
                        </li>
                        <li>
                            <button id="mobileModulesBtn" class="text-xl font-medium px-4 py-2 rounded-lg hover:bg-gray-800 transition w-full" data-lang-key="modules">Modules</button>
                        </li>
                        <li>
                            <button id="mobileCircuitDesignBtn" class="text-xl font-medium px-4 py-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition w-full" data-lang-key="circuitDesign">Circuit Design</button>
                        </li>
                        <li>
                            <button id="mobileSimulationsBtn" class="text-xl font-medium px-4 py-2 rounded-lg hover:bg-gray-800 transition w-full" data-lang-key="simulations">Simulations</button>
                        </li>
                        <li>
                            <button id="mobileResourcesBtn" class="text-xl font-medium px-4 py-2 rounded-lg hover:bg-gray-800 transition w-full" data-lang-key="resources">Resources</button>
                        </li>
                    </ul>
                </nav>
                
                <div class="mt-8 w-full max-w-md">
                    <h3 class="text-lg font-semibold mb-3 text-blue-400" data-lang-key="difficulty">Difficulty Level</h3>
                    <div class="flex justify-center space-x-4">
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-blue-500 rounded focus:ring-blue-500" checked id="mobileBeginnerFilter">
                            <span data-lang-key="beginner">Beginner</span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-green-500 rounded focus:ring-green-500" checked id="mobileIntermediateFilter">
                            <span data-lang-key="intermediate">Intermediate</span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-red-500 rounded focus:ring-red-500" checked id="mobileAdvancedFilter">
                            <span data-lang-key="advanced">Advanced</span>
                        </label>
                    </div>
                </div>
                
                <div class="mt-8">
                    <button id="mobileLangToggle" class="px-4 py-2 bg-gray-800 rounded-lg hover:bg-gray-700 transition flex items-center">
                        <span id="mobileCurrentLang" class="font-medium">EN</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <main class="main-content flex-1 p-4 md:p-6">
            <!-- Circuit Design Workbench -->
            <div id="circuitDesignContent" class="fade-in">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold mb-2 text-blue-400" data-lang-key="circuitDesignWorkbench">Circuit Design Workbench</h2>
                    <p class="text-gray-300" data-lang-key="circuitDesignDescription">
                        Build, simulate, and analyze biomedical circuits. Drag components from the palette, connect them with wires, and run simulations to see the results.
                    </p>
                </div>
                
                <!-- Circuit modules selection -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-3 text-blue-400" data-lang-key="circuitModules">Circuit Modules</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Module 9: Inverting Amplifier -->
                        <div class="module-card bg-gray-800 rounded-lg p-4 border-l-4 border-blue-500 hover:border-blue-400 transition">
                            <h4 class="font-bold text-lg mb-2" data-lang-key="module9Title">Module 9: Inverting Amplifier</h4>
                            <p class="text-gray-300 text-sm mb-3" data-lang-key="module9Desc">Build an op-amp circuit to amplify a weak biomedical signal.</p>
                            <div class="flex justify-between items-center">
                                <span class="px-2 py-1 bg-blue-900 text-blue-100 text-xs rounded-full" data-lang-key="intermediate">Intermediate</span>
                                <button class="load-module-btn px-3 py-1 bg-blue-600 hover:bg-blue-500 rounded text-sm transition ripple" data-module="9" data-lang-key="loadModule">Load</button>
                            </div>
                        </div>
                        
                        <!-- Module 10: Active Low-Pass Filter -->
                        <div class="module-card bg-gray-800 rounded-lg p-4 border-l-4 border-red-500 hover:border-red-400 transition">
                            <h4 class="font-bold text-lg mb-2" data-lang-key="module10Title">Module 10: Active Low-Pass Filter</h4>
                            <p class="text-gray-300 text-sm mb-3" data-lang-key="module10Desc">Design a filter to remove noise from biomedical signals.</p>
                            <div class="flex justify-between items-center">
                                <span class="px-2 py-1 bg-red-900 text-red-100 text-xs rounded-full" data-lang-key="advanced">Advanced</span>
                                <button class="load-module-btn px-3 py-1 bg-blue-600 hover:bg-blue-500 rounded text-sm transition ripple" data-module="10" data-lang-key="loadModule">Load</button>
                            </div>
                        </div>
                        
                        <!-- Module 11: Instrumentation Amplifier -->
                        <div class="module-card bg-gray-800 rounded-lg p-4 border-l-4 border-red-500 hover:border-red-400 transition">
                            <h4 class="font-bold text-lg mb-2" data-lang-key="module11Title">Module 11: Instrumentation Amplifier</h4>
                            <p class="text-gray-300 text-sm mb-3" data-lang-key="module11Desc">Build the front-end amplifier used in ECG machines.</p>
                            <div class="flex justify-between items-center">
                                <span class="px-2 py-1 bg-red-900 text-red-100 text-xs rounded-full" data-lang-key="advanced">Advanced</span>
                                <button class="load-module-btn px-3 py-1 bg-blue-600 hover:bg-blue-500 rounded text-sm transition ripple" data-module="11" data-lang-key="loadModule">Load</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Circuit workbench -->
                <div class="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                    <!-- Module instructions panel -->
                    <div id="moduleInstructions" class="bg-gray-700 p-4 border-b border-gray-600">
                        <h3 class="font-semibold text-lg mb-2 text-blue-400" data-lang-key="instructions">Instructions</h3>
                        <p id="moduleTaskText" class="text-gray-300" data-lang-key="selectModulePrompt">Please select a module from above to begin.</p>
                    </div>
                    
                    <!-- Circuit control panel -->
                    <div class="bg-gray-800 p-3 border-b border-gray-700 flex flex-wrap items-center justify-between gap-2">
                        <div class="flex space-x-2">
                            <button id="runSimulationBtn" class="px-3 py-1 bg-green-600 hover:bg-green-500 rounded flex items-center ripple" disabled data-lang-key="runSimulation">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>Run</span>
                            </button>
                            <button id="stopSimulationBtn" class="px-3 py-1 bg-red-600 hover:bg-red-500 rounded flex items-center ripple" disabled data-lang-key="stopSimulation">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                                </svg>
                                <span>Stop</span>
                            </button>
                            <button id="resetCanvasBtn" class="px-3 py-1 bg-yellow-600 hover:bg-yellow-500 rounded flex items-center ripple" data-lang-key="resetCanvas">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                <span>Reset</span>
                            </button>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button id="wiringModeBtn" class="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded flex items-center ripple" data-lang-key="wiringMode">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                </svg>
                                <span>Wiring Mode</span>
                            </button>
                            <button id="checkCircuitBtn" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 rounded flex items-center ripple" disabled data-lang-key="checkCircuit">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>Check Circuit</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Circuit container -->
                    <div class="circuit-container flex flex-col md:flex-row h-full">
                        <!-- Component palette -->
                        <div class="component-palette w-full md:w-64 bg-gray-700 p-3 overflow-y-auto border-r border-gray-600">
                            <h4 class="
</body>
</html>