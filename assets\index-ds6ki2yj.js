var Xm=Object.defineProperty;var Qm=(t,e,n)=>e in t?Xm(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var P=(t,e,n)=>Qm(t,typeof e!="symbol"?e+"":e,n);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const r of s)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&i(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const r={};return s.integrity&&(r.integrity=s.integrity),s.referrerPolicy&&(r.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?r.credentials="include":s.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function i(s){if(s.ep)return;s.ep=!0;const r=n(s);fetch(s.href,r)}})();var Lh={exports:{}},So={},Dh={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rs=Symbol.for("react.element"),qm=Symbol.for("react.portal"),Zm=Symbol.for("react.fragment"),Jm=Symbol.for("react.strict_mode"),eg=Symbol.for("react.profiler"),tg=Symbol.for("react.provider"),ng=Symbol.for("react.context"),ig=Symbol.for("react.forward_ref"),sg=Symbol.for("react.suspense"),rg=Symbol.for("react.memo"),og=Symbol.for("react.lazy"),Zc=Symbol.iterator;function lg(t){return t===null||typeof t!="object"?null:(t=Zc&&t[Zc]||t["@@iterator"],typeof t=="function"?t:null)}var Th={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ah=Object.assign,Oh={};function Si(t,e,n){this.props=t,this.context=e,this.refs=Oh,this.updater=n||Th}Si.prototype.isReactComponent={};Si.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Si.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function Rh(){}Rh.prototype=Si.prototype;function Va(t,e,n){this.props=t,this.context=e,this.refs=Oh,this.updater=n||Th}var Ha=Va.prototype=new Rh;Ha.constructor=Va;Ah(Ha,Si.prototype);Ha.isPureReactComponent=!0;var Jc=Array.isArray,Ih=Object.prototype.hasOwnProperty,Wa={current:null},zh={key:!0,ref:!0,__self:!0,__source:!0};function Fh(t,e,n){var i,s={},r=null,o=null;if(e!=null)for(i in e.ref!==void 0&&(o=e.ref),e.key!==void 0&&(r=""+e.key),e)Ih.call(e,i)&&!zh.hasOwnProperty(i)&&(s[i]=e[i]);var l=arguments.length-2;if(l===1)s.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];s.children=a}if(t&&t.defaultProps)for(i in l=t.defaultProps,l)s[i]===void 0&&(s[i]=l[i]);return{$$typeof:Rs,type:t,key:r,ref:o,props:s,_owner:Wa.current}}function ag(t,e){return{$$typeof:Rs,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function $a(t){return typeof t=="object"&&t!==null&&t.$$typeof===Rs}function cg(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var eu=/\/+/g;function Ko(t,e){return typeof t=="object"&&t!==null&&t.key!=null?cg(""+t.key):e.toString(36)}function wr(t,e,n,i,s){var r=typeof t;(r==="undefined"||r==="boolean")&&(t=null);var o=!1;if(t===null)o=!0;else switch(r){case"string":case"number":o=!0;break;case"object":switch(t.$$typeof){case Rs:case qm:o=!0}}if(o)return o=t,s=s(o),t=i===""?"."+Ko(o,0):i,Jc(s)?(n="",t!=null&&(n=t.replace(eu,"$&/")+"/"),wr(s,e,n,"",function(u){return u})):s!=null&&($a(s)&&(s=ag(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(eu,"$&/")+"/")+t)),e.push(s)),1;if(o=0,i=i===""?".":i+":",Jc(t))for(var l=0;l<t.length;l++){r=t[l];var a=i+Ko(r,l);o+=wr(r,e,n,a,s)}else if(a=lg(t),typeof a=="function")for(t=a.call(t),l=0;!(r=t.next()).done;)r=r.value,a=i+Ko(r,l++),o+=wr(r,e,n,a,s);else if(r==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return o}function Us(t,e,n){if(t==null)return t;var i=[],s=0;return wr(t,i,"","",function(r){return e.call(n,r,s++)}),i}function ug(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var Re={current:null},kr={transition:null},dg={ReactCurrentDispatcher:Re,ReactCurrentBatchConfig:kr,ReactCurrentOwner:Wa};function Bh(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:Us,forEach:function(t,e,n){Us(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return Us(t,function(){e++}),e},toArray:function(t){return Us(t,function(e){return e})||[]},only:function(t){if(!$a(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};F.Component=Si;F.Fragment=Zm;F.Profiler=eg;F.PureComponent=Va;F.StrictMode=Jm;F.Suspense=sg;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=dg;F.act=Bh;F.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var i=Ah({},t.props),s=t.key,r=t.ref,o=t._owner;if(e!=null){if(e.ref!==void 0&&(r=e.ref,o=Wa.current),e.key!==void 0&&(s=""+e.key),t.type&&t.type.defaultProps)var l=t.type.defaultProps;for(a in e)Ih.call(e,a)&&!zh.hasOwnProperty(a)&&(i[a]=e[a]===void 0&&l!==void 0?l[a]:e[a])}var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}return{$$typeof:Rs,type:t.type,key:s,ref:r,props:i,_owner:o}};F.createContext=function(t){return t={$$typeof:ng,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:tg,_context:t},t.Consumer=t};F.createElement=Fh;F.createFactory=function(t){var e=Fh.bind(null,t);return e.type=t,e};F.createRef=function(){return{current:null}};F.forwardRef=function(t){return{$$typeof:ig,render:t}};F.isValidElement=$a;F.lazy=function(t){return{$$typeof:og,_payload:{_status:-1,_result:t},_init:ug}};F.memo=function(t,e){return{$$typeof:rg,type:t,compare:e===void 0?null:e}};F.startTransition=function(t){var e=kr.transition;kr.transition={};try{t()}finally{kr.transition=e}};F.unstable_act=Bh;F.useCallback=function(t,e){return Re.current.useCallback(t,e)};F.useContext=function(t){return Re.current.useContext(t)};F.useDebugValue=function(){};F.useDeferredValue=function(t){return Re.current.useDeferredValue(t)};F.useEffect=function(t,e){return Re.current.useEffect(t,e)};F.useId=function(){return Re.current.useId()};F.useImperativeHandle=function(t,e,n){return Re.current.useImperativeHandle(t,e,n)};F.useInsertionEffect=function(t,e){return Re.current.useInsertionEffect(t,e)};F.useLayoutEffect=function(t,e){return Re.current.useLayoutEffect(t,e)};F.useMemo=function(t,e){return Re.current.useMemo(t,e)};F.useReducer=function(t,e,n){return Re.current.useReducer(t,e,n)};F.useRef=function(t){return Re.current.useRef(t)};F.useState=function(t){return Re.current.useState(t)};F.useSyncExternalStore=function(t,e,n){return Re.current.useSyncExternalStore(t,e,n)};F.useTransition=function(){return Re.current.useTransition()};F.version="18.3.1";Dh.exports=F;var T=Dh.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hg=T,fg=Symbol.for("react.element"),pg=Symbol.for("react.fragment"),mg=Object.prototype.hasOwnProperty,gg=hg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,xg={key:!0,ref:!0,__self:!0,__source:!0};function Vh(t,e,n){var i,s={},r=null,o=null;n!==void 0&&(r=""+n),e.key!==void 0&&(r=""+e.key),e.ref!==void 0&&(o=e.ref);for(i in e)mg.call(e,i)&&!xg.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps,e)s[i]===void 0&&(s[i]=e[i]);return{$$typeof:fg,type:t,key:r,ref:o,props:s,_owner:gg.current}}So.Fragment=pg;So.jsx=Vh;So.jsxs=Vh;Lh.exports=So;var c=Lh.exports,Hh={exports:{}},qe={},Wh={exports:{}},$h={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(E,D){var O=E.length;E.push(D);e:for(;0<O;){var Y=O-1>>>1,q=E[Y];if(0<s(q,D))E[Y]=D,E[O]=q,O=Y;else break e}}function n(E){return E.length===0?null:E[0]}function i(E){if(E.length===0)return null;var D=E[0],O=E.pop();if(O!==D){E[0]=O;e:for(var Y=0,q=E.length,xt=q>>>1;Y<xt;){var De=2*(Y+1)-1,Nt=E[De],Te=De+1,$s=E[Te];if(0>s(Nt,O))Te<q&&0>s($s,Nt)?(E[Y]=$s,E[Te]=O,Y=Te):(E[Y]=Nt,E[De]=O,Y=De);else if(Te<q&&0>s($s,O))E[Y]=$s,E[Te]=O,Y=Te;else break e}}return D}function s(E,D){var O=E.sortIndex-D.sortIndex;return O!==0?O:E.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var r=performance;t.unstable_now=function(){return r.now()}}else{var o=Date,l=o.now();t.unstable_now=function(){return o.now()-l}}var a=[],u=[],d=1,h=null,f=3,p=!1,g=!1,x=!1,b=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,y=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(E){for(var D=n(u);D!==null;){if(D.callback===null)i(u);else if(D.startTime<=E)i(u),D.sortIndex=D.expirationTime,e(a,D);else break;D=n(u)}}function _(E){if(x=!1,v(E),!g)if(n(a)!==null)g=!0,K(w);else{var D=n(u);D!==null&&Q(_,D.startTime-E)}}function w(E,D){g=!1,x&&(x=!1,m(S),S=-1),p=!0;var O=f;try{for(v(D),h=n(a);h!==null&&(!(h.expirationTime>D)||E&&!A());){var Y=h.callback;if(typeof Y=="function"){h.callback=null,f=h.priorityLevel;var q=Y(h.expirationTime<=D);D=t.unstable_now(),typeof q=="function"?h.callback=q:h===n(a)&&i(a),v(D)}else i(a);h=n(a)}if(h!==null)var xt=!0;else{var De=n(u);De!==null&&Q(_,De.startTime-D),xt=!1}return xt}finally{h=null,f=O,p=!1}}var j=!1,k=null,S=-1,C=5,M=-1;function A(){return!(t.unstable_now()-M<C)}function I(){if(k!==null){var E=t.unstable_now();M=E;var D=!0;try{D=k(!0,E)}finally{D?se():(j=!1,k=null)}}else j=!1}var se;if(typeof y=="function")se=function(){y(I)};else if(typeof MessageChannel<"u"){var ke=new MessageChannel,W=ke.port2;ke.port1.onmessage=I,se=function(){W.postMessage(null)}}else se=function(){b(I,0)};function K(E){k=E,j||(j=!0,se())}function Q(E,D){S=b(function(){E(t.unstable_now())},D)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(E){E.callback=null},t.unstable_continueExecution=function(){g||p||(g=!0,K(w))},t.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<E?Math.floor(1e3/E):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(a)},t.unstable_next=function(E){switch(f){case 1:case 2:case 3:var D=3;break;default:D=f}var O=f;f=D;try{return E()}finally{f=O}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(E,D){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var O=f;f=E;try{return D()}finally{f=O}},t.unstable_scheduleCallback=function(E,D,O){var Y=t.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?Y+O:Y):O=Y,E){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=O+q,E={id:d++,callback:D,priorityLevel:E,startTime:O,expirationTime:q,sortIndex:-1},O>Y?(E.sortIndex=O,e(u,E),n(a)===null&&E===n(u)&&(x?(m(S),S=-1):x=!0,Q(_,O-Y))):(E.sortIndex=q,e(a,E),g||p||(g=!0,K(w))),E},t.unstable_shouldYield=A,t.unstable_wrapCallback=function(E){var D=f;return function(){var O=f;f=D;try{return E.apply(this,arguments)}finally{f=O}}}})($h);Wh.exports=$h;var yg=Wh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vg=T,Qe=yg;function N(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Uh=new Set,us={};function Hn(t,e){pi(t,e),pi(t+"Capture",e)}function pi(t,e){for(us[t]=e,t=0;t<e.length;t++)Uh.add(e[t])}var Ft=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Dl=Object.prototype.hasOwnProperty,bg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,tu={},nu={};function _g(t){return Dl.call(nu,t)?!0:Dl.call(tu,t)?!1:bg.test(t)?nu[t]=!0:(tu[t]=!0,!1)}function wg(t,e,n,i){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return i?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function kg(t,e,n,i){if(e===null||typeof e>"u"||wg(t,e,n,i))return!0;if(i)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Ie(t,e,n,i,s,r,o){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=i,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=r,this.removeEmptyString=o}var we={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){we[t]=new Ie(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];we[e]=new Ie(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){we[t]=new Ie(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){we[t]=new Ie(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){we[t]=new Ie(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){we[t]=new Ie(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){we[t]=new Ie(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){we[t]=new Ie(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){we[t]=new Ie(t,5,!1,t.toLowerCase(),null,!1,!1)});var Ua=/[\-:]([a-z])/g;function Ka(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(Ua,Ka);we[e]=new Ie(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(Ua,Ka);we[e]=new Ie(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(Ua,Ka);we[e]=new Ie(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){we[t]=new Ie(t,1,!1,t.toLowerCase(),null,!1,!1)});we.xlinkHref=new Ie("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){we[t]=new Ie(t,1,!1,t.toLowerCase(),null,!0,!0)});function Ya(t,e,n,i){var s=we.hasOwnProperty(e)?we[e]:null;(s!==null?s.type!==0:i||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(kg(e,n,s,i)&&(n=null),i||s===null?_g(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):s.mustUseProperty?t[s.propertyName]=n===null?s.type===3?!1:"":n:(e=s.attributeName,i=s.attributeNamespace,n===null?t.removeAttribute(e):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,i?t.setAttributeNS(i,e,n):t.setAttribute(e,n))))}var Wt=vg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ks=Symbol.for("react.element"),Xn=Symbol.for("react.portal"),Qn=Symbol.for("react.fragment"),Ga=Symbol.for("react.strict_mode"),Tl=Symbol.for("react.profiler"),Kh=Symbol.for("react.provider"),Yh=Symbol.for("react.context"),Xa=Symbol.for("react.forward_ref"),Al=Symbol.for("react.suspense"),Ol=Symbol.for("react.suspense_list"),Qa=Symbol.for("react.memo"),Ut=Symbol.for("react.lazy"),Gh=Symbol.for("react.offscreen"),iu=Symbol.iterator;function Mi(t){return t===null||typeof t!="object"?null:(t=iu&&t[iu]||t["@@iterator"],typeof t=="function"?t:null)}var le=Object.assign,Yo;function Bi(t){if(Yo===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Yo=e&&e[1]||""}return`
`+Yo+t}var Go=!1;function Xo(t,e){if(!t||Go)return"";Go=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var i=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){i=u}t.call(e.prototype)}else{try{throw Error()}catch(u){i=u}t()}}catch(u){if(u&&i&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),r=i.stack.split(`
`),o=s.length-1,l=r.length-1;1<=o&&0<=l&&s[o]!==r[l];)l--;for(;1<=o&&0<=l;o--,l--)if(s[o]!==r[l]){if(o!==1||l!==1)do if(o--,l--,0>l||s[o]!==r[l]){var a=`
`+s[o].replace(" at new "," at ");return t.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",t.displayName)),a}while(1<=o&&0<=l);break}}}finally{Go=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?Bi(t):""}function Sg(t){switch(t.tag){case 5:return Bi(t.type);case 16:return Bi("Lazy");case 13:return Bi("Suspense");case 19:return Bi("SuspenseList");case 0:case 2:case 15:return t=Xo(t.type,!1),t;case 11:return t=Xo(t.type.render,!1),t;case 1:return t=Xo(t.type,!0),t;default:return""}}function Rl(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Qn:return"Fragment";case Xn:return"Portal";case Tl:return"Profiler";case Ga:return"StrictMode";case Al:return"Suspense";case Ol:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case Yh:return(t.displayName||"Context")+".Consumer";case Kh:return(t._context.displayName||"Context")+".Provider";case Xa:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Qa:return e=t.displayName||null,e!==null?e:Rl(t.type)||"Memo";case Ut:e=t._payload,t=t._init;try{return Rl(t(e))}catch{}}return null}function jg(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Rl(e);case 8:return e===Ga?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function dn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Xh(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Ng(t){var e=Xh(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(o){i=""+o,r.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(o){i=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Ys(t){t._valueTracker||(t._valueTracker=Ng(t))}function Qh(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),i="";return t&&(i=Xh(t)?t.checked?"true":"false":t.value),t=i,t!==n?(e.setValue(t),!0):!1}function Wr(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function Il(t,e){var n=e.checked;return le({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function su(t,e){var n=e.defaultValue==null?"":e.defaultValue,i=e.checked!=null?e.checked:e.defaultChecked;n=dn(e.value!=null?e.value:n),t._wrapperState={initialChecked:i,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function qh(t,e){e=e.checked,e!=null&&Ya(t,"checked",e,!1)}function zl(t,e){qh(t,e);var n=dn(e.value),i=e.type;if(n!=null)i==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(i==="submit"||i==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?Fl(t,e.type,n):e.hasOwnProperty("defaultValue")&&Fl(t,e.type,dn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function ru(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var i=e.type;if(!(i!=="submit"&&i!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function Fl(t,e,n){(e!=="number"||Wr(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var Vi=Array.isArray;function li(t,e,n,i){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&i&&(t[n].defaultSelected=!0)}else{for(n=""+dn(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,i&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function Bl(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(N(91));return le({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function ou(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(N(92));if(Vi(n)){if(1<n.length)throw Error(N(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:dn(n)}}function Zh(t,e){var n=dn(e.value),i=dn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),i!=null&&(t.defaultValue=""+i)}function lu(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function Jh(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Vl(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?Jh(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Gs,ef=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,i,s){MSApp.execUnsafeLocalFunction(function(){return t(e,n,i,s)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(Gs=Gs||document.createElement("div"),Gs.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=Gs.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function ds(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Qi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Mg=["Webkit","ms","Moz","O"];Object.keys(Qi).forEach(function(t){Mg.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),Qi[e]=Qi[t]})});function tf(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||Qi.hasOwnProperty(t)&&Qi[t]?(""+e).trim():e+"px"}function nf(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var i=n.indexOf("--")===0,s=tf(n,e[n],i);n==="float"&&(n="cssFloat"),i?t.setProperty(n,s):t[n]=s}}var Cg=le({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Hl(t,e){if(e){if(Cg[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(N(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(N(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(N(61))}if(e.style!=null&&typeof e.style!="object")throw Error(N(62))}}function Wl(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var $l=null;function qa(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ul=null,ai=null,ci=null;function au(t){if(t=Fs(t)){if(typeof Ul!="function")throw Error(N(280));var e=t.stateNode;e&&(e=Po(e),Ul(t.stateNode,t.type,e))}}function sf(t){ai?ci?ci.push(t):ci=[t]:ai=t}function rf(){if(ai){var t=ai,e=ci;if(ci=ai=null,au(t),e)for(t=0;t<e.length;t++)au(e[t])}}function of(t,e){return t(e)}function lf(){}var Qo=!1;function af(t,e,n){if(Qo)return t(e,n);Qo=!0;try{return of(t,e,n)}finally{Qo=!1,(ai!==null||ci!==null)&&(lf(),rf())}}function hs(t,e){var n=t.stateNode;if(n===null)return null;var i=Po(n);if(i===null)return null;n=i[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(N(231,e,typeof n));return n}var Kl=!1;if(Ft)try{var Ci={};Object.defineProperty(Ci,"passive",{get:function(){Kl=!0}}),window.addEventListener("test",Ci,Ci),window.removeEventListener("test",Ci,Ci)}catch{Kl=!1}function Pg(t,e,n,i,s,r,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(d){this.onError(d)}}var qi=!1,$r=null,Ur=!1,Yl=null,Eg={onError:function(t){qi=!0,$r=t}};function Lg(t,e,n,i,s,r,o,l,a){qi=!1,$r=null,Pg.apply(Eg,arguments)}function Dg(t,e,n,i,s,r,o,l,a){if(Lg.apply(this,arguments),qi){if(qi){var u=$r;qi=!1,$r=null}else throw Error(N(198));Ur||(Ur=!0,Yl=u)}}function Wn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function cf(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function cu(t){if(Wn(t)!==t)throw Error(N(188))}function Tg(t){var e=t.alternate;if(!e){if(e=Wn(t),e===null)throw Error(N(188));return e!==t?null:t}for(var n=t,i=e;;){var s=n.return;if(s===null)break;var r=s.alternate;if(r===null){if(i=s.return,i!==null){n=i;continue}break}if(s.child===r.child){for(r=s.child;r;){if(r===n)return cu(s),t;if(r===i)return cu(s),e;r=r.sibling}throw Error(N(188))}if(n.return!==i.return)n=s,i=r;else{for(var o=!1,l=s.child;l;){if(l===n){o=!0,n=s,i=r;break}if(l===i){o=!0,i=s,n=r;break}l=l.sibling}if(!o){for(l=r.child;l;){if(l===n){o=!0,n=r,i=s;break}if(l===i){o=!0,i=r,n=s;break}l=l.sibling}if(!o)throw Error(N(189))}}if(n.alternate!==i)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?t:e}function uf(t){return t=Tg(t),t!==null?df(t):null}function df(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=df(t);if(e!==null)return e;t=t.sibling}return null}var hf=Qe.unstable_scheduleCallback,uu=Qe.unstable_cancelCallback,Ag=Qe.unstable_shouldYield,Og=Qe.unstable_requestPaint,de=Qe.unstable_now,Rg=Qe.unstable_getCurrentPriorityLevel,Za=Qe.unstable_ImmediatePriority,ff=Qe.unstable_UserBlockingPriority,Kr=Qe.unstable_NormalPriority,Ig=Qe.unstable_LowPriority,pf=Qe.unstable_IdlePriority,jo=null,kt=null;function zg(t){if(kt&&typeof kt.onCommitFiberRoot=="function")try{kt.onCommitFiberRoot(jo,t,void 0,(t.current.flags&128)===128)}catch{}}var ht=Math.clz32?Math.clz32:Vg,Fg=Math.log,Bg=Math.LN2;function Vg(t){return t>>>=0,t===0?32:31-(Fg(t)/Bg|0)|0}var Xs=64,Qs=4194304;function Hi(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function Yr(t,e){var n=t.pendingLanes;if(n===0)return 0;var i=0,s=t.suspendedLanes,r=t.pingedLanes,o=n&268435455;if(o!==0){var l=o&~s;l!==0?i=Hi(l):(r&=o,r!==0&&(i=Hi(r)))}else o=n&~s,o!==0?i=Hi(o):r!==0&&(i=Hi(r));if(i===0)return 0;if(e!==0&&e!==i&&!(e&s)&&(s=i&-i,r=e&-e,s>=r||s===16&&(r&4194240)!==0))return e;if(i&4&&(i|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=i;0<e;)n=31-ht(e),s=1<<n,i|=t[n],e&=~s;return i}function Hg(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Wg(t,e){for(var n=t.suspendedLanes,i=t.pingedLanes,s=t.expirationTimes,r=t.pendingLanes;0<r;){var o=31-ht(r),l=1<<o,a=s[o];a===-1?(!(l&n)||l&i)&&(s[o]=Hg(l,e)):a<=e&&(t.expiredLanes|=l),r&=~l}}function Gl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function mf(){var t=Xs;return Xs<<=1,!(Xs&4194240)&&(Xs=64),t}function qo(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Is(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-ht(e),t[e]=n}function $g(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var i=t.eventTimes;for(t=t.expirationTimes;0<n;){var s=31-ht(n),r=1<<s;e[s]=0,i[s]=-1,t[s]=-1,n&=~r}}function Ja(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var i=31-ht(n),s=1<<i;s&e|t[i]&e&&(t[i]|=e),n&=~s}}var U=0;function gf(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var xf,ec,yf,vf,bf,Xl=!1,qs=[],tn=null,nn=null,sn=null,fs=new Map,ps=new Map,Yt=[],Ug="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function du(t,e){switch(t){case"focusin":case"focusout":tn=null;break;case"dragenter":case"dragleave":nn=null;break;case"mouseover":case"mouseout":sn=null;break;case"pointerover":case"pointerout":fs.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":ps.delete(e.pointerId)}}function Pi(t,e,n,i,s,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:i,nativeEvent:r,targetContainers:[s]},e!==null&&(e=Fs(e),e!==null&&ec(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function Kg(t,e,n,i,s){switch(e){case"focusin":return tn=Pi(tn,t,e,n,i,s),!0;case"dragenter":return nn=Pi(nn,t,e,n,i,s),!0;case"mouseover":return sn=Pi(sn,t,e,n,i,s),!0;case"pointerover":var r=s.pointerId;return fs.set(r,Pi(fs.get(r)||null,t,e,n,i,s)),!0;case"gotpointercapture":return r=s.pointerId,ps.set(r,Pi(ps.get(r)||null,t,e,n,i,s)),!0}return!1}function _f(t){var e=Mn(t.target);if(e!==null){var n=Wn(e);if(n!==null){if(e=n.tag,e===13){if(e=cf(n),e!==null){t.blockedOn=e,bf(t.priority,function(){yf(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Sr(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Ql(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var i=new n.constructor(n.type,n);$l=i,n.target.dispatchEvent(i),$l=null}else return e=Fs(n),e!==null&&ec(e),t.blockedOn=n,!1;e.shift()}return!0}function hu(t,e,n){Sr(t)&&n.delete(e)}function Yg(){Xl=!1,tn!==null&&Sr(tn)&&(tn=null),nn!==null&&Sr(nn)&&(nn=null),sn!==null&&Sr(sn)&&(sn=null),fs.forEach(hu),ps.forEach(hu)}function Ei(t,e){t.blockedOn===e&&(t.blockedOn=null,Xl||(Xl=!0,Qe.unstable_scheduleCallback(Qe.unstable_NormalPriority,Yg)))}function ms(t){function e(s){return Ei(s,t)}if(0<qs.length){Ei(qs[0],t);for(var n=1;n<qs.length;n++){var i=qs[n];i.blockedOn===t&&(i.blockedOn=null)}}for(tn!==null&&Ei(tn,t),nn!==null&&Ei(nn,t),sn!==null&&Ei(sn,t),fs.forEach(e),ps.forEach(e),n=0;n<Yt.length;n++)i=Yt[n],i.blockedOn===t&&(i.blockedOn=null);for(;0<Yt.length&&(n=Yt[0],n.blockedOn===null);)_f(n),n.blockedOn===null&&Yt.shift()}var ui=Wt.ReactCurrentBatchConfig,Gr=!0;function Gg(t,e,n,i){var s=U,r=ui.transition;ui.transition=null;try{U=1,tc(t,e,n,i)}finally{U=s,ui.transition=r}}function Xg(t,e,n,i){var s=U,r=ui.transition;ui.transition=null;try{U=4,tc(t,e,n,i)}finally{U=s,ui.transition=r}}function tc(t,e,n,i){if(Gr){var s=Ql(t,e,n,i);if(s===null)ll(t,e,i,Xr,n),du(t,i);else if(Kg(s,t,e,n,i))i.stopPropagation();else if(du(t,i),e&4&&-1<Ug.indexOf(t)){for(;s!==null;){var r=Fs(s);if(r!==null&&xf(r),r=Ql(t,e,n,i),r===null&&ll(t,e,i,Xr,n),r===s)break;s=r}s!==null&&i.stopPropagation()}else ll(t,e,i,null,n)}}var Xr=null;function Ql(t,e,n,i){if(Xr=null,t=qa(i),t=Mn(t),t!==null)if(e=Wn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=cf(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return Xr=t,null}function wf(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Rg()){case Za:return 1;case ff:return 4;case Kr:case Ig:return 16;case pf:return 536870912;default:return 16}default:return 16}}var Xt=null,nc=null,jr=null;function kf(){if(jr)return jr;var t,e=nc,n=e.length,i,s="value"in Xt?Xt.value:Xt.textContent,r=s.length;for(t=0;t<n&&e[t]===s[t];t++);var o=n-t;for(i=1;i<=o&&e[n-i]===s[r-i];i++);return jr=s.slice(t,1<i?1-i:void 0)}function Nr(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Zs(){return!0}function fu(){return!1}function Ze(t){function e(n,i,s,r,o){this._reactName=n,this._targetInst=s,this.type=i,this.nativeEvent=r,this.target=o,this.currentTarget=null;for(var l in t)t.hasOwnProperty(l)&&(n=t[l],this[l]=n?n(r):r[l]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Zs:fu,this.isPropagationStopped=fu,this}return le(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Zs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Zs)},persist:function(){},isPersistent:Zs}),e}var ji={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ic=Ze(ji),zs=le({},ji,{view:0,detail:0}),Qg=Ze(zs),Zo,Jo,Li,No=le({},zs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:sc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Li&&(Li&&t.type==="mousemove"?(Zo=t.screenX-Li.screenX,Jo=t.screenY-Li.screenY):Jo=Zo=0,Li=t),Zo)},movementY:function(t){return"movementY"in t?t.movementY:Jo}}),pu=Ze(No),qg=le({},No,{dataTransfer:0}),Zg=Ze(qg),Jg=le({},zs,{relatedTarget:0}),el=Ze(Jg),e0=le({},ji,{animationName:0,elapsedTime:0,pseudoElement:0}),t0=Ze(e0),n0=le({},ji,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),i0=Ze(n0),s0=le({},ji,{data:0}),mu=Ze(s0),r0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},o0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},l0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function a0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=l0[t])?!!e[t]:!1}function sc(){return a0}var c0=le({},zs,{key:function(t){if(t.key){var e=r0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Nr(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?o0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:sc,charCode:function(t){return t.type==="keypress"?Nr(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Nr(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),u0=Ze(c0),d0=le({},No,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),gu=Ze(d0),h0=le({},zs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:sc}),f0=Ze(h0),p0=le({},ji,{propertyName:0,elapsedTime:0,pseudoElement:0}),m0=Ze(p0),g0=le({},No,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),x0=Ze(g0),y0=[9,13,27,32],rc=Ft&&"CompositionEvent"in window,Zi=null;Ft&&"documentMode"in document&&(Zi=document.documentMode);var v0=Ft&&"TextEvent"in window&&!Zi,Sf=Ft&&(!rc||Zi&&8<Zi&&11>=Zi),xu=" ",yu=!1;function jf(t,e){switch(t){case"keyup":return y0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var qn=!1;function b0(t,e){switch(t){case"compositionend":return Nf(e);case"keypress":return e.which!==32?null:(yu=!0,xu);case"textInput":return t=e.data,t===xu&&yu?null:t;default:return null}}function _0(t,e){if(qn)return t==="compositionend"||!rc&&jf(t,e)?(t=kf(),jr=nc=Xt=null,qn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Sf&&e.locale!=="ko"?null:e.data;default:return null}}var w0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function vu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!w0[t.type]:e==="textarea"}function Mf(t,e,n,i){sf(i),e=Qr(e,"onChange"),0<e.length&&(n=new ic("onChange","change",null,n,i),t.push({event:n,listeners:e}))}var Ji=null,gs=null;function k0(t){zf(t,0)}function Mo(t){var e=ei(t);if(Qh(e))return t}function S0(t,e){if(t==="change")return e}var Cf=!1;if(Ft){var tl;if(Ft){var nl="oninput"in document;if(!nl){var bu=document.createElement("div");bu.setAttribute("oninput","return;"),nl=typeof bu.oninput=="function"}tl=nl}else tl=!1;Cf=tl&&(!document.documentMode||9<document.documentMode)}function _u(){Ji&&(Ji.detachEvent("onpropertychange",Pf),gs=Ji=null)}function Pf(t){if(t.propertyName==="value"&&Mo(gs)){var e=[];Mf(e,gs,t,qa(t)),af(k0,e)}}function j0(t,e,n){t==="focusin"?(_u(),Ji=e,gs=n,Ji.attachEvent("onpropertychange",Pf)):t==="focusout"&&_u()}function N0(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Mo(gs)}function M0(t,e){if(t==="click")return Mo(e)}function C0(t,e){if(t==="input"||t==="change")return Mo(e)}function P0(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var mt=typeof Object.is=="function"?Object.is:P0;function xs(t,e){if(mt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),i=Object.keys(e);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var s=n[i];if(!Dl.call(e,s)||!mt(t[s],e[s]))return!1}return!0}function wu(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ku(t,e){var n=wu(t);t=0;for(var i;n;){if(n.nodeType===3){if(i=t+n.textContent.length,t<=e&&i>=e)return{node:n,offset:e-t};t=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wu(n)}}function Ef(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Ef(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Lf(){for(var t=window,e=Wr();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Wr(t.document)}return e}function oc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function E0(t){var e=Lf(),n=t.focusedElem,i=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&Ef(n.ownerDocument.documentElement,n)){if(i!==null&&oc(n)){if(e=i.start,t=i.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var s=n.textContent.length,r=Math.min(i.start,s);i=i.end===void 0?r:Math.min(i.end,s),!t.extend&&r>i&&(s=i,i=r,r=s),s=ku(n,r);var o=ku(n,i);s&&o&&(t.rangeCount!==1||t.anchorNode!==s.node||t.anchorOffset!==s.offset||t.focusNode!==o.node||t.focusOffset!==o.offset)&&(e=e.createRange(),e.setStart(s.node,s.offset),t.removeAllRanges(),r>i?(t.addRange(e),t.extend(o.node,o.offset)):(e.setEnd(o.node,o.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var L0=Ft&&"documentMode"in document&&11>=document.documentMode,Zn=null,ql=null,es=null,Zl=!1;function Su(t,e,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Zl||Zn==null||Zn!==Wr(i)||(i=Zn,"selectionStart"in i&&oc(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),es&&xs(es,i)||(es=i,i=Qr(ql,"onSelect"),0<i.length&&(e=new ic("onSelect","select",null,e,n),t.push({event:e,listeners:i}),e.target=Zn)))}function Js(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Jn={animationend:Js("Animation","AnimationEnd"),animationiteration:Js("Animation","AnimationIteration"),animationstart:Js("Animation","AnimationStart"),transitionend:Js("Transition","TransitionEnd")},il={},Df={};Ft&&(Df=document.createElement("div").style,"AnimationEvent"in window||(delete Jn.animationend.animation,delete Jn.animationiteration.animation,delete Jn.animationstart.animation),"TransitionEvent"in window||delete Jn.transitionend.transition);function Co(t){if(il[t])return il[t];if(!Jn[t])return t;var e=Jn[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Df)return il[t]=e[n];return t}var Tf=Co("animationend"),Af=Co("animationiteration"),Of=Co("animationstart"),Rf=Co("transitionend"),If=new Map,ju="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function mn(t,e){If.set(t,e),Hn(e,[t])}for(var sl=0;sl<ju.length;sl++){var rl=ju[sl],D0=rl.toLowerCase(),T0=rl[0].toUpperCase()+rl.slice(1);mn(D0,"on"+T0)}mn(Tf,"onAnimationEnd");mn(Af,"onAnimationIteration");mn(Of,"onAnimationStart");mn("dblclick","onDoubleClick");mn("focusin","onFocus");mn("focusout","onBlur");mn(Rf,"onTransitionEnd");pi("onMouseEnter",["mouseout","mouseover"]);pi("onMouseLeave",["mouseout","mouseover"]);pi("onPointerEnter",["pointerout","pointerover"]);pi("onPointerLeave",["pointerout","pointerover"]);Hn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Hn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Hn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Hn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Hn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Hn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Wi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),A0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Wi));function Nu(t,e,n){var i=t.type||"unknown-event";t.currentTarget=n,Dg(i,e,void 0,t),t.currentTarget=null}function zf(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var i=t[n],s=i.event;i=i.listeners;e:{var r=void 0;if(e)for(var o=i.length-1;0<=o;o--){var l=i[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==r&&s.isPropagationStopped())break e;Nu(s,l,u),r=a}else for(o=0;o<i.length;o++){if(l=i[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==r&&s.isPropagationStopped())break e;Nu(s,l,u),r=a}}}if(Ur)throw t=Yl,Ur=!1,Yl=null,t}function Z(t,e){var n=e[ia];n===void 0&&(n=e[ia]=new Set);var i=t+"__bubble";n.has(i)||(Ff(e,t,2,!1),n.add(i))}function ol(t,e,n){var i=0;e&&(i|=4),Ff(n,t,i,e)}var er="_reactListening"+Math.random().toString(36).slice(2);function ys(t){if(!t[er]){t[er]=!0,Uh.forEach(function(n){n!=="selectionchange"&&(A0.has(n)||ol(n,!1,t),ol(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[er]||(e[er]=!0,ol("selectionchange",!1,e))}}function Ff(t,e,n,i){switch(wf(e)){case 1:var s=Gg;break;case 4:s=Xg;break;default:s=tc}n=s.bind(null,e,n,t),s=void 0,!Kl||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),i?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function ll(t,e,n,i,s){var r=i;if(!(e&1)&&!(e&2)&&i!==null)e:for(;;){if(i===null)return;var o=i.tag;if(o===3||o===4){var l=i.stateNode.containerInfo;if(l===s||l.nodeType===8&&l.parentNode===s)break;if(o===4)for(o=i.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===s||a.nodeType===8&&a.parentNode===s))return;o=o.return}for(;l!==null;){if(o=Mn(l),o===null)return;if(a=o.tag,a===5||a===6){i=r=o;continue e}l=l.parentNode}}i=i.return}af(function(){var u=r,d=qa(n),h=[];e:{var f=If.get(t);if(f!==void 0){var p=ic,g=t;switch(t){case"keypress":if(Nr(n)===0)break e;case"keydown":case"keyup":p=u0;break;case"focusin":g="focus",p=el;break;case"focusout":g="blur",p=el;break;case"beforeblur":case"afterblur":p=el;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=pu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=Zg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=f0;break;case Tf:case Af:case Of:p=t0;break;case Rf:p=m0;break;case"scroll":p=Qg;break;case"wheel":p=x0;break;case"copy":case"cut":case"paste":p=i0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=gu}var x=(e&4)!==0,b=!x&&t==="scroll",m=x?f!==null?f+"Capture":null:f;x=[];for(var y=u,v;y!==null;){v=y;var _=v.stateNode;if(v.tag===5&&_!==null&&(v=_,m!==null&&(_=hs(y,m),_!=null&&x.push(vs(y,_,v)))),b)break;y=y.return}0<x.length&&(f=new p(f,g,null,n,d),h.push({event:f,listeners:x}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",p=t==="mouseout"||t==="pointerout",f&&n!==$l&&(g=n.relatedTarget||n.fromElement)&&(Mn(g)||g[Bt]))break e;if((p||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,p?(g=n.relatedTarget||n.toElement,p=u,g=g?Mn(g):null,g!==null&&(b=Wn(g),g!==b||g.tag!==5&&g.tag!==6)&&(g=null)):(p=null,g=u),p!==g)){if(x=pu,_="onMouseLeave",m="onMouseEnter",y="mouse",(t==="pointerout"||t==="pointerover")&&(x=gu,_="onPointerLeave",m="onPointerEnter",y="pointer"),b=p==null?f:ei(p),v=g==null?f:ei(g),f=new x(_,y+"leave",p,n,d),f.target=b,f.relatedTarget=v,_=null,Mn(d)===u&&(x=new x(m,y+"enter",g,n,d),x.target=v,x.relatedTarget=b,_=x),b=_,p&&g)t:{for(x=p,m=g,y=0,v=x;v;v=Un(v))y++;for(v=0,_=m;_;_=Un(_))v++;for(;0<y-v;)x=Un(x),y--;for(;0<v-y;)m=Un(m),v--;for(;y--;){if(x===m||m!==null&&x===m.alternate)break t;x=Un(x),m=Un(m)}x=null}else x=null;p!==null&&Mu(h,f,p,x,!1),g!==null&&b!==null&&Mu(h,b,g,x,!0)}}e:{if(f=u?ei(u):window,p=f.nodeName&&f.nodeName.toLowerCase(),p==="select"||p==="input"&&f.type==="file")var w=S0;else if(vu(f))if(Cf)w=C0;else{w=N0;var j=j0}else(p=f.nodeName)&&p.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(w=M0);if(w&&(w=w(t,u))){Mf(h,w,n,d);break e}j&&j(t,f,u),t==="focusout"&&(j=f._wrapperState)&&j.controlled&&f.type==="number"&&Fl(f,"number",f.value)}switch(j=u?ei(u):window,t){case"focusin":(vu(j)||j.contentEditable==="true")&&(Zn=j,ql=u,es=null);break;case"focusout":es=ql=Zn=null;break;case"mousedown":Zl=!0;break;case"contextmenu":case"mouseup":case"dragend":Zl=!1,Su(h,n,d);break;case"selectionchange":if(L0)break;case"keydown":case"keyup":Su(h,n,d)}var k;if(rc)e:{switch(t){case"compositionstart":var S="onCompositionStart";break e;case"compositionend":S="onCompositionEnd";break e;case"compositionupdate":S="onCompositionUpdate";break e}S=void 0}else qn?jf(t,n)&&(S="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(S="onCompositionStart");S&&(Sf&&n.locale!=="ko"&&(qn||S!=="onCompositionStart"?S==="onCompositionEnd"&&qn&&(k=kf()):(Xt=d,nc="value"in Xt?Xt.value:Xt.textContent,qn=!0)),j=Qr(u,S),0<j.length&&(S=new mu(S,t,null,n,d),h.push({event:S,listeners:j}),k?S.data=k:(k=Nf(n),k!==null&&(S.data=k)))),(k=v0?b0(t,n):_0(t,n))&&(u=Qr(u,"onBeforeInput"),0<u.length&&(d=new mu("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=k))}zf(h,e)})}function vs(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Qr(t,e){for(var n=e+"Capture",i=[];t!==null;){var s=t,r=s.stateNode;s.tag===5&&r!==null&&(s=r,r=hs(t,n),r!=null&&i.unshift(vs(t,r,s)),r=hs(t,e),r!=null&&i.push(vs(t,r,s))),t=t.return}return i}function Un(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Mu(t,e,n,i,s){for(var r=e._reactName,o=[];n!==null&&n!==i;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===i)break;l.tag===5&&u!==null&&(l=u,s?(a=hs(n,r),a!=null&&o.unshift(vs(n,a,l))):s||(a=hs(n,r),a!=null&&o.push(vs(n,a,l)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var O0=/\r\n?/g,R0=/\u0000|\uFFFD/g;function Cu(t){return(typeof t=="string"?t:""+t).replace(O0,`
`).replace(R0,"")}function tr(t,e,n){if(e=Cu(e),Cu(t)!==e&&n)throw Error(N(425))}function qr(){}var Jl=null,ea=null;function ta(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var na=typeof setTimeout=="function"?setTimeout:void 0,I0=typeof clearTimeout=="function"?clearTimeout:void 0,Pu=typeof Promise=="function"?Promise:void 0,z0=typeof queueMicrotask=="function"?queueMicrotask:typeof Pu<"u"?function(t){return Pu.resolve(null).then(t).catch(F0)}:na;function F0(t){setTimeout(function(){throw t})}function al(t,e){var n=e,i=0;do{var s=n.nextSibling;if(t.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(i===0){t.removeChild(s),ms(e);return}i--}else n!=="$"&&n!=="$?"&&n!=="$!"||i++;n=s}while(n);ms(e)}function rn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function Eu(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var Ni=Math.random().toString(36).slice(2),wt="__reactFiber$"+Ni,bs="__reactProps$"+Ni,Bt="__reactContainer$"+Ni,ia="__reactEvents$"+Ni,B0="__reactListeners$"+Ni,V0="__reactHandles$"+Ni;function Mn(t){var e=t[wt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Bt]||n[wt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Eu(t);t!==null;){if(n=t[wt])return n;t=Eu(t)}return e}t=n,n=t.parentNode}return null}function Fs(t){return t=t[wt]||t[Bt],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function ei(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(N(33))}function Po(t){return t[bs]||null}var sa=[],ti=-1;function gn(t){return{current:t}}function J(t){0>ti||(t.current=sa[ti],sa[ti]=null,ti--)}function X(t,e){ti++,sa[ti]=t.current,t.current=e}var hn={},Ee=gn(hn),He=gn(!1),On=hn;function mi(t,e){var n=t.type.contextTypes;if(!n)return hn;var i=t.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===e)return i.__reactInternalMemoizedMaskedChildContext;var s={},r;for(r in n)s[r]=e[r];return i&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=s),s}function We(t){return t=t.childContextTypes,t!=null}function Zr(){J(He),J(Ee)}function Lu(t,e,n){if(Ee.current!==hn)throw Error(N(168));X(Ee,e),X(He,n)}function Bf(t,e,n){var i=t.stateNode;if(e=e.childContextTypes,typeof i.getChildContext!="function")return n;i=i.getChildContext();for(var s in i)if(!(s in e))throw Error(N(108,jg(t)||"Unknown",s));return le({},n,i)}function Jr(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||hn,On=Ee.current,X(Ee,t),X(He,He.current),!0}function Du(t,e,n){var i=t.stateNode;if(!i)throw Error(N(169));n?(t=Bf(t,e,On),i.__reactInternalMemoizedMergedChildContext=t,J(He),J(Ee),X(Ee,t)):J(He),X(He,n)}var Lt=null,Eo=!1,cl=!1;function Vf(t){Lt===null?Lt=[t]:Lt.push(t)}function H0(t){Eo=!0,Vf(t)}function xn(){if(!cl&&Lt!==null){cl=!0;var t=0,e=U;try{var n=Lt;for(U=1;t<n.length;t++){var i=n[t];do i=i(!0);while(i!==null)}Lt=null,Eo=!1}catch(s){throw Lt!==null&&(Lt=Lt.slice(t+1)),hf(Za,xn),s}finally{U=e,cl=!1}}return null}var ni=[],ii=0,eo=null,to=0,tt=[],nt=0,Rn=null,Tt=1,At="";function kn(t,e){ni[ii++]=to,ni[ii++]=eo,eo=t,to=e}function Hf(t,e,n){tt[nt++]=Tt,tt[nt++]=At,tt[nt++]=Rn,Rn=t;var i=Tt;t=At;var s=32-ht(i)-1;i&=~(1<<s),n+=1;var r=32-ht(e)+s;if(30<r){var o=s-s%5;r=(i&(1<<o)-1).toString(32),i>>=o,s-=o,Tt=1<<32-ht(e)+s|n<<s|i,At=r+t}else Tt=1<<r|n<<s|i,At=t}function lc(t){t.return!==null&&(kn(t,1),Hf(t,1,0))}function ac(t){for(;t===eo;)eo=ni[--ii],ni[ii]=null,to=ni[--ii],ni[ii]=null;for(;t===Rn;)Rn=tt[--nt],tt[nt]=null,At=tt[--nt],tt[nt]=null,Tt=tt[--nt],tt[nt]=null}var Xe=null,Ge=null,te=!1,ut=null;function Wf(t,e){var n=it(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function Tu(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,Xe=t,Ge=rn(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,Xe=t,Ge=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Rn!==null?{id:Tt,overflow:At}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=it(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,Xe=t,Ge=null,!0):!1;default:return!1}}function ra(t){return(t.mode&1)!==0&&(t.flags&128)===0}function oa(t){if(te){var e=Ge;if(e){var n=e;if(!Tu(t,e)){if(ra(t))throw Error(N(418));e=rn(n.nextSibling);var i=Xe;e&&Tu(t,e)?Wf(i,n):(t.flags=t.flags&-4097|2,te=!1,Xe=t)}}else{if(ra(t))throw Error(N(418));t.flags=t.flags&-4097|2,te=!1,Xe=t}}}function Au(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Xe=t}function nr(t){if(t!==Xe)return!1;if(!te)return Au(t),te=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!ta(t.type,t.memoizedProps)),e&&(e=Ge)){if(ra(t))throw $f(),Error(N(418));for(;e;)Wf(t,e),e=rn(e.nextSibling)}if(Au(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(N(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Ge=rn(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Ge=null}}else Ge=Xe?rn(t.stateNode.nextSibling):null;return!0}function $f(){for(var t=Ge;t;)t=rn(t.nextSibling)}function gi(){Ge=Xe=null,te=!1}function cc(t){ut===null?ut=[t]:ut.push(t)}var W0=Wt.ReactCurrentBatchConfig;function Di(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var i=n.stateNode}if(!i)throw Error(N(147,t));var s=i,r=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===r?e.ref:(e=function(o){var l=s.refs;o===null?delete l[r]:l[r]=o},e._stringRef=r,e)}if(typeof t!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,t))}return t}function ir(t,e){throw t=Object.prototype.toString.call(e),Error(N(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function Ou(t){var e=t._init;return e(t._payload)}function Uf(t){function e(m,y){if(t){var v=m.deletions;v===null?(m.deletions=[y],m.flags|=16):v.push(y)}}function n(m,y){if(!t)return null;for(;y!==null;)e(m,y),y=y.sibling;return null}function i(m,y){for(m=new Map;y!==null;)y.key!==null?m.set(y.key,y):m.set(y.index,y),y=y.sibling;return m}function s(m,y){return m=cn(m,y),m.index=0,m.sibling=null,m}function r(m,y,v){return m.index=v,t?(v=m.alternate,v!==null?(v=v.index,v<y?(m.flags|=2,y):v):(m.flags|=2,y)):(m.flags|=1048576,y)}function o(m){return t&&m.alternate===null&&(m.flags|=2),m}function l(m,y,v,_){return y===null||y.tag!==6?(y=gl(v,m.mode,_),y.return=m,y):(y=s(y,v),y.return=m,y)}function a(m,y,v,_){var w=v.type;return w===Qn?d(m,y,v.props.children,_,v.key):y!==null&&(y.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Ut&&Ou(w)===y.type)?(_=s(y,v.props),_.ref=Di(m,y,v),_.return=m,_):(_=Tr(v.type,v.key,v.props,null,m.mode,_),_.ref=Di(m,y,v),_.return=m,_)}function u(m,y,v,_){return y===null||y.tag!==4||y.stateNode.containerInfo!==v.containerInfo||y.stateNode.implementation!==v.implementation?(y=xl(v,m.mode,_),y.return=m,y):(y=s(y,v.children||[]),y.return=m,y)}function d(m,y,v,_,w){return y===null||y.tag!==7?(y=Dn(v,m.mode,_,w),y.return=m,y):(y=s(y,v),y.return=m,y)}function h(m,y,v){if(typeof y=="string"&&y!==""||typeof y=="number")return y=gl(""+y,m.mode,v),y.return=m,y;if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Ks:return v=Tr(y.type,y.key,y.props,null,m.mode,v),v.ref=Di(m,null,y),v.return=m,v;case Xn:return y=xl(y,m.mode,v),y.return=m,y;case Ut:var _=y._init;return h(m,_(y._payload),v)}if(Vi(y)||Mi(y))return y=Dn(y,m.mode,v,null),y.return=m,y;ir(m,y)}return null}function f(m,y,v,_){var w=y!==null?y.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return w!==null?null:l(m,y,""+v,_);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Ks:return v.key===w?a(m,y,v,_):null;case Xn:return v.key===w?u(m,y,v,_):null;case Ut:return w=v._init,f(m,y,w(v._payload),_)}if(Vi(v)||Mi(v))return w!==null?null:d(m,y,v,_,null);ir(m,v)}return null}function p(m,y,v,_,w){if(typeof _=="string"&&_!==""||typeof _=="number")return m=m.get(v)||null,l(y,m,""+_,w);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case Ks:return m=m.get(_.key===null?v:_.key)||null,a(y,m,_,w);case Xn:return m=m.get(_.key===null?v:_.key)||null,u(y,m,_,w);case Ut:var j=_._init;return p(m,y,v,j(_._payload),w)}if(Vi(_)||Mi(_))return m=m.get(v)||null,d(y,m,_,w,null);ir(y,_)}return null}function g(m,y,v,_){for(var w=null,j=null,k=y,S=y=0,C=null;k!==null&&S<v.length;S++){k.index>S?(C=k,k=null):C=k.sibling;var M=f(m,k,v[S],_);if(M===null){k===null&&(k=C);break}t&&k&&M.alternate===null&&e(m,k),y=r(M,y,S),j===null?w=M:j.sibling=M,j=M,k=C}if(S===v.length)return n(m,k),te&&kn(m,S),w;if(k===null){for(;S<v.length;S++)k=h(m,v[S],_),k!==null&&(y=r(k,y,S),j===null?w=k:j.sibling=k,j=k);return te&&kn(m,S),w}for(k=i(m,k);S<v.length;S++)C=p(k,m,S,v[S],_),C!==null&&(t&&C.alternate!==null&&k.delete(C.key===null?S:C.key),y=r(C,y,S),j===null?w=C:j.sibling=C,j=C);return t&&k.forEach(function(A){return e(m,A)}),te&&kn(m,S),w}function x(m,y,v,_){var w=Mi(v);if(typeof w!="function")throw Error(N(150));if(v=w.call(v),v==null)throw Error(N(151));for(var j=w=null,k=y,S=y=0,C=null,M=v.next();k!==null&&!M.done;S++,M=v.next()){k.index>S?(C=k,k=null):C=k.sibling;var A=f(m,k,M.value,_);if(A===null){k===null&&(k=C);break}t&&k&&A.alternate===null&&e(m,k),y=r(A,y,S),j===null?w=A:j.sibling=A,j=A,k=C}if(M.done)return n(m,k),te&&kn(m,S),w;if(k===null){for(;!M.done;S++,M=v.next())M=h(m,M.value,_),M!==null&&(y=r(M,y,S),j===null?w=M:j.sibling=M,j=M);return te&&kn(m,S),w}for(k=i(m,k);!M.done;S++,M=v.next())M=p(k,m,S,M.value,_),M!==null&&(t&&M.alternate!==null&&k.delete(M.key===null?S:M.key),y=r(M,y,S),j===null?w=M:j.sibling=M,j=M);return t&&k.forEach(function(I){return e(m,I)}),te&&kn(m,S),w}function b(m,y,v,_){if(typeof v=="object"&&v!==null&&v.type===Qn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Ks:e:{for(var w=v.key,j=y;j!==null;){if(j.key===w){if(w=v.type,w===Qn){if(j.tag===7){n(m,j.sibling),y=s(j,v.props.children),y.return=m,m=y;break e}}else if(j.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Ut&&Ou(w)===j.type){n(m,j.sibling),y=s(j,v.props),y.ref=Di(m,j,v),y.return=m,m=y;break e}n(m,j);break}else e(m,j);j=j.sibling}v.type===Qn?(y=Dn(v.props.children,m.mode,_,v.key),y.return=m,m=y):(_=Tr(v.type,v.key,v.props,null,m.mode,_),_.ref=Di(m,y,v),_.return=m,m=_)}return o(m);case Xn:e:{for(j=v.key;y!==null;){if(y.key===j)if(y.tag===4&&y.stateNode.containerInfo===v.containerInfo&&y.stateNode.implementation===v.implementation){n(m,y.sibling),y=s(y,v.children||[]),y.return=m,m=y;break e}else{n(m,y);break}else e(m,y);y=y.sibling}y=xl(v,m.mode,_),y.return=m,m=y}return o(m);case Ut:return j=v._init,b(m,y,j(v._payload),_)}if(Vi(v))return g(m,y,v,_);if(Mi(v))return x(m,y,v,_);ir(m,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,y!==null&&y.tag===6?(n(m,y.sibling),y=s(y,v),y.return=m,m=y):(n(m,y),y=gl(v,m.mode,_),y.return=m,m=y),o(m)):n(m,y)}return b}var xi=Uf(!0),Kf=Uf(!1),no=gn(null),io=null,si=null,uc=null;function dc(){uc=si=io=null}function hc(t){var e=no.current;J(no),t._currentValue=e}function la(t,e,n){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===n)break;t=t.return}}function di(t,e){io=t,uc=si=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Ve=!0),t.firstContext=null)}function rt(t){var e=t._currentValue;if(uc!==t)if(t={context:t,memoizedValue:e,next:null},si===null){if(io===null)throw Error(N(308));si=t,io.dependencies={lanes:0,firstContext:t}}else si=si.next=t;return e}var Cn=null;function fc(t){Cn===null?Cn=[t]:Cn.push(t)}function Yf(t,e,n,i){var s=e.interleaved;return s===null?(n.next=n,fc(e)):(n.next=s.next,s.next=n),e.interleaved=n,Vt(t,i)}function Vt(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Kt=!1;function pc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Gf(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function zt(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function on(t,e,n){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,V&2){var s=i.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),i.pending=e,Vt(t,n)}return s=i.interleaved,s===null?(e.next=e,fc(i)):(e.next=s.next,s.next=e),i.interleaved=e,Vt(t,n)}function Mr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,Ja(t,n)}}function Ru(t,e){var n=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var s=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};r===null?s=r=o:r=r.next=o,n=n.next}while(n!==null);r===null?s=r=e:r=r.next=e}else s=r=e;n={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:r,shared:i.shared,effects:i.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function so(t,e,n,i){var s=t.updateQueue;Kt=!1;var r=s.firstBaseUpdate,o=s.lastBaseUpdate,l=s.shared.pending;if(l!==null){s.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?r=u:o.next=u,o=a;var d=t.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==o&&(l===null?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=a))}if(r!==null){var h=s.baseState;o=0,d=u=a=null,l=r;do{var f=l.lane,p=l.eventTime;if((i&f)===f){d!==null&&(d=d.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var g=t,x=l;switch(f=e,p=n,x.tag){case 1:if(g=x.payload,typeof g=="function"){h=g.call(p,h,f);break e}h=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=x.payload,f=typeof g=="function"?g.call(p,h,f):g,f==null)break e;h=le({},h,f);break e;case 2:Kt=!0}}l.callback!==null&&l.lane!==0&&(t.flags|=64,f=s.effects,f===null?s.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(u=d=p,a=h):d=d.next=p,o|=f;if(l=l.next,l===null){if(l=s.shared.pending,l===null)break;f=l,l=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(d===null&&(a=h),s.baseState=a,s.firstBaseUpdate=u,s.lastBaseUpdate=d,e=s.shared.interleaved,e!==null){s=e;do o|=s.lane,s=s.next;while(s!==e)}else r===null&&(s.shared.lanes=0);zn|=o,t.lanes=o,t.memoizedState=h}}function Iu(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var i=t[e],s=i.callback;if(s!==null){if(i.callback=null,i=n,typeof s!="function")throw Error(N(191,s));s.call(i)}}}var Bs={},St=gn(Bs),_s=gn(Bs),ws=gn(Bs);function Pn(t){if(t===Bs)throw Error(N(174));return t}function mc(t,e){switch(X(ws,e),X(_s,t),X(St,Bs),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:Vl(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=Vl(e,t)}J(St),X(St,e)}function yi(){J(St),J(_s),J(ws)}function Xf(t){Pn(ws.current);var e=Pn(St.current),n=Vl(e,t.type);e!==n&&(X(_s,t),X(St,n))}function gc(t){_s.current===t&&(J(St),J(_s))}var re=gn(0);function ro(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var ul=[];function xc(){for(var t=0;t<ul.length;t++)ul[t]._workInProgressVersionPrimary=null;ul.length=0}var Cr=Wt.ReactCurrentDispatcher,dl=Wt.ReactCurrentBatchConfig,In=0,oe=null,pe=null,xe=null,oo=!1,ts=!1,ks=0,$0=0;function Se(){throw Error(N(321))}function yc(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!mt(t[n],e[n]))return!1;return!0}function vc(t,e,n,i,s,r){if(In=r,oe=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Cr.current=t===null||t.memoizedState===null?G0:X0,t=n(i,s),ts){r=0;do{if(ts=!1,ks=0,25<=r)throw Error(N(301));r+=1,xe=pe=null,e.updateQueue=null,Cr.current=Q0,t=n(i,s)}while(ts)}if(Cr.current=lo,e=pe!==null&&pe.next!==null,In=0,xe=pe=oe=null,oo=!1,e)throw Error(N(300));return t}function bc(){var t=ks!==0;return ks=0,t}function bt(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xe===null?oe.memoizedState=xe=t:xe=xe.next=t,xe}function ot(){if(pe===null){var t=oe.alternate;t=t!==null?t.memoizedState:null}else t=pe.next;var e=xe===null?oe.memoizedState:xe.next;if(e!==null)xe=e,pe=t;else{if(t===null)throw Error(N(310));pe=t,t={memoizedState:pe.memoizedState,baseState:pe.baseState,baseQueue:pe.baseQueue,queue:pe.queue,next:null},xe===null?oe.memoizedState=xe=t:xe=xe.next=t}return xe}function Ss(t,e){return typeof e=="function"?e(t):e}function hl(t){var e=ot(),n=e.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=t;var i=pe,s=i.baseQueue,r=n.pending;if(r!==null){if(s!==null){var o=s.next;s.next=r.next,r.next=o}i.baseQueue=s=r,n.pending=null}if(s!==null){r=s.next,i=i.baseState;var l=o=null,a=null,u=r;do{var d=u.lane;if((In&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),i=u.hasEagerState?u.eagerState:t(i,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=h,o=i):a=a.next=h,oe.lanes|=d,zn|=d}u=u.next}while(u!==null&&u!==r);a===null?o=i:a.next=l,mt(i,e.memoizedState)||(Ve=!0),e.memoizedState=i,e.baseState=o,e.baseQueue=a,n.lastRenderedState=i}if(t=n.interleaved,t!==null){s=t;do r=s.lane,oe.lanes|=r,zn|=r,s=s.next;while(s!==t)}else s===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function fl(t){var e=ot(),n=e.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=t;var i=n.dispatch,s=n.pending,r=e.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do r=t(r,o.action),o=o.next;while(o!==s);mt(r,e.memoizedState)||(Ve=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,i]}function Qf(){}function qf(t,e){var n=oe,i=ot(),s=e(),r=!mt(i.memoizedState,s);if(r&&(i.memoizedState=s,Ve=!0),i=i.queue,_c(ep.bind(null,n,i,t),[t]),i.getSnapshot!==e||r||xe!==null&&xe.memoizedState.tag&1){if(n.flags|=2048,js(9,Jf.bind(null,n,i,s,e),void 0,null),ve===null)throw Error(N(349));In&30||Zf(n,e,s)}return s}function Zf(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=oe.updateQueue,e===null?(e={lastEffect:null,stores:null},oe.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Jf(t,e,n,i){e.value=n,e.getSnapshot=i,tp(e)&&np(t)}function ep(t,e,n){return n(function(){tp(e)&&np(t)})}function tp(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!mt(t,n)}catch{return!0}}function np(t){var e=Vt(t,1);e!==null&&ft(e,t,1,-1)}function zu(t){var e=bt();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ss,lastRenderedState:t},e.queue=t,t=t.dispatch=Y0.bind(null,oe,t),[e.memoizedState,t]}function js(t,e,n,i){return t={tag:t,create:e,destroy:n,deps:i,next:null},e=oe.updateQueue,e===null?(e={lastEffect:null,stores:null},oe.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(i=n.next,n.next=t,t.next=i,e.lastEffect=t)),t}function ip(){return ot().memoizedState}function Pr(t,e,n,i){var s=bt();oe.flags|=t,s.memoizedState=js(1|e,n,void 0,i===void 0?null:i)}function Lo(t,e,n,i){var s=ot();i=i===void 0?null:i;var r=void 0;if(pe!==null){var o=pe.memoizedState;if(r=o.destroy,i!==null&&yc(i,o.deps)){s.memoizedState=js(e,n,r,i);return}}oe.flags|=t,s.memoizedState=js(1|e,n,r,i)}function Fu(t,e){return Pr(8390656,8,t,e)}function _c(t,e){return Lo(2048,8,t,e)}function sp(t,e){return Lo(4,2,t,e)}function rp(t,e){return Lo(4,4,t,e)}function op(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function lp(t,e,n){return n=n!=null?n.concat([t]):null,Lo(4,4,op.bind(null,e,t),n)}function wc(){}function ap(t,e){var n=ot();e=e===void 0?null:e;var i=n.memoizedState;return i!==null&&e!==null&&yc(e,i[1])?i[0]:(n.memoizedState=[t,e],t)}function cp(t,e){var n=ot();e=e===void 0?null:e;var i=n.memoizedState;return i!==null&&e!==null&&yc(e,i[1])?i[0]:(t=t(),n.memoizedState=[t,e],t)}function up(t,e,n){return In&21?(mt(n,e)||(n=mf(),oe.lanes|=n,zn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Ve=!0),t.memoizedState=n)}function U0(t,e){var n=U;U=n!==0&&4>n?n:4,t(!0);var i=dl.transition;dl.transition={};try{t(!1),e()}finally{U=n,dl.transition=i}}function dp(){return ot().memoizedState}function K0(t,e,n){var i=an(t);if(n={lane:i,action:n,hasEagerState:!1,eagerState:null,next:null},hp(t))fp(e,n);else if(n=Yf(t,e,n,i),n!==null){var s=Oe();ft(n,t,i,s),pp(n,e,i)}}function Y0(t,e,n){var i=an(t),s={lane:i,action:n,hasEagerState:!1,eagerState:null,next:null};if(hp(t))fp(e,s);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var o=e.lastRenderedState,l=r(o,n);if(s.hasEagerState=!0,s.eagerState=l,mt(l,o)){var a=e.interleaved;a===null?(s.next=s,fc(e)):(s.next=a.next,a.next=s),e.interleaved=s;return}}catch{}finally{}n=Yf(t,e,s,i),n!==null&&(s=Oe(),ft(n,t,i,s),pp(n,e,i))}}function hp(t){var e=t.alternate;return t===oe||e!==null&&e===oe}function fp(t,e){ts=oo=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function pp(t,e,n){if(n&4194240){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,Ja(t,n)}}var lo={readContext:rt,useCallback:Se,useContext:Se,useEffect:Se,useImperativeHandle:Se,useInsertionEffect:Se,useLayoutEffect:Se,useMemo:Se,useReducer:Se,useRef:Se,useState:Se,useDebugValue:Se,useDeferredValue:Se,useTransition:Se,useMutableSource:Se,useSyncExternalStore:Se,useId:Se,unstable_isNewReconciler:!1},G0={readContext:rt,useCallback:function(t,e){return bt().memoizedState=[t,e===void 0?null:e],t},useContext:rt,useEffect:Fu,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,Pr(4194308,4,op.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Pr(4194308,4,t,e)},useInsertionEffect:function(t,e){return Pr(4,2,t,e)},useMemo:function(t,e){var n=bt();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var i=bt();return e=n!==void 0?n(e):e,i.memoizedState=i.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},i.queue=t,t=t.dispatch=K0.bind(null,oe,t),[i.memoizedState,t]},useRef:function(t){var e=bt();return t={current:t},e.memoizedState=t},useState:zu,useDebugValue:wc,useDeferredValue:function(t){return bt().memoizedState=t},useTransition:function(){var t=zu(!1),e=t[0];return t=U0.bind(null,t[1]),bt().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var i=oe,s=bt();if(te){if(n===void 0)throw Error(N(407));n=n()}else{if(n=e(),ve===null)throw Error(N(349));In&30||Zf(i,e,n)}s.memoizedState=n;var r={value:n,getSnapshot:e};return s.queue=r,Fu(ep.bind(null,i,r,t),[t]),i.flags|=2048,js(9,Jf.bind(null,i,r,n,e),void 0,null),n},useId:function(){var t=bt(),e=ve.identifierPrefix;if(te){var n=At,i=Tt;n=(i&~(1<<32-ht(i)-1)).toString(32)+n,e=":"+e+"R"+n,n=ks++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=$0++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},X0={readContext:rt,useCallback:ap,useContext:rt,useEffect:_c,useImperativeHandle:lp,useInsertionEffect:sp,useLayoutEffect:rp,useMemo:cp,useReducer:hl,useRef:ip,useState:function(){return hl(Ss)},useDebugValue:wc,useDeferredValue:function(t){var e=ot();return up(e,pe.memoizedState,t)},useTransition:function(){var t=hl(Ss)[0],e=ot().memoizedState;return[t,e]},useMutableSource:Qf,useSyncExternalStore:qf,useId:dp,unstable_isNewReconciler:!1},Q0={readContext:rt,useCallback:ap,useContext:rt,useEffect:_c,useImperativeHandle:lp,useInsertionEffect:sp,useLayoutEffect:rp,useMemo:cp,useReducer:fl,useRef:ip,useState:function(){return fl(Ss)},useDebugValue:wc,useDeferredValue:function(t){var e=ot();return pe===null?e.memoizedState=t:up(e,pe.memoizedState,t)},useTransition:function(){var t=fl(Ss)[0],e=ot().memoizedState;return[t,e]},useMutableSource:Qf,useSyncExternalStore:qf,useId:dp,unstable_isNewReconciler:!1};function at(t,e){if(t&&t.defaultProps){e=le({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function aa(t,e,n,i){e=t.memoizedState,n=n(i,e),n=n==null?e:le({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Do={isMounted:function(t){return(t=t._reactInternals)?Wn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var i=Oe(),s=an(t),r=zt(i,s);r.payload=e,n!=null&&(r.callback=n),e=on(t,r,s),e!==null&&(ft(e,t,s,i),Mr(e,t,s))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var i=Oe(),s=an(t),r=zt(i,s);r.tag=1,r.payload=e,n!=null&&(r.callback=n),e=on(t,r,s),e!==null&&(ft(e,t,s,i),Mr(e,t,s))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Oe(),i=an(t),s=zt(n,i);s.tag=2,e!=null&&(s.callback=e),e=on(t,s,i),e!==null&&(ft(e,t,i,n),Mr(e,t,i))}};function Bu(t,e,n,i,s,r,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,r,o):e.prototype&&e.prototype.isPureReactComponent?!xs(n,i)||!xs(s,r):!0}function mp(t,e,n){var i=!1,s=hn,r=e.contextType;return typeof r=="object"&&r!==null?r=rt(r):(s=We(e)?On:Ee.current,i=e.contextTypes,r=(i=i!=null)?mi(t,s):hn),e=new e(n,r),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Do,t.stateNode=e,e._reactInternals=t,i&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=s,t.__reactInternalMemoizedMaskedChildContext=r),e}function Vu(t,e,n,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,i),e.state!==t&&Do.enqueueReplaceState(e,e.state,null)}function ca(t,e,n,i){var s=t.stateNode;s.props=n,s.state=t.memoizedState,s.refs={},pc(t);var r=e.contextType;typeof r=="object"&&r!==null?s.context=rt(r):(r=We(e)?On:Ee.current,s.context=mi(t,r)),s.state=t.memoizedState,r=e.getDerivedStateFromProps,typeof r=="function"&&(aa(t,e,r,n),s.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(e=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),e!==s.state&&Do.enqueueReplaceState(s,s.state,null),so(t,n,s,i),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308)}function vi(t,e){try{var n="",i=e;do n+=Sg(i),i=i.return;while(i);var s=n}catch(r){s=`
Error generating stack: `+r.message+`
`+r.stack}return{value:t,source:e,stack:s,digest:null}}function pl(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function ua(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var q0=typeof WeakMap=="function"?WeakMap:Map;function gp(t,e,n){n=zt(-1,n),n.tag=3,n.payload={element:null};var i=e.value;return n.callback=function(){co||(co=!0,ba=i),ua(t,e)},n}function xp(t,e,n){n=zt(-1,n),n.tag=3;var i=t.type.getDerivedStateFromError;if(typeof i=="function"){var s=e.value;n.payload=function(){return i(s)},n.callback=function(){ua(t,e)}}var r=t.stateNode;return r!==null&&typeof r.componentDidCatch=="function"&&(n.callback=function(){ua(t,e),typeof i!="function"&&(ln===null?ln=new Set([this]):ln.add(this));var o=e.stack;this.componentDidCatch(e.value,{componentStack:o!==null?o:""})}),n}function Hu(t,e,n){var i=t.pingCache;if(i===null){i=t.pingCache=new q0;var s=new Set;i.set(e,s)}else s=i.get(e),s===void 0&&(s=new Set,i.set(e,s));s.has(n)||(s.add(n),t=dx.bind(null,t,e,n),e.then(t,t))}function Wu(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function $u(t,e,n,i,s){return t.mode&1?(t.flags|=65536,t.lanes=s,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=zt(-1,1),e.tag=2,on(n,e,1))),n.lanes|=1),t)}var Z0=Wt.ReactCurrentOwner,Ve=!1;function Ae(t,e,n,i){e.child=t===null?Kf(e,null,n,i):xi(e,t.child,n,i)}function Uu(t,e,n,i,s){n=n.render;var r=e.ref;return di(e,s),i=vc(t,e,n,i,r,s),n=bc(),t!==null&&!Ve?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~s,Ht(t,e,s)):(te&&n&&lc(e),e.flags|=1,Ae(t,e,i,s),e.child)}function Ku(t,e,n,i,s){if(t===null){var r=n.type;return typeof r=="function"&&!Ec(r)&&r.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=r,yp(t,e,r,i,s)):(t=Tr(n.type,null,i,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!(t.lanes&s)){var o=r.memoizedProps;if(n=n.compare,n=n!==null?n:xs,n(o,i)&&t.ref===e.ref)return Ht(t,e,s)}return e.flags|=1,t=cn(r,i),t.ref=e.ref,t.return=e,e.child=t}function yp(t,e,n,i,s){if(t!==null){var r=t.memoizedProps;if(xs(r,i)&&t.ref===e.ref)if(Ve=!1,e.pendingProps=i=r,(t.lanes&s)!==0)t.flags&131072&&(Ve=!0);else return e.lanes=t.lanes,Ht(t,e,s)}return da(t,e,n,i,s)}function vp(t,e,n){var i=e.pendingProps,s=i.children,r=t!==null?t.memoizedState:null;if(i.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},X(oi,Ke),Ke|=n;else{if(!(n&1073741824))return t=r!==null?r.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,X(oi,Ke),Ke|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=r!==null?r.baseLanes:n,X(oi,Ke),Ke|=i}else r!==null?(i=r.baseLanes|n,e.memoizedState=null):i=n,X(oi,Ke),Ke|=i;return Ae(t,e,s,n),e.child}function bp(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function da(t,e,n,i,s){var r=We(n)?On:Ee.current;return r=mi(e,r),di(e,s),n=vc(t,e,n,i,r,s),i=bc(),t!==null&&!Ve?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~s,Ht(t,e,s)):(te&&i&&lc(e),e.flags|=1,Ae(t,e,n,s),e.child)}function Yu(t,e,n,i,s){if(We(n)){var r=!0;Jr(e)}else r=!1;if(di(e,s),e.stateNode===null)Er(t,e),mp(e,n,i),ca(e,n,i,s),i=!0;else if(t===null){var o=e.stateNode,l=e.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=rt(u):(u=We(n)?On:Ee.current,u=mi(e,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==i||a!==u)&&Vu(e,o,i,u),Kt=!1;var f=e.memoizedState;o.state=f,so(e,i,o,s),a=e.memoizedState,l!==i||f!==a||He.current||Kt?(typeof d=="function"&&(aa(e,n,d,i),a=e.memoizedState),(l=Kt||Bu(e,n,l,i,f,a,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=a),o.props=i,o.state=a,o.context=u,i=l):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{o=e.stateNode,Gf(t,e),l=e.memoizedProps,u=e.type===e.elementType?l:at(e.type,l),o.props=u,h=e.pendingProps,f=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=rt(a):(a=We(n)?On:Ee.current,a=mi(e,a));var p=n.getDerivedStateFromProps;(d=typeof p=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==h||f!==a)&&Vu(e,o,i,a),Kt=!1,f=e.memoizedState,o.state=f,so(e,i,o,s);var g=e.memoizedState;l!==h||f!==g||He.current||Kt?(typeof p=="function"&&(aa(e,n,p,i),g=e.memoizedState),(u=Kt||Bu(e,n,u,i,f,g,a)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(i,g,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(i,g,a)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=g),o.props=i,o.state=g,o.context=a,i=u):(typeof o.componentDidUpdate!="function"||l===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),i=!1)}return ha(t,e,n,i,r,s)}function ha(t,e,n,i,s,r){bp(t,e);var o=(e.flags&128)!==0;if(!i&&!o)return s&&Du(e,n,!1),Ht(t,e,r);i=e.stateNode,Z0.current=e;var l=o&&typeof n.getDerivedStateFromError!="function"?null:i.render();return e.flags|=1,t!==null&&o?(e.child=xi(e,t.child,null,r),e.child=xi(e,null,l,r)):Ae(t,e,l,r),e.memoizedState=i.state,s&&Du(e,n,!0),e.child}function _p(t){var e=t.stateNode;e.pendingContext?Lu(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Lu(t,e.context,!1),mc(t,e.containerInfo)}function Gu(t,e,n,i,s){return gi(),cc(s),e.flags|=256,Ae(t,e,n,i),e.child}var fa={dehydrated:null,treeContext:null,retryLane:0};function pa(t){return{baseLanes:t,cachePool:null,transitions:null}}function wp(t,e,n){var i=e.pendingProps,s=re.current,r=!1,o=(e.flags&128)!==0,l;if((l=o)||(l=t!==null&&t.memoizedState===null?!1:(s&2)!==0),l?(r=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(s|=1),X(re,s&1),t===null)return oa(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(o=i.children,t=i.fallback,r?(i=e.mode,r=e.child,o={mode:"hidden",children:o},!(i&1)&&r!==null?(r.childLanes=0,r.pendingProps=o):r=Oo(o,i,0,null),t=Dn(t,i,n,null),r.return=e,t.return=e,r.sibling=t,e.child=r,e.child.memoizedState=pa(n),e.memoizedState=fa,t):kc(e,o));if(s=t.memoizedState,s!==null&&(l=s.dehydrated,l!==null))return J0(t,e,o,i,l,s,n);if(r){r=i.fallback,o=e.mode,s=t.child,l=s.sibling;var a={mode:"hidden",children:i.children};return!(o&1)&&e.child!==s?(i=e.child,i.childLanes=0,i.pendingProps=a,e.deletions=null):(i=cn(s,a),i.subtreeFlags=s.subtreeFlags&14680064),l!==null?r=cn(l,r):(r=Dn(r,o,n,null),r.flags|=2),r.return=e,i.return=e,i.sibling=r,e.child=i,i=r,r=e.child,o=t.child.memoizedState,o=o===null?pa(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},r.memoizedState=o,r.childLanes=t.childLanes&~n,e.memoizedState=fa,i}return r=t.child,t=r.sibling,i=cn(r,{mode:"visible",children:i.children}),!(e.mode&1)&&(i.lanes=n),i.return=e,i.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=i,e.memoizedState=null,i}function kc(t,e){return e=Oo({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function sr(t,e,n,i){return i!==null&&cc(i),xi(e,t.child,null,n),t=kc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function J0(t,e,n,i,s,r,o){if(n)return e.flags&256?(e.flags&=-257,i=pl(Error(N(422))),sr(t,e,o,i)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(r=i.fallback,s=e.mode,i=Oo({mode:"visible",children:i.children},s,0,null),r=Dn(r,s,o,null),r.flags|=2,i.return=e,r.return=e,i.sibling=r,e.child=i,e.mode&1&&xi(e,t.child,null,o),e.child.memoizedState=pa(o),e.memoizedState=fa,r);if(!(e.mode&1))return sr(t,e,o,null);if(s.data==="$!"){if(i=s.nextSibling&&s.nextSibling.dataset,i)var l=i.dgst;return i=l,r=Error(N(419)),i=pl(r,i,void 0),sr(t,e,o,i)}if(l=(o&t.childLanes)!==0,Ve||l){if(i=ve,i!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(i.suspendedLanes|o)?0:s,s!==0&&s!==r.retryLane&&(r.retryLane=s,Vt(t,s),ft(i,t,s,-1))}return Pc(),i=pl(Error(N(421))),sr(t,e,o,i)}return s.data==="$?"?(e.flags|=128,e.child=t.child,e=hx.bind(null,t),s._reactRetry=e,null):(t=r.treeContext,Ge=rn(s.nextSibling),Xe=e,te=!0,ut=null,t!==null&&(tt[nt++]=Tt,tt[nt++]=At,tt[nt++]=Rn,Tt=t.id,At=t.overflow,Rn=e),e=kc(e,i.children),e.flags|=4096,e)}function Xu(t,e,n){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),la(t.return,e,n)}function ml(t,e,n,i,s){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:s}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=i,r.tail=n,r.tailMode=s)}function kp(t,e,n){var i=e.pendingProps,s=i.revealOrder,r=i.tail;if(Ae(t,e,i.children,n),i=re.current,i&2)i=i&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Xu(t,n,e);else if(t.tag===19)Xu(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}if(X(re,i),!(e.mode&1))e.memoizedState=null;else switch(s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&ro(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),ml(e,!1,s,n,r);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&ro(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}ml(e,!0,n,null,r);break;case"together":ml(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Er(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Ht(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),zn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(N(153));if(e.child!==null){for(t=e.child,n=cn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=cn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function ex(t,e,n){switch(e.tag){case 3:_p(e),gi();break;case 5:Xf(e);break;case 1:We(e.type)&&Jr(e);break;case 4:mc(e,e.stateNode.containerInfo);break;case 10:var i=e.type._context,s=e.memoizedProps.value;X(no,i._currentValue),i._currentValue=s;break;case 13:if(i=e.memoizedState,i!==null)return i.dehydrated!==null?(X(re,re.current&1),e.flags|=128,null):n&e.child.childLanes?wp(t,e,n):(X(re,re.current&1),t=Ht(t,e,n),t!==null?t.sibling:null);X(re,re.current&1);break;case 19:if(i=(n&e.childLanes)!==0,t.flags&128){if(i)return kp(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),X(re,re.current),i)break;return null;case 22:case 23:return e.lanes=0,vp(t,e,n)}return Ht(t,e,n)}var Sp,ma,jp,Np;Sp=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ma=function(){};jp=function(t,e,n,i){var s=t.memoizedProps;if(s!==i){t=e.stateNode,Pn(St.current);var r=null;switch(n){case"input":s=Il(t,s),i=Il(t,i),r=[];break;case"select":s=le({},s,{value:void 0}),i=le({},i,{value:void 0}),r=[];break;case"textarea":s=Bl(t,s),i=Bl(t,i),r=[];break;default:typeof s.onClick!="function"&&typeof i.onClick=="function"&&(t.onclick=qr)}Hl(n,i);var o;n=null;for(u in s)if(!i.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var l=s[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(us.hasOwnProperty(u)?r||(r=[]):(r=r||[]).push(u,null));for(u in i){var a=i[u];if(l=s!=null?s[u]:void 0,i.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(r||(r=[]),r.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(r=r||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(r=r||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(us.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&Z("scroll",t),r||l===a||(r=[])):(r=r||[]).push(u,a))}n&&(r=r||[]).push("style",n);var u=r;(e.updateQueue=u)&&(e.flags|=4)}};Np=function(t,e,n,i){n!==i&&(e.flags|=4)};function Ti(t,e){if(!te)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function je(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,i=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags&14680064,i|=s.flags&14680064,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=i,t.childLanes=n,e}function tx(t,e,n){var i=e.pendingProps;switch(ac(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return je(e),null;case 1:return We(e.type)&&Zr(),je(e),null;case 3:return i=e.stateNode,yi(),J(He),J(Ee),xc(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(nr(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,ut!==null&&(ka(ut),ut=null))),ma(t,e),je(e),null;case 5:gc(e);var s=Pn(ws.current);if(n=e.type,t!==null&&e.stateNode!=null)jp(t,e,n,i,s),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!i){if(e.stateNode===null)throw Error(N(166));return je(e),null}if(t=Pn(St.current),nr(e)){i=e.stateNode,n=e.type;var r=e.memoizedProps;switch(i[wt]=e,i[bs]=r,t=(e.mode&1)!==0,n){case"dialog":Z("cancel",i),Z("close",i);break;case"iframe":case"object":case"embed":Z("load",i);break;case"video":case"audio":for(s=0;s<Wi.length;s++)Z(Wi[s],i);break;case"source":Z("error",i);break;case"img":case"image":case"link":Z("error",i),Z("load",i);break;case"details":Z("toggle",i);break;case"input":su(i,r),Z("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!r.multiple},Z("invalid",i);break;case"textarea":ou(i,r),Z("invalid",i)}Hl(n,r),s=null;for(var o in r)if(r.hasOwnProperty(o)){var l=r[o];o==="children"?typeof l=="string"?i.textContent!==l&&(r.suppressHydrationWarning!==!0&&tr(i.textContent,l,t),s=["children",l]):typeof l=="number"&&i.textContent!==""+l&&(r.suppressHydrationWarning!==!0&&tr(i.textContent,l,t),s=["children",""+l]):us.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&Z("scroll",i)}switch(n){case"input":Ys(i),ru(i,r,!0);break;case"textarea":Ys(i),lu(i);break;case"select":case"option":break;default:typeof r.onClick=="function"&&(i.onclick=qr)}i=s,e.updateQueue=i,i!==null&&(e.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Jh(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof i.is=="string"?t=o.createElement(n,{is:i.is}):(t=o.createElement(n),n==="select"&&(o=t,i.multiple?o.multiple=!0:i.size&&(o.size=i.size))):t=o.createElementNS(t,n),t[wt]=e,t[bs]=i,Sp(t,e,!1,!1),e.stateNode=t;e:{switch(o=Wl(n,i),n){case"dialog":Z("cancel",t),Z("close",t),s=i;break;case"iframe":case"object":case"embed":Z("load",t),s=i;break;case"video":case"audio":for(s=0;s<Wi.length;s++)Z(Wi[s],t);s=i;break;case"source":Z("error",t),s=i;break;case"img":case"image":case"link":Z("error",t),Z("load",t),s=i;break;case"details":Z("toggle",t),s=i;break;case"input":su(t,i),s=Il(t,i),Z("invalid",t);break;case"option":s=i;break;case"select":t._wrapperState={wasMultiple:!!i.multiple},s=le({},i,{value:void 0}),Z("invalid",t);break;case"textarea":ou(t,i),s=Bl(t,i),Z("invalid",t);break;default:s=i}Hl(n,s),l=s;for(r in l)if(l.hasOwnProperty(r)){var a=l[r];r==="style"?nf(t,a):r==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&ef(t,a)):r==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&ds(t,a):typeof a=="number"&&ds(t,""+a):r!=="suppressContentEditableWarning"&&r!=="suppressHydrationWarning"&&r!=="autoFocus"&&(us.hasOwnProperty(r)?a!=null&&r==="onScroll"&&Z("scroll",t):a!=null&&Ya(t,r,a,o))}switch(n){case"input":Ys(t),ru(t,i,!1);break;case"textarea":Ys(t),lu(t);break;case"option":i.value!=null&&t.setAttribute("value",""+dn(i.value));break;case"select":t.multiple=!!i.multiple,r=i.value,r!=null?li(t,!!i.multiple,r,!1):i.defaultValue!=null&&li(t,!!i.multiple,i.defaultValue,!0);break;default:typeof s.onClick=="function"&&(t.onclick=qr)}switch(n){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return je(e),null;case 6:if(t&&e.stateNode!=null)Np(t,e,t.memoizedProps,i);else{if(typeof i!="string"&&e.stateNode===null)throw Error(N(166));if(n=Pn(ws.current),Pn(St.current),nr(e)){if(i=e.stateNode,n=e.memoizedProps,i[wt]=e,(r=i.nodeValue!==n)&&(t=Xe,t!==null))switch(t.tag){case 3:tr(i.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&tr(i.nodeValue,n,(t.mode&1)!==0)}r&&(e.flags|=4)}else i=(n.nodeType===9?n:n.ownerDocument).createTextNode(i),i[wt]=e,e.stateNode=i}return je(e),null;case 13:if(J(re),i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(te&&Ge!==null&&e.mode&1&&!(e.flags&128))$f(),gi(),e.flags|=98560,r=!1;else if(r=nr(e),i!==null&&i.dehydrated!==null){if(t===null){if(!r)throw Error(N(318));if(r=e.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(N(317));r[wt]=e}else gi(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;je(e),r=!1}else ut!==null&&(ka(ut),ut=null),r=!0;if(!r)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(i=i!==null,i!==(t!==null&&t.memoizedState!==null)&&i&&(e.child.flags|=8192,e.mode&1&&(t===null||re.current&1?me===0&&(me=3):Pc())),e.updateQueue!==null&&(e.flags|=4),je(e),null);case 4:return yi(),ma(t,e),t===null&&ys(e.stateNode.containerInfo),je(e),null;case 10:return hc(e.type._context),je(e),null;case 17:return We(e.type)&&Zr(),je(e),null;case 19:if(J(re),r=e.memoizedState,r===null)return je(e),null;if(i=(e.flags&128)!==0,o=r.rendering,o===null)if(i)Ti(r,!1);else{if(me!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(o=ro(t),o!==null){for(e.flags|=128,Ti(r,!1),i=o.updateQueue,i!==null&&(e.updateQueue=i,e.flags|=4),e.subtreeFlags=0,i=n,n=e.child;n!==null;)r=n,t=i,r.flags&=14680066,o=r.alternate,o===null?(r.childLanes=0,r.lanes=t,r.child=null,r.subtreeFlags=0,r.memoizedProps=null,r.memoizedState=null,r.updateQueue=null,r.dependencies=null,r.stateNode=null):(r.childLanes=o.childLanes,r.lanes=o.lanes,r.child=o.child,r.subtreeFlags=0,r.deletions=null,r.memoizedProps=o.memoizedProps,r.memoizedState=o.memoizedState,r.updateQueue=o.updateQueue,r.type=o.type,t=o.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return X(re,re.current&1|2),e.child}t=t.sibling}r.tail!==null&&de()>bi&&(e.flags|=128,i=!0,Ti(r,!1),e.lanes=4194304)}else{if(!i)if(t=ro(o),t!==null){if(e.flags|=128,i=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),Ti(r,!0),r.tail===null&&r.tailMode==="hidden"&&!o.alternate&&!te)return je(e),null}else 2*de()-r.renderingStartTime>bi&&n!==1073741824&&(e.flags|=128,i=!0,Ti(r,!1),e.lanes=4194304);r.isBackwards?(o.sibling=e.child,e.child=o):(n=r.last,n!==null?n.sibling=o:e.child=o,r.last=o)}return r.tail!==null?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.renderingStartTime=de(),e.sibling=null,n=re.current,X(re,i?n&1|2:n&1),e):(je(e),null);case 22:case 23:return Cc(),i=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==i&&(e.flags|=8192),i&&e.mode&1?Ke&1073741824&&(je(e),e.subtreeFlags&6&&(e.flags|=8192)):je(e),null;case 24:return null;case 25:return null}throw Error(N(156,e.tag))}function nx(t,e){switch(ac(e),e.tag){case 1:return We(e.type)&&Zr(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return yi(),J(He),J(Ee),xc(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return gc(e),null;case 13:if(J(re),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(N(340));gi()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return J(re),null;case 4:return yi(),null;case 10:return hc(e.type._context),null;case 22:case 23:return Cc(),null;case 24:return null;default:return null}}var rr=!1,Ce=!1,ix=typeof WeakSet=="function"?WeakSet:Set,L=null;function ri(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(i){ce(t,e,i)}else n.current=null}function ga(t,e,n){try{n()}catch(i){ce(t,e,i)}}var Qu=!1;function sx(t,e){if(Jl=Gr,t=Lf(),oc(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var s=i.anchorOffset,r=i.focusNode;i=i.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,d=0,h=t,f=null;t:for(;;){for(var p;h!==n||s!==0&&h.nodeType!==3||(l=o+s),h!==r||i!==0&&h.nodeType!==3||(a=o+i),h.nodeType===3&&(o+=h.nodeValue.length),(p=h.firstChild)!==null;)f=h,h=p;for(;;){if(h===t)break t;if(f===n&&++u===s&&(l=o),f===r&&++d===i&&(a=o),(p=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=p}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(ea={focusedElem:t,selectionRange:n},Gr=!1,L=e;L!==null;)if(e=L,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,L=t;else for(;L!==null;){e=L;try{var g=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var x=g.memoizedProps,b=g.memoizedState,m=e.stateNode,y=m.getSnapshotBeforeUpdate(e.elementType===e.type?x:at(e.type,x),b);m.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var v=e.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(_){ce(e,e.return,_)}if(t=e.sibling,t!==null){t.return=e.return,L=t;break}L=e.return}return g=Qu,Qu=!1,g}function ns(t,e,n){var i=e.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var s=i=i.next;do{if((s.tag&t)===t){var r=s.destroy;s.destroy=void 0,r!==void 0&&ga(e,n,r)}s=s.next}while(s!==i)}}function To(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var i=n.create;n.destroy=i()}n=n.next}while(n!==e)}}function xa(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function Mp(t){var e=t.alternate;e!==null&&(t.alternate=null,Mp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[wt],delete e[bs],delete e[ia],delete e[B0],delete e[V0])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Cp(t){return t.tag===5||t.tag===3||t.tag===4}function qu(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Cp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ya(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=qr));else if(i!==4&&(t=t.child,t!==null))for(ya(t,e,n),t=t.sibling;t!==null;)ya(t,e,n),t=t.sibling}function va(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(i!==4&&(t=t.child,t!==null))for(va(t,e,n),t=t.sibling;t!==null;)va(t,e,n),t=t.sibling}var be=null,ct=!1;function $t(t,e,n){for(n=n.child;n!==null;)Pp(t,e,n),n=n.sibling}function Pp(t,e,n){if(kt&&typeof kt.onCommitFiberUnmount=="function")try{kt.onCommitFiberUnmount(jo,n)}catch{}switch(n.tag){case 5:Ce||ri(n,e);case 6:var i=be,s=ct;be=null,$t(t,e,n),be=i,ct=s,be!==null&&(ct?(t=be,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):be.removeChild(n.stateNode));break;case 18:be!==null&&(ct?(t=be,n=n.stateNode,t.nodeType===8?al(t.parentNode,n):t.nodeType===1&&al(t,n),ms(t)):al(be,n.stateNode));break;case 4:i=be,s=ct,be=n.stateNode.containerInfo,ct=!0,$t(t,e,n),be=i,ct=s;break;case 0:case 11:case 14:case 15:if(!Ce&&(i=n.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){s=i=i.next;do{var r=s,o=r.destroy;r=r.tag,o!==void 0&&(r&2||r&4)&&ga(n,e,o),s=s.next}while(s!==i)}$t(t,e,n);break;case 1:if(!Ce&&(ri(n,e),i=n.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=n.memoizedProps,i.state=n.memoizedState,i.componentWillUnmount()}catch(l){ce(n,e,l)}$t(t,e,n);break;case 21:$t(t,e,n);break;case 22:n.mode&1?(Ce=(i=Ce)||n.memoizedState!==null,$t(t,e,n),Ce=i):$t(t,e,n);break;default:$t(t,e,n)}}function Zu(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new ix),e.forEach(function(i){var s=fx.bind(null,t,i);n.has(i)||(n.add(i),i.then(s,s))})}}function lt(t,e){var n=e.deletions;if(n!==null)for(var i=0;i<n.length;i++){var s=n[i];try{var r=t,o=e,l=o;e:for(;l!==null;){switch(l.tag){case 5:be=l.stateNode,ct=!1;break e;case 3:be=l.stateNode.containerInfo,ct=!0;break e;case 4:be=l.stateNode.containerInfo,ct=!0;break e}l=l.return}if(be===null)throw Error(N(160));Pp(r,o,s),be=null,ct=!1;var a=s.alternate;a!==null&&(a.return=null),s.return=null}catch(u){ce(s,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)Ep(e,t),e=e.sibling}function Ep(t,e){var n=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(lt(e,t),yt(t),i&4){try{ns(3,t,t.return),To(3,t)}catch(x){ce(t,t.return,x)}try{ns(5,t,t.return)}catch(x){ce(t,t.return,x)}}break;case 1:lt(e,t),yt(t),i&512&&n!==null&&ri(n,n.return);break;case 5:if(lt(e,t),yt(t),i&512&&n!==null&&ri(n,n.return),t.flags&32){var s=t.stateNode;try{ds(s,"")}catch(x){ce(t,t.return,x)}}if(i&4&&(s=t.stateNode,s!=null)){var r=t.memoizedProps,o=n!==null?n.memoizedProps:r,l=t.type,a=t.updateQueue;if(t.updateQueue=null,a!==null)try{l==="input"&&r.type==="radio"&&r.name!=null&&qh(s,r),Wl(l,o);var u=Wl(l,r);for(o=0;o<a.length;o+=2){var d=a[o],h=a[o+1];d==="style"?nf(s,h):d==="dangerouslySetInnerHTML"?ef(s,h):d==="children"?ds(s,h):Ya(s,d,h,u)}switch(l){case"input":zl(s,r);break;case"textarea":Zh(s,r);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!r.multiple;var p=r.value;p!=null?li(s,!!r.multiple,p,!1):f!==!!r.multiple&&(r.defaultValue!=null?li(s,!!r.multiple,r.defaultValue,!0):li(s,!!r.multiple,r.multiple?[]:"",!1))}s[bs]=r}catch(x){ce(t,t.return,x)}}break;case 6:if(lt(e,t),yt(t),i&4){if(t.stateNode===null)throw Error(N(162));s=t.stateNode,r=t.memoizedProps;try{s.nodeValue=r}catch(x){ce(t,t.return,x)}}break;case 3:if(lt(e,t),yt(t),i&4&&n!==null&&n.memoizedState.isDehydrated)try{ms(e.containerInfo)}catch(x){ce(t,t.return,x)}break;case 4:lt(e,t),yt(t);break;case 13:lt(e,t),yt(t),s=t.child,s.flags&8192&&(r=s.memoizedState!==null,s.stateNode.isHidden=r,!r||s.alternate!==null&&s.alternate.memoizedState!==null||(Nc=de())),i&4&&Zu(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(Ce=(u=Ce)||d,lt(e,t),Ce=u):lt(e,t),yt(t),i&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!d&&t.mode&1)for(L=t,d=t.child;d!==null;){for(h=L=d;L!==null;){switch(f=L,p=f.child,f.tag){case 0:case 11:case 14:case 15:ns(4,f,f.return);break;case 1:ri(f,f.return);var g=f.stateNode;if(typeof g.componentWillUnmount=="function"){i=f,n=f.return;try{e=i,g.props=e.memoizedProps,g.state=e.memoizedState,g.componentWillUnmount()}catch(x){ce(i,n,x)}}break;case 5:ri(f,f.return);break;case 22:if(f.memoizedState!==null){ed(h);continue}}p!==null?(p.return=f,L=p):ed(h)}d=d.sibling}e:for(d=null,h=t;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,u?(r=s.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none"):(l=h.stateNode,a=h.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=tf("display",o))}catch(x){ce(t,t.return,x)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(x){ce(t,t.return,x)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===t)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===t)break e;for(;h.sibling===null;){if(h.return===null||h.return===t)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:lt(e,t),yt(t),i&4&&Zu(t);break;case 21:break;default:lt(e,t),yt(t)}}function yt(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(Cp(n)){var i=n;break e}n=n.return}throw Error(N(160))}switch(i.tag){case 5:var s=i.stateNode;i.flags&32&&(ds(s,""),i.flags&=-33);var r=qu(t);va(t,r,s);break;case 3:case 4:var o=i.stateNode.containerInfo,l=qu(t);ya(t,l,o);break;default:throw Error(N(161))}}catch(a){ce(t,t.return,a)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function rx(t,e,n){L=t,Lp(t)}function Lp(t,e,n){for(var i=(t.mode&1)!==0;L!==null;){var s=L,r=s.child;if(s.tag===22&&i){var o=s.memoizedState!==null||rr;if(!o){var l=s.alternate,a=l!==null&&l.memoizedState!==null||Ce;l=rr;var u=Ce;if(rr=o,(Ce=a)&&!u)for(L=s;L!==null;)o=L,a=o.child,o.tag===22&&o.memoizedState!==null?td(s):a!==null?(a.return=o,L=a):td(s);for(;r!==null;)L=r,Lp(r),r=r.sibling;L=s,rr=l,Ce=u}Ju(t)}else s.subtreeFlags&8772&&r!==null?(r.return=s,L=r):Ju(t)}}function Ju(t){for(;L!==null;){var e=L;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:Ce||To(5,e);break;case 1:var i=e.stateNode;if(e.flags&4&&!Ce)if(n===null)i.componentDidMount();else{var s=e.elementType===e.type?n.memoizedProps:at(e.type,n.memoizedProps);i.componentDidUpdate(s,n.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var r=e.updateQueue;r!==null&&Iu(e,r,i);break;case 3:var o=e.updateQueue;if(o!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}Iu(e,o,n)}break;case 5:var l=e.stateNode;if(n===null&&e.flags&4){n=l;var a=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&ms(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}Ce||e.flags&512&&xa(e)}catch(f){ce(e,e.return,f)}}if(e===t){L=null;break}if(n=e.sibling,n!==null){n.return=e.return,L=n;break}L=e.return}}function ed(t){for(;L!==null;){var e=L;if(e===t){L=null;break}var n=e.sibling;if(n!==null){n.return=e.return,L=n;break}L=e.return}}function td(t){for(;L!==null;){var e=L;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{To(4,e)}catch(a){ce(e,n,a)}break;case 1:var i=e.stateNode;if(typeof i.componentDidMount=="function"){var s=e.return;try{i.componentDidMount()}catch(a){ce(e,s,a)}}var r=e.return;try{xa(e)}catch(a){ce(e,r,a)}break;case 5:var o=e.return;try{xa(e)}catch(a){ce(e,o,a)}}}catch(a){ce(e,e.return,a)}if(e===t){L=null;break}var l=e.sibling;if(l!==null){l.return=e.return,L=l;break}L=e.return}}var ox=Math.ceil,ao=Wt.ReactCurrentDispatcher,Sc=Wt.ReactCurrentOwner,st=Wt.ReactCurrentBatchConfig,V=0,ve=null,fe=null,_e=0,Ke=0,oi=gn(0),me=0,Ns=null,zn=0,Ao=0,jc=0,is=null,Fe=null,Nc=0,bi=1/0,Et=null,co=!1,ba=null,ln=null,or=!1,Qt=null,uo=0,ss=0,_a=null,Lr=-1,Dr=0;function Oe(){return V&6?de():Lr!==-1?Lr:Lr=de()}function an(t){return t.mode&1?V&2&&_e!==0?_e&-_e:W0.transition!==null?(Dr===0&&(Dr=mf()),Dr):(t=U,t!==0||(t=window.event,t=t===void 0?16:wf(t.type)),t):1}function ft(t,e,n,i){if(50<ss)throw ss=0,_a=null,Error(N(185));Is(t,n,i),(!(V&2)||t!==ve)&&(t===ve&&(!(V&2)&&(Ao|=n),me===4&&Gt(t,_e)),$e(t,i),n===1&&V===0&&!(e.mode&1)&&(bi=de()+500,Eo&&xn()))}function $e(t,e){var n=t.callbackNode;Wg(t,e);var i=Yr(t,t===ve?_e:0);if(i===0)n!==null&&uu(n),t.callbackNode=null,t.callbackPriority=0;else if(e=i&-i,t.callbackPriority!==e){if(n!=null&&uu(n),e===1)t.tag===0?H0(nd.bind(null,t)):Vf(nd.bind(null,t)),z0(function(){!(V&6)&&xn()}),n=null;else{switch(gf(i)){case 1:n=Za;break;case 4:n=ff;break;case 16:n=Kr;break;case 536870912:n=pf;break;default:n=Kr}n=Fp(n,Dp.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function Dp(t,e){if(Lr=-1,Dr=0,V&6)throw Error(N(327));var n=t.callbackNode;if(hi()&&t.callbackNode!==n)return null;var i=Yr(t,t===ve?_e:0);if(i===0)return null;if(i&30||i&t.expiredLanes||e)e=ho(t,i);else{e=i;var s=V;V|=2;var r=Ap();(ve!==t||_e!==e)&&(Et=null,bi=de()+500,Ln(t,e));do try{cx();break}catch(l){Tp(t,l)}while(!0);dc(),ao.current=r,V=s,fe!==null?e=0:(ve=null,_e=0,e=me)}if(e!==0){if(e===2&&(s=Gl(t),s!==0&&(i=s,e=wa(t,s))),e===1)throw n=Ns,Ln(t,0),Gt(t,i),$e(t,de()),n;if(e===6)Gt(t,i);else{if(s=t.current.alternate,!(i&30)&&!lx(s)&&(e=ho(t,i),e===2&&(r=Gl(t),r!==0&&(i=r,e=wa(t,r))),e===1))throw n=Ns,Ln(t,0),Gt(t,i),$e(t,de()),n;switch(t.finishedWork=s,t.finishedLanes=i,e){case 0:case 1:throw Error(N(345));case 2:Sn(t,Fe,Et);break;case 3:if(Gt(t,i),(i&130023424)===i&&(e=Nc+500-de(),10<e)){if(Yr(t,0)!==0)break;if(s=t.suspendedLanes,(s&i)!==i){Oe(),t.pingedLanes|=t.suspendedLanes&s;break}t.timeoutHandle=na(Sn.bind(null,t,Fe,Et),e);break}Sn(t,Fe,Et);break;case 4:if(Gt(t,i),(i&4194240)===i)break;for(e=t.eventTimes,s=-1;0<i;){var o=31-ht(i);r=1<<o,o=e[o],o>s&&(s=o),i&=~r}if(i=s,i=de()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*ox(i/1960))-i,10<i){t.timeoutHandle=na(Sn.bind(null,t,Fe,Et),i);break}Sn(t,Fe,Et);break;case 5:Sn(t,Fe,Et);break;default:throw Error(N(329))}}}return $e(t,de()),t.callbackNode===n?Dp.bind(null,t):null}function wa(t,e){var n=is;return t.current.memoizedState.isDehydrated&&(Ln(t,e).flags|=256),t=ho(t,e),t!==2&&(e=Fe,Fe=n,e!==null&&ka(e)),t}function ka(t){Fe===null?Fe=t:Fe.push.apply(Fe,t)}function lx(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var i=0;i<n.length;i++){var s=n[i],r=s.getSnapshot;s=s.value;try{if(!mt(r(),s))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Gt(t,e){for(e&=~jc,e&=~Ao,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-ht(e),i=1<<n;t[n]=-1,e&=~i}}function nd(t){if(V&6)throw Error(N(327));hi();var e=Yr(t,0);if(!(e&1))return $e(t,de()),null;var n=ho(t,e);if(t.tag!==0&&n===2){var i=Gl(t);i!==0&&(e=i,n=wa(t,i))}if(n===1)throw n=Ns,Ln(t,0),Gt(t,e),$e(t,de()),n;if(n===6)throw Error(N(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,Sn(t,Fe,Et),$e(t,de()),null}function Mc(t,e){var n=V;V|=1;try{return t(e)}finally{V=n,V===0&&(bi=de()+500,Eo&&xn())}}function Fn(t){Qt!==null&&Qt.tag===0&&!(V&6)&&hi();var e=V;V|=1;var n=st.transition,i=U;try{if(st.transition=null,U=1,t)return t()}finally{U=i,st.transition=n,V=e,!(V&6)&&xn()}}function Cc(){Ke=oi.current,J(oi)}function Ln(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,I0(n)),fe!==null)for(n=fe.return;n!==null;){var i=n;switch(ac(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&Zr();break;case 3:yi(),J(He),J(Ee),xc();break;case 5:gc(i);break;case 4:yi();break;case 13:J(re);break;case 19:J(re);break;case 10:hc(i.type._context);break;case 22:case 23:Cc()}n=n.return}if(ve=t,fe=t=cn(t.current,null),_e=Ke=e,me=0,Ns=null,jc=Ao=zn=0,Fe=is=null,Cn!==null){for(e=0;e<Cn.length;e++)if(n=Cn[e],i=n.interleaved,i!==null){n.interleaved=null;var s=i.next,r=n.pending;if(r!==null){var o=r.next;r.next=s,i.next=o}n.pending=i}Cn=null}return t}function Tp(t,e){do{var n=fe;try{if(dc(),Cr.current=lo,oo){for(var i=oe.memoizedState;i!==null;){var s=i.queue;s!==null&&(s.pending=null),i=i.next}oo=!1}if(In=0,xe=pe=oe=null,ts=!1,ks=0,Sc.current=null,n===null||n.return===null){me=1,Ns=e,fe=null;break}e:{var r=t,o=n.return,l=n,a=e;if(e=_e,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=l,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=Wu(o);if(p!==null){p.flags&=-257,$u(p,o,l,r,e),p.mode&1&&Hu(r,u,e),e=p,a=u;var g=e.updateQueue;if(g===null){var x=new Set;x.add(a),e.updateQueue=x}else g.add(a);break e}else{if(!(e&1)){Hu(r,u,e),Pc();break e}a=Error(N(426))}}else if(te&&l.mode&1){var b=Wu(o);if(b!==null){!(b.flags&65536)&&(b.flags|=256),$u(b,o,l,r,e),cc(vi(a,l));break e}}r=a=vi(a,l),me!==4&&(me=2),is===null?is=[r]:is.push(r),r=o;do{switch(r.tag){case 3:r.flags|=65536,e&=-e,r.lanes|=e;var m=gp(r,a,e);Ru(r,m);break e;case 1:l=a;var y=r.type,v=r.stateNode;if(!(r.flags&128)&&(typeof y.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(ln===null||!ln.has(v)))){r.flags|=65536,e&=-e,r.lanes|=e;var _=xp(r,l,e);Ru(r,_);break e}}r=r.return}while(r!==null)}Rp(n)}catch(w){e=w,fe===n&&n!==null&&(fe=n=n.return);continue}break}while(!0)}function Ap(){var t=ao.current;return ao.current=lo,t===null?lo:t}function Pc(){(me===0||me===3||me===2)&&(me=4),ve===null||!(zn&268435455)&&!(Ao&268435455)||Gt(ve,_e)}function ho(t,e){var n=V;V|=2;var i=Ap();(ve!==t||_e!==e)&&(Et=null,Ln(t,e));do try{ax();break}catch(s){Tp(t,s)}while(!0);if(dc(),V=n,ao.current=i,fe!==null)throw Error(N(261));return ve=null,_e=0,me}function ax(){for(;fe!==null;)Op(fe)}function cx(){for(;fe!==null&&!Ag();)Op(fe)}function Op(t){var e=zp(t.alternate,t,Ke);t.memoizedProps=t.pendingProps,e===null?Rp(t):fe=e,Sc.current=null}function Rp(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=nx(n,e),n!==null){n.flags&=32767,fe=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{me=6,fe=null;return}}else if(n=tx(n,e,Ke),n!==null){fe=n;return}if(e=e.sibling,e!==null){fe=e;return}fe=e=t}while(e!==null);me===0&&(me=5)}function Sn(t,e,n){var i=U,s=st.transition;try{st.transition=null,U=1,ux(t,e,n,i)}finally{st.transition=s,U=i}return null}function ux(t,e,n,i){do hi();while(Qt!==null);if(V&6)throw Error(N(327));n=t.finishedWork;var s=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(N(177));t.callbackNode=null,t.callbackPriority=0;var r=n.lanes|n.childLanes;if($g(t,r),t===ve&&(fe=ve=null,_e=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||or||(or=!0,Fp(Kr,function(){return hi(),null})),r=(n.flags&15990)!==0,n.subtreeFlags&15990||r){r=st.transition,st.transition=null;var o=U;U=1;var l=V;V|=4,Sc.current=null,sx(t,n),Ep(n,t),E0(ea),Gr=!!Jl,ea=Jl=null,t.current=n,rx(n),Og(),V=l,U=o,st.transition=r}else t.current=n;if(or&&(or=!1,Qt=t,uo=s),r=t.pendingLanes,r===0&&(ln=null),zg(n.stateNode),$e(t,de()),e!==null)for(i=t.onRecoverableError,n=0;n<e.length;n++)s=e[n],i(s.value,{componentStack:s.stack,digest:s.digest});if(co)throw co=!1,t=ba,ba=null,t;return uo&1&&t.tag!==0&&hi(),r=t.pendingLanes,r&1?t===_a?ss++:(ss=0,_a=t):ss=0,xn(),null}function hi(){if(Qt!==null){var t=gf(uo),e=st.transition,n=U;try{if(st.transition=null,U=16>t?16:t,Qt===null)var i=!1;else{if(t=Qt,Qt=null,uo=0,V&6)throw Error(N(331));var s=V;for(V|=4,L=t.current;L!==null;){var r=L,o=r.child;if(L.flags&16){var l=r.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(L=u;L!==null;){var d=L;switch(d.tag){case 0:case 11:case 15:ns(8,d,r)}var h=d.child;if(h!==null)h.return=d,L=h;else for(;L!==null;){d=L;var f=d.sibling,p=d.return;if(Mp(d),d===u){L=null;break}if(f!==null){f.return=p,L=f;break}L=p}}}var g=r.alternate;if(g!==null){var x=g.child;if(x!==null){g.child=null;do{var b=x.sibling;x.sibling=null,x=b}while(x!==null)}}L=r}}if(r.subtreeFlags&2064&&o!==null)o.return=r,L=o;else e:for(;L!==null;){if(r=L,r.flags&2048)switch(r.tag){case 0:case 11:case 15:ns(9,r,r.return)}var m=r.sibling;if(m!==null){m.return=r.return,L=m;break e}L=r.return}}var y=t.current;for(L=y;L!==null;){o=L;var v=o.child;if(o.subtreeFlags&2064&&v!==null)v.return=o,L=v;else e:for(o=y;L!==null;){if(l=L,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:To(9,l)}}catch(w){ce(l,l.return,w)}if(l===o){L=null;break e}var _=l.sibling;if(_!==null){_.return=l.return,L=_;break e}L=l.return}}if(V=s,xn(),kt&&typeof kt.onPostCommitFiberRoot=="function")try{kt.onPostCommitFiberRoot(jo,t)}catch{}i=!0}return i}finally{U=n,st.transition=e}}return!1}function id(t,e,n){e=vi(n,e),e=gp(t,e,1),t=on(t,e,1),e=Oe(),t!==null&&(Is(t,1,e),$e(t,e))}function ce(t,e,n){if(t.tag===3)id(t,t,n);else for(;e!==null;){if(e.tag===3){id(e,t,n);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(ln===null||!ln.has(i))){t=vi(n,t),t=xp(e,t,1),e=on(e,t,1),t=Oe(),e!==null&&(Is(e,1,t),$e(e,t));break}}e=e.return}}function dx(t,e,n){var i=t.pingCache;i!==null&&i.delete(e),e=Oe(),t.pingedLanes|=t.suspendedLanes&n,ve===t&&(_e&n)===n&&(me===4||me===3&&(_e&130023424)===_e&&500>de()-Nc?Ln(t,0):jc|=n),$e(t,e)}function Ip(t,e){e===0&&(t.mode&1?(e=Qs,Qs<<=1,!(Qs&130023424)&&(Qs=4194304)):e=1);var n=Oe();t=Vt(t,e),t!==null&&(Is(t,e,n),$e(t,n))}function hx(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Ip(t,n)}function fx(t,e){var n=0;switch(t.tag){case 13:var i=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:i=t.stateNode;break;default:throw Error(N(314))}i!==null&&i.delete(e),Ip(t,n)}var zp;zp=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||He.current)Ve=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Ve=!1,ex(t,e,n);Ve=!!(t.flags&131072)}else Ve=!1,te&&e.flags&1048576&&Hf(e,to,e.index);switch(e.lanes=0,e.tag){case 2:var i=e.type;Er(t,e),t=e.pendingProps;var s=mi(e,Ee.current);di(e,n),s=vc(null,e,i,t,s,n);var r=bc();return e.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,We(i)?(r=!0,Jr(e)):r=!1,e.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,pc(e),s.updater=Do,e.stateNode=s,s._reactInternals=e,ca(e,i,t,n),e=ha(null,e,i,!0,r,n)):(e.tag=0,te&&r&&lc(e),Ae(null,e,s,n),e=e.child),e;case 16:i=e.elementType;e:{switch(Er(t,e),t=e.pendingProps,s=i._init,i=s(i._payload),e.type=i,s=e.tag=mx(i),t=at(i,t),s){case 0:e=da(null,e,i,t,n);break e;case 1:e=Yu(null,e,i,t,n);break e;case 11:e=Uu(null,e,i,t,n);break e;case 14:e=Ku(null,e,i,at(i.type,t),n);break e}throw Error(N(306,i,""))}return e;case 0:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:at(i,s),da(t,e,i,s,n);case 1:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:at(i,s),Yu(t,e,i,s,n);case 3:e:{if(_p(e),t===null)throw Error(N(387));i=e.pendingProps,r=e.memoizedState,s=r.element,Gf(t,e),so(e,i,null,n);var o=e.memoizedState;if(i=o.element,r.isDehydrated)if(r={element:i,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){s=vi(Error(N(423)),e),e=Gu(t,e,i,n,s);break e}else if(i!==s){s=vi(Error(N(424)),e),e=Gu(t,e,i,n,s);break e}else for(Ge=rn(e.stateNode.containerInfo.firstChild),Xe=e,te=!0,ut=null,n=Kf(e,null,i,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(gi(),i===s){e=Ht(t,e,n);break e}Ae(t,e,i,n)}e=e.child}return e;case 5:return Xf(e),t===null&&oa(e),i=e.type,s=e.pendingProps,r=t!==null?t.memoizedProps:null,o=s.children,ta(i,s)?o=null:r!==null&&ta(i,r)&&(e.flags|=32),bp(t,e),Ae(t,e,o,n),e.child;case 6:return t===null&&oa(e),null;case 13:return wp(t,e,n);case 4:return mc(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=xi(e,null,i,n):Ae(t,e,i,n),e.child;case 11:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:at(i,s),Uu(t,e,i,s,n);case 7:return Ae(t,e,e.pendingProps,n),e.child;case 8:return Ae(t,e,e.pendingProps.children,n),e.child;case 12:return Ae(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(i=e.type._context,s=e.pendingProps,r=e.memoizedProps,o=s.value,X(no,i._currentValue),i._currentValue=o,r!==null)if(mt(r.value,o)){if(r.children===s.children&&!He.current){e=Ht(t,e,n);break e}}else for(r=e.child,r!==null&&(r.return=e);r!==null;){var l=r.dependencies;if(l!==null){o=r.child;for(var a=l.firstContext;a!==null;){if(a.context===i){if(r.tag===1){a=zt(-1,n&-n),a.tag=2;var u=r.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}r.lanes|=n,a=r.alternate,a!==null&&(a.lanes|=n),la(r.return,n,e),l.lanes|=n;break}a=a.next}}else if(r.tag===10)o=r.type===e.type?null:r.child;else if(r.tag===18){if(o=r.return,o===null)throw Error(N(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),la(o,n,e),o=r.sibling}else o=r.child;if(o!==null)o.return=r;else for(o=r;o!==null;){if(o===e){o=null;break}if(r=o.sibling,r!==null){r.return=o.return,o=r;break}o=o.return}r=o}Ae(t,e,s.children,n),e=e.child}return e;case 9:return s=e.type,i=e.pendingProps.children,di(e,n),s=rt(s),i=i(s),e.flags|=1,Ae(t,e,i,n),e.child;case 14:return i=e.type,s=at(i,e.pendingProps),s=at(i.type,s),Ku(t,e,i,s,n);case 15:return yp(t,e,e.type,e.pendingProps,n);case 17:return i=e.type,s=e.pendingProps,s=e.elementType===i?s:at(i,s),Er(t,e),e.tag=1,We(i)?(t=!0,Jr(e)):t=!1,di(e,n),mp(e,i,s),ca(e,i,s,n),ha(null,e,i,!0,t,n);case 19:return kp(t,e,n);case 22:return vp(t,e,n)}throw Error(N(156,e.tag))};function Fp(t,e){return hf(t,e)}function px(t,e,n,i){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function it(t,e,n,i){return new px(t,e,n,i)}function Ec(t){return t=t.prototype,!(!t||!t.isReactComponent)}function mx(t){if(typeof t=="function")return Ec(t)?1:0;if(t!=null){if(t=t.$$typeof,t===Xa)return 11;if(t===Qa)return 14}return 2}function cn(t,e){var n=t.alternate;return n===null?(n=it(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function Tr(t,e,n,i,s,r){var o=2;if(i=t,typeof t=="function")Ec(t)&&(o=1);else if(typeof t=="string")o=5;else e:switch(t){case Qn:return Dn(n.children,s,r,e);case Ga:o=8,s|=8;break;case Tl:return t=it(12,n,e,s|2),t.elementType=Tl,t.lanes=r,t;case Al:return t=it(13,n,e,s),t.elementType=Al,t.lanes=r,t;case Ol:return t=it(19,n,e,s),t.elementType=Ol,t.lanes=r,t;case Gh:return Oo(n,s,r,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Kh:o=10;break e;case Yh:o=9;break e;case Xa:o=11;break e;case Qa:o=14;break e;case Ut:o=16,i=null;break e}throw Error(N(130,t==null?t:typeof t,""))}return e=it(o,n,e,s),e.elementType=t,e.type=i,e.lanes=r,e}function Dn(t,e,n,i){return t=it(7,t,i,e),t.lanes=n,t}function Oo(t,e,n,i){return t=it(22,t,i,e),t.elementType=Gh,t.lanes=n,t.stateNode={isHidden:!1},t}function gl(t,e,n){return t=it(6,t,null,e),t.lanes=n,t}function xl(t,e,n){return e=it(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function gx(t,e,n,i,s){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=qo(0),this.expirationTimes=qo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=qo(0),this.identifierPrefix=i,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Lc(t,e,n,i,s,r,o,l,a){return t=new gx(t,e,n,l,a),e===1?(e=1,r===!0&&(e|=8)):e=0,r=it(3,null,null,e),t.current=r,r.stateNode=t,r.memoizedState={element:i,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},pc(r),t}function xx(t,e,n){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Xn,key:i==null?null:""+i,children:t,containerInfo:e,implementation:n}}function Bp(t){if(!t)return hn;t=t._reactInternals;e:{if(Wn(t)!==t||t.tag!==1)throw Error(N(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(We(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(N(171))}if(t.tag===1){var n=t.type;if(We(n))return Bf(t,n,e)}return e}function Vp(t,e,n,i,s,r,o,l,a){return t=Lc(n,i,!0,t,s,r,o,l,a),t.context=Bp(null),n=t.current,i=Oe(),s=an(n),r=zt(i,s),r.callback=e??null,on(n,r,s),t.current.lanes=s,Is(t,s,i),$e(t,i),t}function Ro(t,e,n,i){var s=e.current,r=Oe(),o=an(s);return n=Bp(n),e.context===null?e.context=n:e.pendingContext=n,e=zt(r,o),e.payload={element:t},i=i===void 0?null:i,i!==null&&(e.callback=i),t=on(s,e,o),t!==null&&(ft(t,s,o,r),Mr(t,s,o)),o}function fo(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function sd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Dc(t,e){sd(t,e),(t=t.alternate)&&sd(t,e)}function yx(){return null}var Hp=typeof reportError=="function"?reportError:function(t){console.error(t)};function Tc(t){this._internalRoot=t}Io.prototype.render=Tc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(N(409));Ro(t,e,null,null)};Io.prototype.unmount=Tc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Fn(function(){Ro(null,t,null,null)}),e[Bt]=null}};function Io(t){this._internalRoot=t}Io.prototype.unstable_scheduleHydration=function(t){if(t){var e=vf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Yt.length&&e!==0&&e<Yt[n].priority;n++);Yt.splice(n,0,t),n===0&&_f(t)}};function Ac(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function zo(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function rd(){}function vx(t,e,n,i,s){if(s){if(typeof i=="function"){var r=i;i=function(){var u=fo(o);r.call(u)}}var o=Vp(e,i,t,0,null,!1,!1,"",rd);return t._reactRootContainer=o,t[Bt]=o.current,ys(t.nodeType===8?t.parentNode:t),Fn(),o}for(;s=t.lastChild;)t.removeChild(s);if(typeof i=="function"){var l=i;i=function(){var u=fo(a);l.call(u)}}var a=Lc(t,0,!1,null,null,!1,!1,"",rd);return t._reactRootContainer=a,t[Bt]=a.current,ys(t.nodeType===8?t.parentNode:t),Fn(function(){Ro(e,a,n,i)}),a}function Fo(t,e,n,i,s){var r=n._reactRootContainer;if(r){var o=r;if(typeof s=="function"){var l=s;s=function(){var a=fo(o);l.call(a)}}Ro(e,o,t,s)}else o=vx(n,e,t,s,i);return fo(o)}xf=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=Hi(e.pendingLanes);n!==0&&(Ja(e,n|1),$e(e,de()),!(V&6)&&(bi=de()+500,xn()))}break;case 13:Fn(function(){var i=Vt(t,1);if(i!==null){var s=Oe();ft(i,t,1,s)}}),Dc(t,1)}};ec=function(t){if(t.tag===13){var e=Vt(t,134217728);if(e!==null){var n=Oe();ft(e,t,134217728,n)}Dc(t,134217728)}};yf=function(t){if(t.tag===13){var e=an(t),n=Vt(t,e);if(n!==null){var i=Oe();ft(n,t,e,i)}Dc(t,e)}};vf=function(){return U};bf=function(t,e){var n=U;try{return U=t,e()}finally{U=n}};Ul=function(t,e,n){switch(e){case"input":if(zl(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var i=n[e];if(i!==t&&i.form===t.form){var s=Po(i);if(!s)throw Error(N(90));Qh(i),zl(i,s)}}}break;case"textarea":Zh(t,n);break;case"select":e=n.value,e!=null&&li(t,!!n.multiple,e,!1)}};of=Mc;lf=Fn;var bx={usingClientEntryPoint:!1,Events:[Fs,ei,Po,sf,rf,Mc]},Ai={findFiberByHostInstance:Mn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},_x={bundleType:Ai.bundleType,version:Ai.version,rendererPackageName:Ai.rendererPackageName,rendererConfig:Ai.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Wt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=uf(t),t===null?null:t.stateNode},findFiberByHostInstance:Ai.findFiberByHostInstance||yx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var lr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!lr.isDisabled&&lr.supportsFiber)try{jo=lr.inject(_x),kt=lr}catch{}}qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=bx;qe.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ac(e))throw Error(N(200));return xx(t,e,null,n)};qe.createRoot=function(t,e){if(!Ac(t))throw Error(N(299));var n=!1,i="",s=Hp;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onRecoverableError!==void 0&&(s=e.onRecoverableError)),e=Lc(t,1,!1,null,null,n,!1,i,s),t[Bt]=e.current,ys(t.nodeType===8?t.parentNode:t),new Tc(e)};qe.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(N(188)):(t=Object.keys(t).join(","),Error(N(268,t)));return t=uf(e),t=t===null?null:t.stateNode,t};qe.flushSync=function(t){return Fn(t)};qe.hydrate=function(t,e,n){if(!zo(e))throw Error(N(200));return Fo(null,t,e,!0,n)};qe.hydrateRoot=function(t,e,n){if(!Ac(t))throw Error(N(405));var i=n!=null&&n.hydratedSources||null,s=!1,r="",o=Hp;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),e=Vp(e,null,t,1,n??null,s,!1,r,o),t[Bt]=e.current,ys(t),i)for(t=0;t<i.length;t++)n=i[t],s=n._getVersion,s=s(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,s]:e.mutableSourceEagerHydrationData.push(n,s);return new Io(e)};qe.render=function(t,e,n){if(!zo(e))throw Error(N(200));return Fo(null,t,e,!1,n)};qe.unmountComponentAtNode=function(t){if(!zo(t))throw Error(N(40));return t._reactRootContainer?(Fn(function(){Fo(null,null,t,!1,function(){t._reactRootContainer=null,t[Bt]=null})}),!0):!1};qe.unstable_batchedUpdates=Mc;qe.unstable_renderSubtreeIntoContainer=function(t,e,n,i){if(!zo(n))throw Error(N(200));if(t==null||t._reactInternals===void 0)throw Error(N(38));return Fo(t,e,n,!1,i)};qe.version="18.3.1-next-f1338f8080-20240426";function Wp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Wp)}catch(t){console.error(t)}}Wp(),Hh.exports=qe;var wx=Hh.exports,$p,od=wx;$p=od.createRoot,od.hydrateRoot;const Je=()=>{const[t,e]=T.useState("en"),[n,i]=T.useState({}),[s,r]=T.useState(!0),o=async u=>{r(!0);try{const h=await(await fetch(`/lang/${u}.json`)).json();i(h),e(u),document.documentElement.setAttribute("dir",u==="ar"?"rtl":"ltr"),document.documentElement.setAttribute("lang",u),localStorage.setItem("preferred-language",u)}catch(d){console.error("Error loading language:",d)}finally{r(!1)}};return T.useEffect(()=>{const u=localStorage.getItem("preferred-language");o(u||"en")},[]),{currentLanguage:t,isLoading:s,toggleLanguage:()=>{o(t==="en"?"ar":"en")},t:u=>n[u]||u,isRTL:t==="ar"}};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var kx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sx=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),ae=(t,e)=>{const n=T.forwardRef(({color:i="currentColor",size:s=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:l="",children:a,...u},d)=>T.createElement("svg",{ref:d,...kx,width:s,height:s,stroke:i,strokeWidth:o?Number(r)*24/Number(s):r,className:["lucide",`lucide-${Sx(t)}`,l].join(" "),...u},[...e.map(([h,f])=>T.createElement(h,f)),...Array.isArray(a)?a:[a]]));return n.displayName=`${t}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Up=ae("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const po=ae("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kp=ae("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yp=ae("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=ae("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nx=ae("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gn=ae("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ld=ae("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ad=ae("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mx=ae("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cx=ae("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Px=ae("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sa=ae("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ex=ae("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lx=ae("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dx=ae("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tx=ae("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ax=ae("Stethoscope",[["path",{d:"M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3",key:"1jd90r"}],["path",{d:"M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4",key:"126ukv"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gp=ae("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xp=ae("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ox=ae("Waves",[["path",{d:"M2 6c.6.5 1.2 1 2.5 1C7 7 7 5 9.5 5c2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"knzxuh"}],["path",{d:"M2 12c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"2jd2cc"}],["path",{d:"M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"rd2r6e"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=ae("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mo=ae("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),Ix=()=>{const{t,toggleLanguage:e,currentLanguage:n,isRTL:i}=Je();return c.jsx("header",{className:"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg",children:c.jsx("div",{className:"container mx-auto px-4 py-4",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Yp,{className:"h-8 w-8 animate-pulse"}),c.jsx("h1",{className:"text-2xl font-bold","data-lang-key":"app_title",children:t("app_title")})]}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("button",{onClick:e,className:`flex items-center space-x-2 px-4 py-2 rounded-lg bg-white/10 
                       hover:bg-white/20 transition-all duration-300 group`,children:[c.jsx(Px,{className:"h-4 w-4 group-hover:rotate-180 transition-transform duration-300"}),c.jsx("span",{className:"font-medium",children:t("language_toggle")})]}),c.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[c.jsx("span",{children:t("progress_indicator")}),c.jsx("div",{className:"w-20 h-2 bg-white/20 rounded-full overflow-hidden",children:c.jsx("div",{className:"h-full bg-white rounded-full w-1/3 transition-all duration-500"})})]})]})]})})})},Qp=[{id:"ecg-fundamentals",titleKey:"ecg_fundamentals_title",difficulty:"beginner",category:"ecg",icon:"💓",description:"Learn the basics of ECG and electrode placement",hasTheory:!0,hasSimulation:!0,hasAnalysis:!1},{id:"ecg-12-leads",titleKey:"ecg_12_leads_title",difficulty:"beginner",category:"ecg",icon:"📊",description:"Understand standard 12-lead ECG monitoring",hasTheory:!0,hasSimulation:!0,hasAnalysis:!1},{id:"ecg-artifacts",titleKey:"ecg_artifacts_title",difficulty:"intermediate",category:"ecg",icon:"⚡",description:"Identify and analyze ECG artifacts",hasTheory:!0,hasSimulation:!0,hasAnalysis:!0},{id:"ecg-pathology",titleKey:"ecg_pathology_title",difficulty:"advanced",category:"ecg",icon:"🔬",description:"Recognize pathological ECG patterns",hasTheory:!0,hasSimulation:!0,hasAnalysis:!0},{id:"eeg-fundamentals",titleKey:"eeg_fundamentals_title",difficulty:"beginner",category:"eeg",icon:"🧠",description:"Introduction to EEG and brainwave patterns",hasTheory:!0,hasSimulation:!0,hasAnalysis:!1},{id:"eeg-analysis",titleKey:"eeg_analysis_title",difficulty:"advanced",category:"eeg",icon:"📈",description:"Advanced EEG signal analysis techniques",hasTheory:!0,hasSimulation:!0,hasAnalysis:!0},{id:"bp-measurement",titleKey:"bp_measurement_title",difficulty:"beginner",category:"bp",icon:"🩺",description:"Blood pressure measurement techniques",hasTheory:!0,hasSimulation:!0,hasAnalysis:!1},{id:"bp-conditions",titleKey:"bp_conditions_title",difficulty:"intermediate",category:"bp",icon:"💊",description:"Hypertension and hypotension analysis",hasTheory:!0,hasSimulation:!0,hasAnalysis:!0}],zx=({selectedModule:t,onModuleSelect:e,isCollapsed:n,onToggleCollapse:i})=>{const{t:s,isRTL:r}=Je(),[o,l]=T.useState("all"),a=Qp.filter(d=>o==="all"||d.difficulty===o),u=d=>{switch(d){case"beginner":return"bg-green-100 text-green-800";case"intermediate":return"bg-yellow-100 text-yellow-800";case"advanced":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return c.jsx("nav",{className:`bg-white shadow-lg transition-all duration-300 ${n?"w-16":"w-80"} ${r?"border-l":"border-r"} border-gray-200`,children:c.jsxs("div",{className:"p-4",children:[c.jsxs("div",{className:"flex items-center justify-between mb-6",children:[!n&&c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(Cx,{className:"h-5 w-5 text-gray-500"}),c.jsx("h2",{className:"font-semibold text-gray-800",children:s("nav_filter_all")})]}),c.jsx("button",{onClick:i,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors",children:n?r?c.jsx(ld,{className:"h-5 w-5"}):c.jsx(ad,{className:"h-5 w-5"}):r?c.jsx(ad,{className:"h-5 w-5"}):c.jsx(ld,{className:"h-5 w-5"})})]}),!n&&c.jsx("div",{className:"mb-6",children:c.jsx("div",{className:"flex flex-wrap gap-2",children:["all","beginner","intermediate","advanced"].map(d=>c.jsx("button",{onClick:()=>l(d),className:`px-3 py-1 rounded-full text-sm font-medium transition-all ${o===d?"bg-blue-500 text-white shadow-md":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:s(`nav_filter_${d}`)},d))})}),c.jsx("div",{className:"space-y-2",children:a.map(d=>c.jsx("button",{onClick:()=>e(d.id),className:`w-full text-left p-3 rounded-lg transition-all duration-200 group ${t===d.id?"bg-blue-50 border-2 border-blue-200 shadow-md":"hover:bg-gray-50 border-2 border-transparent"}`,children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:"text-2xl group-hover:animate-pulse",children:d.icon}),!n&&c.jsxs("div",{className:"flex-1 min-w-0",children:[c.jsx("h3",{className:"font-medium text-gray-900 truncate",children:s(d.titleKey)}),c.jsx("p",{className:"text-sm text-gray-500 truncate",children:d.description}),c.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${u(d.difficulty)}`,children:s(`nav_filter_${d.difficulty}`)})]})]})},d.id))})]})})},Fx=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState(null),[s,r]=T.useState({}),[o,l]=T.useState(""),[a,u]=T.useState(!1),d=[{id:"ra",label:"RA",name:"Right Arm",position:{x:20,y:25}},{id:"la",label:"LA",name:"Left Arm",position:{x:80,y:25}},{id:"ll",label:"LL",name:"Left Leg",position:{x:80,y:75}},{id:"rl",label:"RL",name:"Right Leg",position:{x:20,y:75}}],h=(m,y)=>{i(y),m.dataTransfer.setData("text/plain",y)},f=(m,y)=>{m.preventDefault(),m.dataTransfer.getData("text/plain")===y?(r(_=>({..._,[y]:!0})),l(e("correct_placement")),u(!0)):(l(e("incorrect_placement")),u(!0)),setTimeout(()=>u(!1),2e3),i(null)},p=m=>{m.preventDefault()},g=()=>{r({}),l(""),u(!1)},x=()=>c.jsx("div",{className:"p-6 max-w-4xl mx-auto",children:c.jsxs("div",{className:"prose prose-lg max-w-none",children:[c.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-800",children:"ECG Fundamentals"}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-8 mb-8",children:[c.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-blue-800",children:"What is ECG?"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"Electrocardiography (ECG) is a medical test that records the electrical activity of the heart over time. It provides valuable information about heart rhythm, rate, and the presence of any abnormalities."}),c.jsx("div",{className:"animate-pulse",children:c.jsx("div",{className:"w-16 h-16 bg-red-200 rounded-full mx-auto flex items-center justify-center",children:c.jsx("span",{className:"text-2xl",children:"💓"})})})]}),c.jsxs("div",{className:"bg-green-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-green-800",children:"Einthoven's Triangle"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"The standard limb leads (I, II, III) form Einthoven's triangle, which provides a comprehensive view of the heart's electrical activity from different angles."}),c.jsx("div",{className:"flex justify-center",children:c.jsxs("svg",{width:"120",height:"100",viewBox:"0 0 120 100",className:"stroke-current text-green-600",children:[c.jsx("polygon",{points:"60,10 20,70 100,70",fill:"none",strokeWidth:"2"}),c.jsx("text",{x:"60",y:"8",textAnchor:"middle",className:"text-sm font-medium",children:"LA"}),c.jsx("text",{x:"15",y:"75",textAnchor:"middle",className:"text-sm font-medium",children:"LL"}),c.jsx("text",{x:"105",y:"75",textAnchor:"middle",className:"text-sm font-medium",children:"RA"})]})})]})]}),c.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Key Concepts"}),c.jsxs("ul",{className:"space-y-2 text-gray-700",children:[c.jsxs("li",{className:"flex items-center space-x-2",children:[c.jsx(Gn,{className:"h-5 w-5 text-green-500"}),c.jsx("span",{children:"ECG measures electrical activity, not mechanical activity"})]}),c.jsxs("li",{className:"flex items-center space-x-2",children:[c.jsx(Gn,{className:"h-5 w-5 text-green-500"}),c.jsx("span",{children:"Standard paper speed is 25 mm/s"})]}),c.jsxs("li",{className:"flex items-center space-x-2",children:[c.jsx(Gn,{className:"h-5 w-5 text-green-500"}),c.jsx("span",{children:"Standard calibration is 10 mm/mV"})]}),c.jsxs("li",{className:"flex items-center space-x-2",children:[c.jsx(Gn,{className:"h-5 w-5 text-green-500"}),c.jsx("span",{children:"Normal heart rate is 60-100 bpm"})]})]})]})]})}),b=()=>c.jsx("div",{className:"p-6 max-w-4xl mx-auto",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-6",children:[c.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:e("electrode_placement")}),c.jsxs("button",{onClick:g,className:"flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[c.jsx(Tx,{className:"h-4 w-4"}),c.jsx("span",{children:e("reset_simulation")})]})]}),c.jsx("p",{className:"text-gray-600 mb-6",children:e("drag_instruction")}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[c.jsxs("div",{className:"space-y-4",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Electrodes"}),c.jsx("div",{className:"grid grid-cols-2 gap-4",children:d.map(m=>c.jsx("div",{draggable:!0,onDragStart:y=>h(y,m.id),className:`p-4 rounded-lg border-2 border-dashed cursor-move transition-all ${s[m.id]?"bg-green-100 border-green-300 opacity-50":"bg-blue-50 border-blue-300 hover:bg-blue-100"}`,children:c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"w-12 h-12 bg-blue-500 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold",children:m.label}),c.jsx("p",{className:"text-sm font-medium",children:m.name})]})},m.id))})]}),c.jsxs("div",{className:"bg-gray-100 rounded-lg p-6",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Human Torso"}),c.jsx("div",{className:"relative bg-white rounded-lg shadow-inner",style:{height:"300px"},children:c.jsxs("svg",{width:"100%",height:"100%",viewBox:"0 0 200 300",className:"absolute inset-0",children:[c.jsx("path",{d:"M50 50 Q50 40 60 40 L140 40 Q150 40 150 50 L150 100 Q150 110 140 110 L130 110 L130 200 Q130 210 120 210 L80 210 Q70 210 70 200 L70 110 L60 110 Q50 110 50 100 Z",fill:"#f3f4f6",stroke:"#d1d5db",strokeWidth:"2"}),d.map(m=>c.jsxs("g",{children:[c.jsx("circle",{cx:m.position.x*2,cy:m.position.y*3,r:"20",fill:s[m.id]?"#10b981":"#e5e7eb",stroke:s[m.id]?"#059669":"#9ca3af",strokeWidth:"2",className:"transition-all duration-300",onDragOver:p,onDrop:y=>f(y,m.id)}),c.jsx("text",{x:m.position.x*2,y:m.position.y*3+5,textAnchor:"middle",className:"text-sm font-bold fill-current",style:{pointerEvents:"none"},children:s[m.id]?m.label:"?"})]},m.id))]})})]})]}),a&&c.jsxs("div",{className:`mt-6 p-4 rounded-lg flex items-center space-x-2 ${o.includes("Correct")?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[o.includes("Correct")?c.jsx(Gn,{className:"h-5 w-5"}):c.jsx(Rx,{className:"h-5 w-5"}),c.jsx("span",{className:"font-medium",children:o})]})]})});return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&x(),t==="simulation"&&b()]})};/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function Vs(t){return t+.5|0}const qt=(t,e,n)=>Math.max(Math.min(t,n),e);function $i(t){return qt(Vs(t*2.55),0,255)}function un(t){return qt(Vs(t*255),0,255)}function Dt(t){return qt(Vs(t/2.55)/100,0,1)}function cd(t){return qt(Vs(t*100),0,100)}const et={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ja=[..."0123456789ABCDEF"],Bx=t=>ja[t&15],Vx=t=>ja[(t&240)>>4]+ja[t&15],ar=t=>(t&240)>>4===(t&15),Hx=t=>ar(t.r)&&ar(t.g)&&ar(t.b)&&ar(t.a);function Wx(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&et[t[1]]*17,g:255&et[t[2]]*17,b:255&et[t[3]]*17,a:e===5?et[t[4]]*17:255}:(e===7||e===9)&&(n={r:et[t[1]]<<4|et[t[2]],g:et[t[3]]<<4|et[t[4]],b:et[t[5]]<<4|et[t[6]],a:e===9?et[t[7]]<<4|et[t[8]]:255})),n}const $x=(t,e)=>t<255?e(t):"";function Ux(t){var e=Hx(t)?Bx:Vx;return t?"#"+e(t.r)+e(t.g)+e(t.b)+$x(t.a,e):void 0}const Kx=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function qp(t,e,n){const i=e*Math.min(n,1-n),s=(r,o=(r+t/30)%12)=>n-i*Math.max(Math.min(o-3,9-o,1),-1);return[s(0),s(8),s(4)]}function Yx(t,e,n){const i=(s,r=(s+t/60)%6)=>n-n*e*Math.max(Math.min(r,4-r,1),0);return[i(5),i(3),i(1)]}function Gx(t,e,n){const i=qp(t,1,.5);let s;for(e+n>1&&(s=1/(e+n),e*=s,n*=s),s=0;s<3;s++)i[s]*=1-e-n,i[s]+=e;return i}function Xx(t,e,n,i,s){return t===s?(e-n)/i+(e<n?6:0):e===s?(n-t)/i+2:(t-e)/i+4}function Oc(t){const n=t.r/255,i=t.g/255,s=t.b/255,r=Math.max(n,i,s),o=Math.min(n,i,s),l=(r+o)/2;let a,u,d;return r!==o&&(d=r-o,u=l>.5?d/(2-r-o):d/(r+o),a=Xx(n,i,s,d,r),a=a*60+.5),[a|0,u||0,l]}function Rc(t,e,n,i){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,i)).map(un)}function Ic(t,e,n){return Rc(qp,t,e,n)}function Qx(t,e,n){return Rc(Gx,t,e,n)}function qx(t,e,n){return Rc(Yx,t,e,n)}function Zp(t){return(t%360+360)%360}function Zx(t){const e=Kx.exec(t);let n=255,i;if(!e)return;e[5]!==i&&(n=e[6]?$i(+e[5]):un(+e[5]));const s=Zp(+e[2]),r=+e[3]/100,o=+e[4]/100;return e[1]==="hwb"?i=Qx(s,r,o):e[1]==="hsv"?i=qx(s,r,o):i=Ic(s,r,o),{r:i[0],g:i[1],b:i[2],a:n}}function Jx(t,e){var n=Oc(t);n[0]=Zp(n[0]+e),n=Ic(n),t.r=n[0],t.g=n[1],t.b=n[2]}function ey(t){if(!t)return;const e=Oc(t),n=e[0],i=cd(e[1]),s=cd(e[2]);return t.a<255?`hsla(${n}, ${i}%, ${s}%, ${Dt(t.a)})`:`hsl(${n}, ${i}%, ${s}%)`}const ud={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},dd={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function ty(){const t={},e=Object.keys(dd),n=Object.keys(ud);let i,s,r,o,l;for(i=0;i<e.length;i++){for(o=l=e[i],s=0;s<n.length;s++)r=n[s],l=l.replace(r,ud[r]);r=parseInt(dd[o],16),t[l]=[r>>16&255,r>>8&255,r&255]}return t}let cr;function ny(t){cr||(cr=ty(),cr.transparent=[0,0,0,0]);const e=cr[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const iy=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function sy(t){const e=iy.exec(t);let n=255,i,s,r;if(e){if(e[7]!==i){const o=+e[7];n=e[8]?$i(o):qt(o*255,0,255)}return i=+e[1],s=+e[3],r=+e[5],i=255&(e[2]?$i(i):qt(i,0,255)),s=255&(e[4]?$i(s):qt(s,0,255)),r=255&(e[6]?$i(r):qt(r,0,255)),{r:i,g:s,b:r,a:n}}}function ry(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${Dt(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const yl=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,Kn=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function oy(t,e,n){const i=Kn(Dt(t.r)),s=Kn(Dt(t.g)),r=Kn(Dt(t.b));return{r:un(yl(i+n*(Kn(Dt(e.r))-i))),g:un(yl(s+n*(Kn(Dt(e.g))-s))),b:un(yl(r+n*(Kn(Dt(e.b))-r))),a:t.a+n*(e.a-t.a)}}function ur(t,e,n){if(t){let i=Oc(t);i[e]=Math.max(0,Math.min(i[e]+i[e]*n,e===0?360:1)),i=Ic(i),t.r=i[0],t.g=i[1],t.b=i[2]}}function Jp(t,e){return t&&Object.assign(e||{},t)}function hd(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=un(t[3]))):(e=Jp(t,{r:0,g:0,b:0,a:1}),e.a=un(e.a)),e}function ly(t){return t.charAt(0)==="r"?sy(t):Zx(t)}class Ms{constructor(e){if(e instanceof Ms)return e;const n=typeof e;let i;n==="object"?i=hd(e):n==="string"&&(i=Wx(e)||ny(e)||ly(e)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var e=Jp(this._rgb);return e&&(e.a=Dt(e.a)),e}set rgb(e){this._rgb=hd(e)}rgbString(){return this._valid?ry(this._rgb):void 0}hexString(){return this._valid?Ux(this._rgb):void 0}hslString(){return this._valid?ey(this._rgb):void 0}mix(e,n){if(e){const i=this.rgb,s=e.rgb;let r;const o=n===r?.5:n,l=2*o-1,a=i.a-s.a,u=((l*a===-1?l:(l+a)/(1+l*a))+1)/2;r=1-u,i.r=255&u*i.r+r*s.r+.5,i.g=255&u*i.g+r*s.g+.5,i.b=255&u*i.b+r*s.b+.5,i.a=o*i.a+(1-o)*s.a,this.rgb=i}return this}interpolate(e,n){return e&&(this._rgb=oy(this._rgb,e._rgb,n)),this}clone(){return new Ms(this.rgb)}alpha(e){return this._rgb.a=un(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=Vs(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return ur(this._rgb,2,e),this}darken(e){return ur(this._rgb,2,-e),this}saturate(e){return ur(this._rgb,1,e),this}desaturate(e){return ur(this._rgb,1,-e),this}rotate(e){return Jx(this._rgb,e),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Mt(){}const ay=(()=>{let t=0;return()=>t++})();function z(t){return t==null}function ne(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function B(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function ue(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function Ue(t,e){return ue(t)?t:e}function R(t,e){return typeof t>"u"?e:t}const cy=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,em=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function G(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function $(t,e,n,i){let s,r,o;if(ne(t))for(r=t.length,s=0;s<r;s++)e.call(n,t[s],s);else if(B(t))for(o=Object.keys(t),r=o.length,s=0;s<r;s++)e.call(n,t[o[s]],o[s])}function go(t,e){let n,i,s,r;if(!t||!e||t.length!==e.length)return!1;for(n=0,i=t.length;n<i;++n)if(s=t[n],r=e[n],s.datasetIndex!==r.datasetIndex||s.index!==r.index)return!1;return!0}function xo(t){if(ne(t))return t.map(xo);if(B(t)){const e=Object.create(null),n=Object.keys(t),i=n.length;let s=0;for(;s<i;++s)e[n[s]]=xo(t[n[s]]);return e}return t}function tm(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function uy(t,e,n,i){if(!tm(t))return;const s=e[t],r=n[t];B(s)&&B(r)?Cs(s,r,i):e[t]=xo(r)}function Cs(t,e,n){const i=ne(e)?e:[e],s=i.length;if(!B(t))return t;n=n||{};const r=n.merger||uy;let o;for(let l=0;l<s;++l){if(o=i[l],!B(o))continue;const a=Object.keys(o);for(let u=0,d=a.length;u<d;++u)r(a[u],t,o,n)}return t}function rs(t,e){return Cs(t,e,{merger:dy})}function dy(t,e,n){if(!tm(t))return;const i=e[t],s=n[t];B(i)&&B(s)?rs(i,s):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=xo(s))}const fd={"":t=>t,x:t=>t.x,y:t=>t.y};function hy(t){const e=t.split("."),n=[];let i="";for(const s of e)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(n.push(i),i="");return n}function fy(t){const e=hy(t);return n=>{for(const i of e){if(i==="")break;n=n&&n[i]}return n}}function fn(t,e){return(fd[e]||(fd[e]=fy(e)))(t)}function zc(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Ps=t=>typeof t<"u",pn=t=>typeof t=="function",pd=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function py(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const H=Math.PI,ee=2*H,my=ee+H,yo=Number.POSITIVE_INFINITY,gy=H/180,he=H/2,vn=H/4,md=H*2/3,Zt=Math.log10,jt=Math.sign;function os(t,e,n){return Math.abs(t-e)<n}function gd(t){const e=Math.round(t);t=os(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(Zt(t))),i=t/n;return(i<=1?1:i<=2?2:i<=5?5:10)*n}function xy(t){const e=[],n=Math.sqrt(t);let i;for(i=1;i<n;i++)t%i===0&&(e.push(i),e.push(t/i));return n===(n|0)&&e.push(n),e.sort((s,r)=>s-r).pop(),e}function yy(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function _i(t){return!yy(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function vy(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function nm(t,e,n){let i,s,r;for(i=0,s=t.length;i<s;i++)r=t[i][n],isNaN(r)||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}function dt(t){return t*(H/180)}function Fc(t){return t*(180/H)}function xd(t){if(!ue(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function im(t,e){const n=e.x-t.x,i=e.y-t.y,s=Math.sqrt(n*n+i*i);let r=Math.atan2(i,n);return r<-.5*H&&(r+=ee),{angle:r,distance:s}}function Na(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function by(t,e){return(t-e+my)%ee-H}function Me(t){return(t%ee+ee)%ee}function Es(t,e,n,i){const s=Me(t),r=Me(e),o=Me(n),l=Me(r-s),a=Me(o-s),u=Me(s-r),d=Me(s-o);return s===r||s===o||i&&r===o||l>a&&u<d}function ye(t,e,n){return Math.max(e,Math.min(n,t))}function _y(t){return ye(t,-32768,32767)}function Ot(t,e,n,i=1e-6){return t>=Math.min(e,n)-i&&t<=Math.max(e,n)+i}function Bc(t,e,n){n=n||(o=>t[o]<e);let i=t.length-1,s=0,r;for(;i-s>1;)r=s+i>>1,n(r)?s=r:i=r;return{lo:s,hi:i}}const Rt=(t,e,n,i)=>Bc(t,n,i?s=>{const r=t[s][e];return r<n||r===n&&t[s+1][e]===n}:s=>t[s][e]<n),wy=(t,e,n)=>Bc(t,n,i=>t[i][e]>=n);function ky(t,e,n){let i=0,s=t.length;for(;i<s&&t[i]<e;)i++;for(;s>i&&t[s-1]>n;)s--;return i>0||s<t.length?t.slice(i,s):t}const sm=["push","pop","shift","splice","unshift"];function Sy(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),sm.forEach(n=>{const i="_onData"+zc(n),s=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...r){const o=s.apply(this,r);return t._chartjs.listeners.forEach(l=>{typeof l[i]=="function"&&l[i](...r)}),o}})})}function yd(t,e){const n=t._chartjs;if(!n)return;const i=n.listeners,s=i.indexOf(e);s!==-1&&i.splice(s,1),!(i.length>0)&&(sm.forEach(r=>{delete t[r]}),delete t._chartjs)}function rm(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const om=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function lm(t,e){let n=[],i=!1;return function(...s){n=s,i||(i=!0,om.call(window,()=>{i=!1,t.apply(e,n)}))}}function jy(t,e){let n;return function(...i){return e?(clearTimeout(n),n=setTimeout(t,e,i)):t.apply(this,i),e}}const Vc=t=>t==="start"?"left":t==="end"?"right":"center",Ne=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,Ny=(t,e,n,i)=>t===(i?"left":"right")?n:t==="center"?(e+n)/2:e;function am(t,e,n){const i=e.length;let s=0,r=i;if(t._sorted){const{iScale:o,vScale:l,_parsed:a}=t,u=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,d=o.axis,{min:h,max:f,minDefined:p,maxDefined:g}=o.getUserBounds();if(p){if(s=Math.min(Rt(a,d,h).lo,n?i:Rt(e,d,o.getPixelForValue(h)).lo),u){const x=a.slice(0,s+1).reverse().findIndex(b=>!z(b[l.axis]));s-=Math.max(0,x)}s=ye(s,0,i-1)}if(g){let x=Math.max(Rt(a,o.axis,f,!0).hi+1,n?0:Rt(e,d,o.getPixelForValue(f),!0).hi+1);if(u){const b=a.slice(x-1).findIndex(m=>!z(m[l.axis]));x+=Math.max(0,b)}r=ye(x,s,i)-s}else r=i-s}return{start:s,count:r}}function cm(t){const{xScale:e,yScale:n,_scaleRanges:i}=t,s={xmin:e.min,xmax:e.max,ymin:n.min,ymax:n.max};if(!i)return t._scaleRanges=s,!0;const r=i.xmin!==e.min||i.xmax!==e.max||i.ymin!==n.min||i.ymax!==n.max;return Object.assign(i,s),r}const dr=t=>t===0||t===1,vd=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*ee/n)),bd=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*ee/n)+1,ls={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*he)+1,easeOutSine:t=>Math.sin(t*he),easeInOutSine:t=>-.5*(Math.cos(H*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>dr(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>dr(t)?t:vd(t,.075,.3),easeOutElastic:t=>dr(t)?t:bd(t,.075,.3),easeInOutElastic(t){return dr(t)?t:t<.5?.5*vd(t*2,.1125,.45):.5+.5*bd(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-ls.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?ls.easeInBounce(t*2)*.5:ls.easeOutBounce(t*2-1)*.5+.5};function Hc(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function _d(t){return Hc(t)?t:new Ms(t)}function vl(t){return Hc(t)?t:new Ms(t).saturate(.5).darken(.1).hexString()}const My=["x","y","borderWidth","radius","tension"],Cy=["color","borderColor","backgroundColor"];function Py(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:Cy},numbers:{type:"number",properties:My}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function Ey(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const wd=new Map;function Ly(t,e){e=e||{};const n=t+JSON.stringify(e);let i=wd.get(n);return i||(i=new Intl.NumberFormat(t,e),wd.set(n,i)),i}function Hs(t,e,n){return Ly(e,n).format(t)}const um={values(t){return ne(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const i=this.chart.options.locale;let s,r=t;if(n.length>1){const u=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(u<1e-4||u>1e15)&&(s="scientific"),r=Dy(t,n)}const o=Zt(Math.abs(r)),l=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),a={notation:s,minimumFractionDigits:l,maximumFractionDigits:l};return Object.assign(a,this.options.ticks.format),Hs(t,i,a)},logarithmic(t,e,n){if(t===0)return"0";const i=n[e].significand||t/Math.pow(10,Math.floor(Zt(t)));return[1,2,3,5,10,15].includes(i)||e>.8*n.length?um.numeric.call(this,t,e,n):""}};function Dy(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var Bo={formatters:um};function Ty(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Bo.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Bn=Object.create(null),Ma=Object.create(null);function as(t,e){if(!e)return t;const n=e.split(".");for(let i=0,s=n.length;i<s;++i){const r=n[i];t=t[r]||(t[r]=Object.create(null))}return t}function bl(t,e,n){return typeof e=="string"?Cs(as(t,e),n):Cs(as(t,""),e)}class Ay{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,s)=>vl(s.backgroundColor),this.hoverBorderColor=(i,s)=>vl(s.borderColor),this.hoverColor=(i,s)=>vl(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return bl(this,e,n)}get(e){return as(this,e)}describe(e,n){return bl(Ma,e,n)}override(e,n){return bl(Bn,e,n)}route(e,n,i,s){const r=as(this,e),o=as(this,i),l="_"+n;Object.defineProperties(r,{[l]:{value:r[n],writable:!0},[n]:{enumerable:!0,get(){const a=this[l],u=o[s];return B(a)?Object.assign({},u,a):R(a,u)},set(a){this[l]=a}}})}apply(e){e.forEach(n=>n(this))}}var ie=new Ay({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Py,Ey,Ty]);function Oy(t){return!t||z(t.size)||z(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function vo(t,e,n,i,s){let r=e[s];return r||(r=e[s]=t.measureText(s).width,n.push(s)),r>i&&(i=r),i}function Ry(t,e,n,i){i=i||{};let s=i.data=i.data||{},r=i.garbageCollect=i.garbageCollect||[];i.font!==e&&(s=i.data={},r=i.garbageCollect=[],i.font=e),t.save(),t.font=e;let o=0;const l=n.length;let a,u,d,h,f;for(a=0;a<l;a++)if(h=n[a],h!=null&&!ne(h))o=vo(t,s,r,o,h);else if(ne(h))for(u=0,d=h.length;u<d;u++)f=h[u],f!=null&&!ne(f)&&(o=vo(t,s,r,o,f));t.restore();const p=r.length/2;if(p>n.length){for(a=0;a<p;a++)delete s[r[a]];r.splice(0,p)}return o}function bn(t,e,n){const i=t.currentDevicePixelRatio,s=n!==0?Math.max(n/2,.5):0;return Math.round((e-s)*i)/i+s}function kd(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function Ca(t,e,n,i){dm(t,e,n,i,null)}function dm(t,e,n,i,s){let r,o,l,a,u,d,h,f;const p=e.pointStyle,g=e.rotation,x=e.radius;let b=(g||0)*gy;if(p&&typeof p=="object"&&(r=p.toString(),r==="[object HTMLImageElement]"||r==="[object HTMLCanvasElement]")){t.save(),t.translate(n,i),t.rotate(b),t.drawImage(p,-p.width/2,-p.height/2,p.width,p.height),t.restore();return}if(!(isNaN(x)||x<=0)){switch(t.beginPath(),p){default:s?t.ellipse(n,i,s/2,x,0,0,ee):t.arc(n,i,x,0,ee),t.closePath();break;case"triangle":d=s?s/2:x,t.moveTo(n+Math.sin(b)*d,i-Math.cos(b)*x),b+=md,t.lineTo(n+Math.sin(b)*d,i-Math.cos(b)*x),b+=md,t.lineTo(n+Math.sin(b)*d,i-Math.cos(b)*x),t.closePath();break;case"rectRounded":u=x*.516,a=x-u,o=Math.cos(b+vn)*a,h=Math.cos(b+vn)*(s?s/2-u:a),l=Math.sin(b+vn)*a,f=Math.sin(b+vn)*(s?s/2-u:a),t.arc(n-h,i-l,u,b-H,b-he),t.arc(n+f,i-o,u,b-he,b),t.arc(n+h,i+l,u,b,b+he),t.arc(n-f,i+o,u,b+he,b+H),t.closePath();break;case"rect":if(!g){a=Math.SQRT1_2*x,d=s?s/2:a,t.rect(n-d,i-a,2*d,2*a);break}b+=vn;case"rectRot":h=Math.cos(b)*(s?s/2:x),o=Math.cos(b)*x,l=Math.sin(b)*x,f=Math.sin(b)*(s?s/2:x),t.moveTo(n-h,i-l),t.lineTo(n+f,i-o),t.lineTo(n+h,i+l),t.lineTo(n-f,i+o),t.closePath();break;case"crossRot":b+=vn;case"cross":h=Math.cos(b)*(s?s/2:x),o=Math.cos(b)*x,l=Math.sin(b)*x,f=Math.sin(b)*(s?s/2:x),t.moveTo(n-h,i-l),t.lineTo(n+h,i+l),t.moveTo(n+f,i-o),t.lineTo(n-f,i+o);break;case"star":h=Math.cos(b)*(s?s/2:x),o=Math.cos(b)*x,l=Math.sin(b)*x,f=Math.sin(b)*(s?s/2:x),t.moveTo(n-h,i-l),t.lineTo(n+h,i+l),t.moveTo(n+f,i-o),t.lineTo(n-f,i+o),b+=vn,h=Math.cos(b)*(s?s/2:x),o=Math.cos(b)*x,l=Math.sin(b)*x,f=Math.sin(b)*(s?s/2:x),t.moveTo(n-h,i-l),t.lineTo(n+h,i+l),t.moveTo(n+f,i-o),t.lineTo(n-f,i+o);break;case"line":o=s?s/2:Math.cos(b)*x,l=Math.sin(b)*x,t.moveTo(n-o,i-l),t.lineTo(n+o,i+l);break;case"dash":t.moveTo(n,i),t.lineTo(n+Math.cos(b)*(s?s/2:x),i+Math.sin(b)*x);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function It(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function Vo(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function Ho(t){t.restore()}function Iy(t,e,n,i,s){if(!e)return t.lineTo(n.x,n.y);if(s==="middle"){const r=(e.x+n.x)/2;t.lineTo(r,e.y),t.lineTo(r,n.y)}else s==="after"!=!!i?t.lineTo(e.x,n.y):t.lineTo(n.x,e.y);t.lineTo(n.x,n.y)}function zy(t,e,n,i){if(!e)return t.lineTo(n.x,n.y);t.bezierCurveTo(i?e.cp1x:e.cp2x,i?e.cp1y:e.cp2y,i?n.cp2x:n.cp1x,i?n.cp2y:n.cp1y,n.x,n.y)}function Fy(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),z(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function By(t,e,n,i,s){if(s.strikethrough||s.underline){const r=t.measureText(i),o=e-r.actualBoundingBoxLeft,l=e+r.actualBoundingBoxRight,a=n-r.actualBoundingBoxAscent,u=n+r.actualBoundingBoxDescent,d=s.strikethrough?(a+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=s.decorationWidth||2,t.moveTo(o,d),t.lineTo(l,d),t.stroke()}}function Vy(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function Vn(t,e,n,i,s,r={}){const o=ne(e)?e:[e],l=r.strokeWidth>0&&r.strokeColor!=="";let a,u;for(t.save(),t.font=s.string,Fy(t,r),a=0;a<o.length;++a)u=o[a],r.backdrop&&Vy(t,r.backdrop),l&&(r.strokeColor&&(t.strokeStyle=r.strokeColor),z(r.strokeWidth)||(t.lineWidth=r.strokeWidth),t.strokeText(u,n,i,r.maxWidth)),t.fillText(u,n,i,r.maxWidth),By(t,n,i,u,r),i+=Number(s.lineHeight);t.restore()}function Ls(t,e){const{x:n,y:i,w:s,h:r,radius:o}=e;t.arc(n+o.topLeft,i+o.topLeft,o.topLeft,1.5*H,H,!0),t.lineTo(n,i+r-o.bottomLeft),t.arc(n+o.bottomLeft,i+r-o.bottomLeft,o.bottomLeft,H,he,!0),t.lineTo(n+s-o.bottomRight,i+r),t.arc(n+s-o.bottomRight,i+r-o.bottomRight,o.bottomRight,he,0,!0),t.lineTo(n+s,i+o.topRight),t.arc(n+s-o.topRight,i+o.topRight,o.topRight,0,-he,!0),t.lineTo(n+o.topLeft,i)}const Hy=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Wy=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function $y(t,e){const n=(""+t).match(Hy);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const Uy=t=>+t||0;function Wc(t,e){const n={},i=B(e),s=i?Object.keys(e):e,r=B(t)?i?o=>R(t[o],t[e[o]]):o=>t[o]:()=>t;for(const o of s)n[o]=Uy(r(o));return n}function hm(t){return Wc(t,{top:"y",right:"x",bottom:"y",left:"x"})}function Tn(t){return Wc(t,["topLeft","topRight","bottomLeft","bottomRight"])}function Le(t){const e=hm(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ge(t,e){t=t||{},e=e||ie.font;let n=R(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let i=R(t.style,e.style);i&&!(""+i).match(Wy)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const s={family:R(t.family,e.family),lineHeight:$y(R(t.lineHeight,e.lineHeight),n),size:n,style:i,weight:R(t.weight,e.weight),string:""};return s.string=Oy(s),s}function Ui(t,e,n,i){let s,r,o;for(s=0,r=t.length;s<r;++s)if(o=t[s],o!==void 0&&o!==void 0)return o}function Ky(t,e,n){const{min:i,max:s}=t,r=em(e,(s-i)/2),o=(l,a)=>n&&l===0?0:l+a;return{min:o(i,-Math.abs(r)),max:o(s,r)}}function yn(t,e){return Object.assign(Object.create(t),e)}function $c(t,e=[""],n,i,s=()=>t[0]){const r=n||t;typeof i>"u"&&(i=gm("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:r,_fallback:i,_getTarget:s,override:l=>$c([l,...t],e,r,i)};return new Proxy(o,{deleteProperty(l,a){return delete l[a],delete l._keys,delete t[0][a],!0},get(l,a){return pm(l,a,()=>ev(a,e,t,l))},getOwnPropertyDescriptor(l,a){return Reflect.getOwnPropertyDescriptor(l._scopes[0],a)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(l,a){return jd(l).includes(a)},ownKeys(l){return jd(l)},set(l,a,u){const d=l._storage||(l._storage=s());return l[a]=d[a]=u,delete l._keys,!0}})}function wi(t,e,n,i){const s={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:fm(t,i),setContext:r=>wi(t,r,n,i),override:r=>wi(t.override(r),e,n,i)};return new Proxy(s,{deleteProperty(r,o){return delete r[o],delete t[o],!0},get(r,o,l){return pm(r,o,()=>Gy(r,o,l))},getOwnPropertyDescriptor(r,o){return r._descriptors.allKeys?Reflect.has(t,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,o)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(r,o){return Reflect.has(t,o)},ownKeys(){return Reflect.ownKeys(t)},set(r,o,l){return t[o]=l,delete r[o],!0}})}function fm(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:i=e.indexable,_allKeys:s=e.allKeys}=t;return{allKeys:s,scriptable:n,indexable:i,isScriptable:pn(n)?n:()=>n,isIndexable:pn(i)?i:()=>i}}const Yy=(t,e)=>t?t+zc(e):e,Uc=(t,e)=>B(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function pm(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const i=n();return t[e]=i,i}function Gy(t,e,n){const{_proxy:i,_context:s,_subProxy:r,_descriptors:o}=t;let l=i[e];return pn(l)&&o.isScriptable(e)&&(l=Xy(e,l,t,n)),ne(l)&&l.length&&(l=Qy(e,l,t,o.isIndexable)),Uc(e,l)&&(l=wi(l,s,r&&r[e],o)),l}function Xy(t,e,n,i){const{_proxy:s,_context:r,_subProxy:o,_stack:l}=n;if(l.has(t))throw new Error("Recursion detected: "+Array.from(l).join("->")+"->"+t);l.add(t);let a=e(r,o||i);return l.delete(t),Uc(t,a)&&(a=Kc(s._scopes,s,t,a)),a}function Qy(t,e,n,i){const{_proxy:s,_context:r,_subProxy:o,_descriptors:l}=n;if(typeof r.index<"u"&&i(t))return e[r.index%e.length];if(B(e[0])){const a=e,u=s._scopes.filter(d=>d!==a);e=[];for(const d of a){const h=Kc(u,s,t,d);e.push(wi(h,r,o&&o[t],l))}}return e}function mm(t,e,n){return pn(t)?t(e,n):t}const qy=(t,e)=>t===!0?e:typeof t=="string"?fn(e,t):void 0;function Zy(t,e,n,i,s){for(const r of e){const o=qy(n,r);if(o){t.add(o);const l=mm(o._fallback,n,s);if(typeof l<"u"&&l!==n&&l!==i)return l}else if(o===!1&&typeof i<"u"&&n!==i)return null}return!1}function Kc(t,e,n,i){const s=e._rootScopes,r=mm(e._fallback,n,i),o=[...t,...s],l=new Set;l.add(i);let a=Sd(l,o,n,r||n,i);return a===null||typeof r<"u"&&r!==n&&(a=Sd(l,o,r,a,i),a===null)?!1:$c(Array.from(l),[""],s,r,()=>Jy(e,n,i))}function Sd(t,e,n,i,s){for(;n;)n=Zy(t,e,n,i,s);return n}function Jy(t,e,n){const i=t._getTarget();e in i||(i[e]={});const s=i[e];return ne(s)&&B(n)?n:s||{}}function ev(t,e,n,i){let s;for(const r of e)if(s=gm(Yy(r,t),n),typeof s<"u")return Uc(t,s)?Kc(n,i,t,s):s}function gm(t,e){for(const n of e){if(!n)continue;const i=n[t];if(typeof i<"u")return i}}function jd(t){let e=t._keys;return e||(e=t._keys=tv(t._scopes)),e}function tv(t){const e=new Set;for(const n of t)for(const i of Object.keys(n).filter(s=>!s.startsWith("_")))e.add(i);return Array.from(e)}function xm(t,e,n,i){const{iScale:s}=t,{key:r="r"}=this._parsing,o=new Array(i);let l,a,u,d;for(l=0,a=i;l<a;++l)u=l+n,d=e[u],o[l]={r:s.parse(fn(d,r),u)};return o}const nv=Number.EPSILON||1e-14,ki=(t,e)=>e<t.length&&!t[e].skip&&t[e],ym=t=>t==="x"?"y":"x";function iv(t,e,n,i){const s=t.skip?e:t,r=e,o=n.skip?e:n,l=Na(r,s),a=Na(o,r);let u=l/(l+a),d=a/(l+a);u=isNaN(u)?0:u,d=isNaN(d)?0:d;const h=i*u,f=i*d;return{previous:{x:r.x-h*(o.x-s.x),y:r.y-h*(o.y-s.y)},next:{x:r.x+f*(o.x-s.x),y:r.y+f*(o.y-s.y)}}}function sv(t,e,n){const i=t.length;let s,r,o,l,a,u=ki(t,0);for(let d=0;d<i-1;++d)if(a=u,u=ki(t,d+1),!(!a||!u)){if(os(e[d],0,nv)){n[d]=n[d+1]=0;continue}s=n[d]/e[d],r=n[d+1]/e[d],l=Math.pow(s,2)+Math.pow(r,2),!(l<=9)&&(o=3/Math.sqrt(l),n[d]=s*o*e[d],n[d+1]=r*o*e[d])}}function rv(t,e,n="x"){const i=ym(n),s=t.length;let r,o,l,a=ki(t,0);for(let u=0;u<s;++u){if(o=l,l=a,a=ki(t,u+1),!l)continue;const d=l[n],h=l[i];o&&(r=(d-o[n])/3,l[`cp1${n}`]=d-r,l[`cp1${i}`]=h-r*e[u]),a&&(r=(a[n]-d)/3,l[`cp2${n}`]=d+r,l[`cp2${i}`]=h+r*e[u])}}function ov(t,e="x"){const n=ym(e),i=t.length,s=Array(i).fill(0),r=Array(i);let o,l,a,u=ki(t,0);for(o=0;o<i;++o)if(l=a,a=u,u=ki(t,o+1),!!a){if(u){const d=u[e]-a[e];s[o]=d!==0?(u[n]-a[n])/d:0}r[o]=l?u?jt(s[o-1])!==jt(s[o])?0:(s[o-1]+s[o])/2:s[o-1]:s[o]}sv(t,s,r),rv(t,r,e)}function hr(t,e,n){return Math.max(Math.min(t,n),e)}function lv(t,e){let n,i,s,r,o,l=It(t[0],e);for(n=0,i=t.length;n<i;++n)o=r,r=l,l=n<i-1&&It(t[n+1],e),r&&(s=t[n],o&&(s.cp1x=hr(s.cp1x,e.left,e.right),s.cp1y=hr(s.cp1y,e.top,e.bottom)),l&&(s.cp2x=hr(s.cp2x,e.left,e.right),s.cp2y=hr(s.cp2y,e.top,e.bottom)))}function av(t,e,n,i,s){let r,o,l,a;if(e.spanGaps&&(t=t.filter(u=>!u.skip)),e.cubicInterpolationMode==="monotone")ov(t,s);else{let u=i?t[t.length-1]:t[0];for(r=0,o=t.length;r<o;++r)l=t[r],a=iv(u,l,t[Math.min(r+1,o-(i?0:1))%o],e.tension),l.cp1x=a.previous.x,l.cp1y=a.previous.y,l.cp2x=a.next.x,l.cp2y=a.next.y,u=l}e.capBezierPoints&&lv(t,n)}function Yc(){return typeof window<"u"&&typeof document<"u"}function Gc(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function bo(t,e,n){let i;return typeof t=="string"?(i=parseInt(t,10),t.indexOf("%")!==-1&&(i=i/100*e.parentNode[n])):i=t,i}const Wo=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function cv(t,e){return Wo(t).getPropertyValue(e)}const uv=["top","right","bottom","left"];function An(t,e,n){const i={};n=n?"-"+n:"";for(let s=0;s<4;s++){const r=uv[s];i[r]=parseFloat(t[e+"-"+r+n])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const dv=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function hv(t,e){const n=t.touches,i=n&&n.length?n[0]:t,{offsetX:s,offsetY:r}=i;let o=!1,l,a;if(dv(s,r,t.target))l=s,a=r;else{const u=e.getBoundingClientRect();l=i.clientX-u.left,a=i.clientY-u.top,o=!0}return{x:l,y:a,box:o}}function jn(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:i}=e,s=Wo(n),r=s.boxSizing==="border-box",o=An(s,"padding"),l=An(s,"border","width"),{x:a,y:u,box:d}=hv(t,n),h=o.left+(d&&l.left),f=o.top+(d&&l.top);let{width:p,height:g}=e;return r&&(p-=o.width+l.width,g-=o.height+l.height),{x:Math.round((a-h)/p*n.width/i),y:Math.round((u-f)/g*n.height/i)}}function fv(t,e,n){let i,s;if(e===void 0||n===void 0){const r=t&&Gc(t);if(!r)e=t.clientWidth,n=t.clientHeight;else{const o=r.getBoundingClientRect(),l=Wo(r),a=An(l,"border","width"),u=An(l,"padding");e=o.width-u.width-a.width,n=o.height-u.height-a.height,i=bo(l.maxWidth,r,"clientWidth"),s=bo(l.maxHeight,r,"clientHeight")}}return{width:e,height:n,maxWidth:i||yo,maxHeight:s||yo}}const fr=t=>Math.round(t*10)/10;function pv(t,e,n,i){const s=Wo(t),r=An(s,"margin"),o=bo(s.maxWidth,t,"clientWidth")||yo,l=bo(s.maxHeight,t,"clientHeight")||yo,a=fv(t,e,n);let{width:u,height:d}=a;if(s.boxSizing==="content-box"){const f=An(s,"border","width"),p=An(s,"padding");u-=p.width+f.width,d-=p.height+f.height}return u=Math.max(0,u-r.width),d=Math.max(0,i?u/i:d-r.height),u=fr(Math.min(u,o,a.maxWidth)),d=fr(Math.min(d,l,a.maxHeight)),u&&!d&&(d=fr(u/2)),(e!==void 0||n!==void 0)&&i&&a.height&&d>a.height&&(d=a.height,u=fr(Math.floor(d*i))),{width:u,height:d}}function Nd(t,e,n){const i=e||1,s=Math.floor(t.height*i),r=Math.floor(t.width*i);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const o=t.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),t.currentDevicePixelRatio!==i||o.height!==s||o.width!==r?(t.currentDevicePixelRatio=i,o.height=s,o.width=r,t.ctx.setTransform(i,0,0,i,0,0),!0):!1}const mv=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};Yc()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function Md(t,e){const n=cv(t,e),i=n&&n.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function Nn(t,e,n,i){return{x:t.x+n*(e.x-t.x),y:t.y+n*(e.y-t.y)}}function gv(t,e,n,i){return{x:t.x+n*(e.x-t.x),y:i==="middle"?n<.5?t.y:e.y:i==="after"?n<1?t.y:e.y:n>0?e.y:t.y}}function xv(t,e,n,i){const s={x:t.cp2x,y:t.cp2y},r={x:e.cp1x,y:e.cp1y},o=Nn(t,s,n),l=Nn(s,r,n),a=Nn(r,e,n),u=Nn(o,l,n),d=Nn(l,a,n);return Nn(u,d,n)}const yv=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,i){return n-i},leftForLtr(n,i){return n-i}}},vv=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function fi(t,e,n){return t?yv(e,n):vv()}function vm(t,e){let n,i;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,i=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=i)}function bm(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function _m(t){return t==="angle"?{between:Es,compare:by,normalize:Me}:{between:Ot,compare:(e,n)=>e-n,normalize:e=>e}}function Cd({start:t,end:e,count:n,loop:i,style:s}){return{start:t%n,end:e%n,loop:i&&(e-t+1)%n===0,style:s}}function bv(t,e,n){const{property:i,start:s,end:r}=n,{between:o,normalize:l}=_m(i),a=e.length;let{start:u,end:d,loop:h}=t,f,p;if(h){for(u+=a,d+=a,f=0,p=a;f<p&&o(l(e[u%a][i]),s,r);++f)u--,d--;u%=a,d%=a}return d<u&&(d+=a),{start:u,end:d,loop:h,style:t.style}}function wm(t,e,n){if(!n)return[t];const{property:i,start:s,end:r}=n,o=e.length,{compare:l,between:a,normalize:u}=_m(i),{start:d,end:h,loop:f,style:p}=bv(t,e,n),g=[];let x=!1,b=null,m,y,v;const _=()=>a(s,v,m)&&l(s,v)!==0,w=()=>l(r,m)===0||a(r,v,m),j=()=>x||_(),k=()=>!x||w();for(let S=d,C=d;S<=h;++S)y=e[S%o],!y.skip&&(m=u(y[i]),m!==v&&(x=a(m,s,r),b===null&&j()&&(b=l(m,s)===0?S:C),b!==null&&k()&&(g.push(Cd({start:b,end:S,loop:f,count:o,style:p})),b=null),C=S,v=m));return b!==null&&g.push(Cd({start:b,end:h,loop:f,count:o,style:p})),g}function km(t,e){const n=[],i=t.segments;for(let s=0;s<i.length;s++){const r=wm(i[s],t.points,e);r.length&&n.push(...r)}return n}function _v(t,e,n,i){let s=0,r=e-1;if(n&&!i)for(;s<e&&!t[s].skip;)s++;for(;s<e&&t[s].skip;)s++;for(s%=e,n&&(r+=s);r>s&&t[r%e].skip;)r--;return r%=e,{start:s,end:r}}function wv(t,e,n,i){const s=t.length,r=[];let o=e,l=t[e],a;for(a=e+1;a<=n;++a){const u=t[a%s];u.skip||u.stop?l.skip||(i=!1,r.push({start:e%s,end:(a-1)%s,loop:i}),e=o=u.stop?a:null):(o=a,l.skip&&(e=a)),l=u}return o!==null&&r.push({start:e%s,end:o%s,loop:i}),r}function kv(t,e){const n=t.points,i=t.options.spanGaps,s=n.length;if(!s)return[];const r=!!t._loop,{start:o,end:l}=_v(n,s,r,i);if(i===!0)return Pd(t,[{start:o,end:l,loop:r}],n,e);const a=l<o?l+s:l,u=!!t._fullLoop&&o===0&&l===s-1;return Pd(t,wv(n,o,a,u),n,e)}function Pd(t,e,n,i){return!i||!i.setContext||!n?e:Sv(t,e,n,i)}function Sv(t,e,n,i){const s=t._chart.getContext(),r=Ed(t.options),{_datasetIndex:o,options:{spanGaps:l}}=t,a=n.length,u=[];let d=r,h=e[0].start,f=h;function p(g,x,b,m){const y=l?-1:1;if(g!==x){for(g+=a;n[g%a].skip;)g-=y;for(;n[x%a].skip;)x+=y;g%a!==x%a&&(u.push({start:g%a,end:x%a,loop:b,style:m}),d=m,h=x%a)}}for(const g of e){h=l?h:g.start;let x=n[h%a],b;for(f=h+1;f<=g.end;f++){const m=n[f%a];b=Ed(i.setContext(yn(s,{type:"segment",p0:x,p1:m,p0DataIndex:(f-1)%a,p1DataIndex:f%a,datasetIndex:o}))),jv(b,d)&&p(h,f-1,g.loop,d),x=m,d=b}h<f-1&&p(h,f-1,g.loop,d)}return u}function Ed(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function jv(t,e){if(!e)return!1;const n=[],i=function(s,r){return Hc(r)?(n.includes(r)||n.push(r),n.indexOf(r)):r};return JSON.stringify(t,i)!==JSON.stringify(e,i)}function pr(t,e,n){return t.options.clip?t[n]:e[n]}function Nv(t,e){const{xScale:n,yScale:i}=t;return n&&i?{left:pr(n,e,"left"),right:pr(n,e,"right"),top:pr(i,e,"top"),bottom:pr(i,e,"bottom")}:e}function Sm(t,e){const n=e._clip;if(n.disabled)return!1;const i=Nv(e,t.chartArea);return{left:n.left===!1?0:i.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:i.right+(n.right===!0?0:n.right),top:n.top===!1?0:i.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:i.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Mv{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,i,s){const r=n.listeners[s],o=n.duration;r.forEach(l=>l({chart:e,initial:n.initial,numSteps:o,currentStep:Math.min(i-n.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=om.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;const r=i.items;let o=r.length-1,l=!1,a;for(;o>=0;--o)a=r[o],a._active?(a._total>i.duration&&(i.duration=a._total),a.tick(e),l=!0):(r[o]=r[r.length-1],r.pop());l&&(s.draw(),this._notify(s,i,e,"progress")),r.length||(i.running=!1,this._notify(s,i,e,"complete"),i.initial=!1),n+=r.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let i=n.get(e);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,i)),i}listen(e,n,i){this._getAnims(e).listeners[n].push(i)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const i=n.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Ct=new Mv;const Ld="transparent",Cv={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const i=_d(t||Ld),s=i.valid&&_d(e||Ld);return s&&s.valid?s.mix(i,n).hexString():e},number(t,e,n){return t+(e-t)*n}};class Pv{constructor(e,n,i,s){const r=n[i];s=Ui([e.to,s,r,e.from]);const o=Ui([e.from,r,s]);this._active=!0,this._fn=e.fn||Cv[e.type||typeof o],this._easing=ls[e.easing]||ls.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=i,this._from=o,this._to=s,this._promises=void 0}active(){return this._active}update(e,n,i){if(this._active){this._notify(!1);const s=this._target[this._prop],r=i-this._start,o=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(o,e.duration)),this._total+=r,this._loop=!!e.loop,this._to=Ui([e.to,n,s,e.from]),this._from=Ui([e.from,s,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,i=this._duration,s=this._prop,r=this._from,o=this._loop,l=this._to;let a;if(this._active=r!==l&&(o||n<i),!this._active){this._target[s]=l,this._notify(!0);return}if(n<0){this._target[s]=r;return}a=n/i%2,a=o&&a>1?2-a:a,a=this._easing(Math.min(1,Math.max(0,a))),this._target[s]=this._fn(r,l,a)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,i)=>{e.push({res:n,rej:i})})}_notify(e){const n=e?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][n]()}}class jm{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!B(e))return;const n=Object.keys(ie.animation),i=this._properties;Object.getOwnPropertyNames(e).forEach(s=>{const r=e[s];if(!B(r))return;const o={};for(const l of n)o[l]=r[l];(ne(r.properties)&&r.properties||[s]).forEach(l=>{(l===s||!i.has(l))&&i.set(l,o)})})}_animateOptions(e,n){const i=n.options,s=Lv(e,i);if(!s)return[];const r=this._createAnimations(s,i);return i.$shared&&Ev(e.options.$animations,i).then(()=>{e.options=i},()=>{}),r}_createAnimations(e,n){const i=this._properties,s=[],r=e.$animations||(e.$animations={}),o=Object.keys(n),l=Date.now();let a;for(a=o.length-1;a>=0;--a){const u=o[a];if(u.charAt(0)==="$")continue;if(u==="options"){s.push(...this._animateOptions(e,n));continue}const d=n[u];let h=r[u];const f=i.get(u);if(h)if(f&&h.active()){h.update(f,d,l);continue}else h.cancel();if(!f||!f.duration){e[u]=d;continue}r[u]=h=new Pv(f,e,u,d),s.push(h)}return s}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const i=this._createAnimations(e,n);if(i.length)return Ct.add(this._chart,i),!0}}function Ev(t,e){const n=[],i=Object.keys(e);for(let s=0;s<i.length;s++){const r=t[i[s]];r&&r.active()&&n.push(r.wait())}return Promise.all(n)}function Lv(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function Dd(t,e){const n=t&&t.options||{},i=n.reverse,s=n.min===void 0?e:0,r=n.max===void 0?e:0;return{start:i?r:s,end:i?s:r}}function Dv(t,e,n){if(n===!1)return!1;const i=Dd(t,n),s=Dd(e,n);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function Tv(t){let e,n,i,s;return B(t)?(e=t.top,n=t.right,i=t.bottom,s=t.left):e=n=i=s=t,{top:e,right:n,bottom:i,left:s,disabled:t===!1}}function Nm(t,e){const n=[],i=t._getSortedDatasetMetas(e);let s,r;for(s=0,r=i.length;s<r;++s)n.push(i[s].index);return n}function Td(t,e,n,i={}){const s=t.keys,r=i.mode==="single";let o,l,a,u;if(e===null)return;let d=!1;for(o=0,l=s.length;o<l;++o){if(a=+s[o],a===n){if(d=!0,i.all)continue;break}u=t.values[a],ue(u)&&(r||e===0||jt(e)===jt(u))&&(e+=u)}return!d&&!i.all?0:e}function Av(t,e){const{iScale:n,vScale:i}=e,s=n.axis==="x"?"x":"y",r=i.axis==="x"?"x":"y",o=Object.keys(t),l=new Array(o.length);let a,u,d;for(a=0,u=o.length;a<u;++a)d=o[a],l[a]={[s]:d,[r]:t[d]};return l}function _l(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function Ov(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function Rv(t){const{min:e,max:n,minDefined:i,maxDefined:s}=t.getUserBounds();return{min:i?e:Number.NEGATIVE_INFINITY,max:s?n:Number.POSITIVE_INFINITY}}function Iv(t,e,n){const i=t[e]||(t[e]={});return i[n]||(i[n]={})}function Ad(t,e,n,i){for(const s of e.getMatchingVisibleMetas(i).reverse()){const r=t[s.index];if(n&&r>0||!n&&r<0)return s.index}return null}function Od(t,e){const{chart:n,_cachedMeta:i}=t,s=n._stacks||(n._stacks={}),{iScale:r,vScale:o,index:l}=i,a=r.axis,u=o.axis,d=Ov(r,o,i),h=e.length;let f;for(let p=0;p<h;++p){const g=e[p],{[a]:x,[u]:b}=g,m=g._stacks||(g._stacks={});f=m[u]=Iv(s,d,x),f[l]=b,f._top=Ad(f,o,!0,i.type),f._bottom=Ad(f,o,!1,i.type);const y=f._visualValues||(f._visualValues={});y[l]=b}}function wl(t,e){const n=t.scales;return Object.keys(n).filter(i=>n[i].axis===e).shift()}function zv(t,e){return yn(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function Fv(t,e,n){return yn(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function Oi(t,e){const n=t.controller.index,i=t.vScale&&t.vScale.axis;if(i){e=e||t._parsed;for(const s of e){const r=s._stacks;if(!r||r[i]===void 0||r[i][n]===void 0)return;delete r[i][n],r[i]._visualValues!==void 0&&r[i]._visualValues[n]!==void 0&&delete r[i]._visualValues[n]}}}const kl=t=>t==="reset"||t==="none",Rd=(t,e)=>e?t:Object.assign({},t),Bv=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:Nm(n,!0),values:null};class pt{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=_l(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&Oi(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,i=this.getDataset(),s=(h,f,p,g)=>h==="x"?f:h==="r"?g:p,r=n.xAxisID=R(i.xAxisID,wl(e,"x")),o=n.yAxisID=R(i.yAxisID,wl(e,"y")),l=n.rAxisID=R(i.rAxisID,wl(e,"r")),a=n.indexAxis,u=n.iAxisID=s(a,r,o,l),d=n.vAxisID=s(a,o,r,l);n.xScale=this.getScaleForId(r),n.yScale=this.getScaleForId(o),n.rScale=this.getScaleForId(l),n.iScale=this.getScaleForId(u),n.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&yd(this._data,this),e._stacked&&Oi(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),i=this._data;if(B(n)){const s=this._cachedMeta;this._data=Av(n,s)}else if(i!==n){if(i){yd(i,this);const s=this._cachedMeta;Oi(s),s._parsed=[]}n&&Object.isExtensible(n)&&Sy(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const r=n._stacked;n._stacked=_l(n.vScale,n),n.stack!==i.stack&&(s=!0,Oi(n),n.stack=i.stack),this._resyncElements(e),(s||r!==n._stacked)&&(Od(this,n._parsed),n._stacked=_l(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),i=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:i,_data:s}=this,{iScale:r,_stacked:o}=i,l=r.axis;let a=e===0&&n===s.length?!0:i._sorted,u=e>0&&i._parsed[e-1],d,h,f;if(this._parsing===!1)i._parsed=s,i._sorted=!0,f=s;else{ne(s[e])?f=this.parseArrayData(i,s,e,n):B(s[e])?f=this.parseObjectData(i,s,e,n):f=this.parsePrimitiveData(i,s,e,n);const p=()=>h[l]===null||u&&h[l]<u[l];for(d=0;d<n;++d)i._parsed[d+e]=h=f[d],a&&(p()&&(a=!1),u=h);i._sorted=a}o&&Od(this,f)}parsePrimitiveData(e,n,i,s){const{iScale:r,vScale:o}=e,l=r.axis,a=o.axis,u=r.getLabels(),d=r===o,h=new Array(s);let f,p,g;for(f=0,p=s;f<p;++f)g=f+i,h[f]={[l]:d||r.parse(u[g],g),[a]:o.parse(n[g],g)};return h}parseArrayData(e,n,i,s){const{xScale:r,yScale:o}=e,l=new Array(s);let a,u,d,h;for(a=0,u=s;a<u;++a)d=a+i,h=n[d],l[a]={x:r.parse(h[0],d),y:o.parse(h[1],d)};return l}parseObjectData(e,n,i,s){const{xScale:r,yScale:o}=e,{xAxisKey:l="x",yAxisKey:a="y"}=this._parsing,u=new Array(s);let d,h,f,p;for(d=0,h=s;d<h;++d)f=d+i,p=n[f],u[d]={x:r.parse(fn(p,l),f),y:o.parse(fn(p,a),f)};return u}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,i){const s=this.chart,r=this._cachedMeta,o=n[e.axis],l={keys:Nm(s,!0),values:n._stacks[e.axis]._visualValues};return Td(l,o,r.index,{mode:i})}updateRangeFromParsed(e,n,i,s){const r=i[n.axis];let o=r===null?NaN:r;const l=s&&i._stacks[n.axis];s&&l&&(s.values=l,o=Td(s,r,this._cachedMeta.index)),e.min=Math.min(e.min,o),e.max=Math.max(e.max,o)}getMinMax(e,n){const i=this._cachedMeta,s=i._parsed,r=i._sorted&&e===i.iScale,o=s.length,l=this._getOtherScale(e),a=Bv(n,i,this.chart),u={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:h}=Rv(l);let f,p;function g(){p=s[f];const x=p[l.axis];return!ue(p[e.axis])||d>x||h<x}for(f=0;f<o&&!(!g()&&(this.updateRangeFromParsed(u,e,p,a),r));++f);if(r){for(f=o-1;f>=0;--f)if(!g()){this.updateRangeFromParsed(u,e,p,a);break}}return u}getAllParsedValues(e){const n=this._cachedMeta._parsed,i=[];let s,r,o;for(s=0,r=n.length;s<r;++s)o=n[s][e.axis],ue(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,i=n.iScale,s=n.vScale,r=this.getParsed(e);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=Tv(R(this.options.clip,Dv(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,i=this._cachedMeta,s=i.data||[],r=n.chartArea,o=[],l=this._drawStart||0,a=this._drawCount||s.length-l,u=this.options.drawActiveElementsOnTop;let d;for(i.dataset&&i.dataset.draw(e,r,l,a),d=l;d<l+a;++d){const h=s[d];h.hidden||(h.active&&u?o.push(h):h.draw(e,r))}for(d=0;d<o.length;++d)o[d].draw(e,r)}getStyle(e,n){const i=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(e||0,i)}getContext(e,n,i){const s=this.getDataset();let r;if(e>=0&&e<this._cachedMeta.data.length){const o=this._cachedMeta.data[e];r=o.$context||(o.$context=Fv(this.getContext(),e,o)),r.parsed=this.getParsed(e),r.raw=s.data[e],r.index=r.dataIndex=e}else r=this.$context||(this.$context=zv(this.chart.getContext(),this.index)),r.dataset=s,r.index=r.datasetIndex=this.index;return r.active=!!n,r.mode=i,r}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",i){const s=n==="active",r=this._cachedDataOpts,o=e+"-"+n,l=r[o],a=this.enableOptionSharing&&Ps(i);if(l)return Rd(l,a);const u=this.chart.config,d=u.datasetElementScopeKeys(this._type,e),h=s?[`${e}Hover`,"hover",e,""]:[e,""],f=u.getOptionScopes(this.getDataset(),d),p=Object.keys(ie.elements[e]),g=()=>this.getContext(i,s,n),x=u.resolveNamedOptions(f,p,g,h);return x.$shared&&(x.$shared=a,r[o]=Object.freeze(Rd(x,a))),x}_resolveAnimations(e,n,i){const s=this.chart,r=this._cachedDataOpts,o=`animation-${n}`,l=r[o];if(l)return l;let a;if(s.options.animation!==!1){const d=this.chart.config,h=d.datasetAnimationScopeKeys(this._type,n),f=d.getOptionScopes(this.getDataset(),h);a=d.createResolver(f,this.getContext(e,i,n))}const u=new jm(s,a&&a.animations);return a&&a._cacheable&&(r[o]=Object.freeze(u)),u}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||kl(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const i=this.resolveDataElementOptions(e,n),s=this._sharedOptions,r=this.getSharedOptions(i),o=this.includeOptions(n,r)||r!==s;return this.updateSharedOptions(r,n,i),{sharedOptions:r,includeOptions:o}}updateElement(e,n,i,s){kl(s)?Object.assign(e,i):this._resolveAnimations(n,s).update(e,i)}updateSharedOptions(e,n,i){e&&!kl(n)&&this._resolveAnimations(void 0,n).update(e,i)}_setStyle(e,n,i,s){e.active=s;const r=this.getStyle(n,s);this._resolveAnimations(n,i,s).update(e,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(e,n,i){this._setStyle(e,i,"active",!1)}setHoverStyle(e,n,i){this._setStyle(e,i,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,i=this._cachedMeta.data;for(const[l,a,u]of this._syncList)this[l](a,u);this._syncList=[];const s=i.length,r=n.length,o=Math.min(r,s);o&&this.parse(0,o),r>s?this._insertElements(s,r-s,e):r<s&&this._removeElements(r,s-r)}_insertElements(e,n,i=!0){const s=this._cachedMeta,r=s.data,o=e+n;let l;const a=u=>{for(u.length+=n,l=u.length-1;l>=o;l--)u[l]=u[l-n]};for(a(r),l=e;l<o;++l)r[l]=new this.dataElementType;this._parsing&&a(s._parsed),this.parse(e,n),i&&this.updateElements(r,e,n,"reset")}updateElements(e,n,i,s){}_removeElements(e,n){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(e,n);i._stacked&&Oi(i,s)}i.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,i,s]=e;this[n](i,s)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const i=arguments.length-2;i&&this._sync(["_insertElements",e,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}P(pt,"defaults",{}),P(pt,"datasetElementType",null),P(pt,"dataElementType",null);function Vv(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let i=[];for(let s=0,r=n.length;s<r;s++)i=i.concat(n[s].controller.getAllParsedValues(t));t._cache.$bar=rm(i.sort((s,r)=>s-r))}return t._cache.$bar}function Hv(t){const e=t.iScale,n=Vv(e,t.type);let i=e._length,s,r,o,l;const a=()=>{o===32767||o===-32768||(Ps(l)&&(i=Math.min(i,Math.abs(o-l)||i)),l=o)};for(s=0,r=n.length;s<r;++s)o=e.getPixelForValue(n[s]),a();for(l=void 0,s=0,r=e.ticks.length;s<r;++s)o=e.getPixelForTick(s),a();return i}function Wv(t,e,n,i){const s=n.barThickness;let r,o;return z(s)?(r=e.min*n.categoryPercentage,o=n.barPercentage):(r=s*i,o=1),{chunk:r/i,ratio:o,start:e.pixels[t]-r/2}}function $v(t,e,n,i){const s=e.pixels,r=s[t];let o=t>0?s[t-1]:null,l=t<s.length-1?s[t+1]:null;const a=n.categoryPercentage;o===null&&(o=r-(l===null?e.end-e.start:l-r)),l===null&&(l=r+r-o);const u=r-(r-Math.min(o,l))/2*a;return{chunk:Math.abs(l-o)/2*a/i,ratio:n.barPercentage,start:u}}function Uv(t,e,n,i){const s=n.parse(t[0],i),r=n.parse(t[1],i),o=Math.min(s,r),l=Math.max(s,r);let a=o,u=l;Math.abs(o)>Math.abs(l)&&(a=l,u=o),e[n.axis]=u,e._custom={barStart:a,barEnd:u,start:s,end:r,min:o,max:l}}function Mm(t,e,n,i){return ne(t)?Uv(t,e,n,i):e[n.axis]=n.parse(t,i),e}function Id(t,e,n,i){const s=t.iScale,r=t.vScale,o=s.getLabels(),l=s===r,a=[];let u,d,h,f;for(u=n,d=n+i;u<d;++u)f=e[u],h={},h[s.axis]=l||s.parse(o[u],u),a.push(Mm(f,h,r,u));return a}function Sl(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function Kv(t,e,n){return t!==0?jt(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function Yv(t){let e,n,i,s,r;return t.horizontal?(e=t.base>t.x,n="left",i="right"):(e=t.base<t.y,n="bottom",i="top"),e?(s="end",r="start"):(s="start",r="end"),{start:n,end:i,reverse:e,top:s,bottom:r}}function Gv(t,e,n,i){let s=e.borderSkipped;const r={};if(!s){t.borderSkipped=r;return}if(s===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:o,end:l,reverse:a,top:u,bottom:d}=Yv(t);s==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===i?s=u:(n._bottom||0)===i?s=d:(r[zd(d,o,l,a)]=!0,s=u)),r[zd(s,o,l,a)]=!0,t.borderSkipped=r}function zd(t,e,n,i){return i?(t=Xv(t,e,n),t=Fd(t,n,e)):t=Fd(t,e,n),t}function Xv(t,e,n){return t===e?n:t===n?e:t}function Fd(t,e,n){return t==="start"?e:t==="end"?n:t}function Qv(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class Ar extends pt{parsePrimitiveData(e,n,i,s){return Id(e,n,i,s)}parseArrayData(e,n,i,s){return Id(e,n,i,s)}parseObjectData(e,n,i,s){const{iScale:r,vScale:o}=e,{xAxisKey:l="x",yAxisKey:a="y"}=this._parsing,u=r.axis==="x"?l:a,d=o.axis==="x"?l:a,h=[];let f,p,g,x;for(f=i,p=i+s;f<p;++f)x=n[f],g={},g[r.axis]=r.parse(fn(x,u),f),h.push(Mm(fn(x,d),g,o,f));return h}updateRangeFromParsed(e,n,i,s){super.updateRangeFromParsed(e,n,i,s);const r=i._custom;r&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,r.min),e.max=Math.max(e.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:i,vScale:s}=n,r=this.getParsed(e),o=r._custom,l=Sl(o)?"["+o.start+", "+o.end+"]":""+s.getLabelForValue(r[s.axis]);return{label:""+i.getLabelForValue(r[i.axis]),value:l}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,i,s){const r=s==="reset",{index:o,_cachedMeta:{vScale:l}}=this,a=l.getBasePixel(),u=l.isHorizontal(),d=this._getRuler(),{sharedOptions:h,includeOptions:f}=this._getSharedOptions(n,s);for(let p=n;p<n+i;p++){const g=this.getParsed(p),x=r||z(g[l.axis])?{base:a,head:a}:this._calculateBarValuePixels(p),b=this._calculateBarIndexPixels(p,d),m=(g._stacks||{})[l.axis],y={horizontal:u,base:x.base,enableBorderRadius:!m||Sl(g._custom)||o===m._top||o===m._bottom,x:u?x.head:b.center,y:u?b.center:x.head,height:u?b.size:Math.abs(x.size),width:u?Math.abs(x.size):b.size};f&&(y.options=h||this.resolveDataElementOptions(p,e[p].active?"active":s));const v=y.options||e[p].options;Gv(y,v,m,o),Qv(y,v,d.ratio),this.updateElement(e[p],p,y,s)}}_getStacks(e,n){const{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),r=i.options.stacked,o=[],l=this._cachedMeta.controller.getParsed(n),a=l&&l[i.axis],u=d=>{const h=d._parsed.find(p=>p[i.axis]===a),f=h&&h[d.vScale.axis];if(z(f)||isNaN(f))return!0};for(const d of s)if(!(n!==void 0&&u(d))&&((r===!1||o.indexOf(d.stack)===-1||r===void 0&&d.stack===void 0)&&o.push(d.stack),d.index===e))break;return o.length||o.push(void 0),o}_getStackCount(e){return this._getStacks(void 0,e).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const e=this.chart.scales,n=this.chart.options.indexAxis;return Object.keys(e).filter(i=>e[i].axis===n).shift()}_getAxis(){const e={},n=this.getFirstScaleIdForIndexAxis();for(const i of this.chart.data.datasets)e[R(this.chart.options.indexAxis==="x"?i.xAxisID:i.yAxisID,n)]=!0;return Object.keys(e)}_getStackIndex(e,n,i){const s=this._getStacks(e,i),r=n!==void 0?s.indexOf(n):-1;return r===-1?s.length-1:r}_getRuler(){const e=this.options,n=this._cachedMeta,i=n.iScale,s=[];let r,o;for(r=0,o=n.data.length;r<o;++r)s.push(i.getPixelForValue(this.getParsed(r)[i.axis],r));const l=e.barThickness;return{min:l||Hv(n),pixels:s,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:e.grouped,ratio:l?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:i,index:s},options:{base:r,minBarLength:o}}=this,l=r||0,a=this.getParsed(e),u=a._custom,d=Sl(u);let h=a[n.axis],f=0,p=i?this.applyStack(n,a,i):h,g,x;p!==h&&(f=p-h,p=h),d&&(h=u.barStart,p=u.barEnd-u.barStart,h!==0&&jt(h)!==jt(u.barEnd)&&(f=0),f+=h);const b=!z(r)&&!d?r:f;let m=n.getPixelForValue(b);if(this.chart.getDataVisibility(e)?g=n.getPixelForValue(f+p):g=m,x=g-m,Math.abs(x)<o){x=Kv(x,n,l)*o,h===l&&(m-=x/2);const y=n.getPixelForDecimal(0),v=n.getPixelForDecimal(1),_=Math.min(y,v),w=Math.max(y,v);m=Math.max(Math.min(m,w),_),g=m+x,i&&!d&&(a._stacks[n.axis]._visualValues[s]=n.getValueForPixel(g)-n.getValueForPixel(m))}if(m===n.getPixelForValue(l)){const y=jt(x)*n.getLineWidthForValue(l)/2;m+=y,x-=y}return{size:x,base:m,head:g,center:g+x/2}}_calculateBarIndexPixels(e,n){const i=n.scale,s=this.options,r=s.skipNull,o=R(s.maxBarThickness,1/0);let l,a;const u=this._getAxisCount();if(n.grouped){const d=r?this._getStackCount(e):n.stackCount,h=s.barThickness==="flex"?$v(e,n,s,d*u):Wv(e,n,s,d*u),f=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,p=this._getAxis().indexOf(R(f,this.getFirstScaleIdForIndexAxis())),g=this._getStackIndex(this.index,this._cachedMeta.stack,r?e:void 0)+p;l=h.start+h.chunk*g+h.chunk/2,a=Math.min(o,h.chunk*h.ratio)}else l=i.getPixelForValue(this.getParsed(e)[i.axis],e),a=Math.min(o,n.min*n.ratio);return{base:l-a/2,head:l+a/2,center:l,size:a}}draw(){const e=this._cachedMeta,n=e.vScale,i=e.data,s=i.length;let r=0;for(;r<s;++r)this.getParsed(r)[n.axis]!==null&&!i[r].hidden&&i[r].draw(this._ctx)}}P(Ar,"id","bar"),P(Ar,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),P(Ar,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class Or extends pt{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(e,n,i,s){const r=super.parsePrimitiveData(e,n,i,s);for(let o=0;o<r.length;o++)r[o]._custom=this.resolveDataElementOptions(o+i).radius;return r}parseArrayData(e,n,i,s){const r=super.parseArrayData(e,n,i,s);for(let o=0;o<r.length;o++){const l=n[i+o];r[o]._custom=R(l[2],this.resolveDataElementOptions(o+i).radius)}return r}parseObjectData(e,n,i,s){const r=super.parseObjectData(e,n,i,s);for(let o=0;o<r.length;o++){const l=n[i+o];r[o]._custom=R(l&&l.r&&+l.r,this.resolveDataElementOptions(o+i).radius)}return r}getMaxOverflow(){const e=this._cachedMeta.data;let n=0;for(let i=e.length-1;i>=0;--i)n=Math.max(n,e[i].size(this.resolveDataElementOptions(i))/2);return n>0&&n}getLabelAndValue(e){const n=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=n,o=this.getParsed(e),l=s.getLabelForValue(o.x),a=r.getLabelForValue(o.y),u=o._custom;return{label:i[e]||"",value:"("+l+", "+a+(u?", "+u:"")+")"}}update(e){const n=this._cachedMeta.data;this.updateElements(n,0,n.length,e)}updateElements(e,n,i,s){const r=s==="reset",{iScale:o,vScale:l}=this._cachedMeta,{sharedOptions:a,includeOptions:u}=this._getSharedOptions(n,s),d=o.axis,h=l.axis;for(let f=n;f<n+i;f++){const p=e[f],g=!r&&this.getParsed(f),x={},b=x[d]=r?o.getPixelForDecimal(.5):o.getPixelForValue(g[d]),m=x[h]=r?l.getBasePixel():l.getPixelForValue(g[h]);x.skip=isNaN(b)||isNaN(m),u&&(x.options=a||this.resolveDataElementOptions(f,p.active?"active":s),r&&(x.options.radius=0)),this.updateElement(p,f,x,s)}}resolveDataElementOptions(e,n){const i=this.getParsed(e);let s=super.resolveDataElementOptions(e,n);s.$shared&&(s=Object.assign({},s,{$shared:!1}));const r=s.radius;return n!=="active"&&(s.radius=0),s.radius+=R(i&&i._custom,r),s}}P(Or,"id","bubble"),P(Or,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),P(Or,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function qv(t,e,n){let i=1,s=1,r=0,o=0;if(e<ee){const l=t,a=l+e,u=Math.cos(l),d=Math.sin(l),h=Math.cos(a),f=Math.sin(a),p=(v,_,w)=>Es(v,l,a,!0)?1:Math.max(_,_*n,w,w*n),g=(v,_,w)=>Es(v,l,a,!0)?-1:Math.min(_,_*n,w,w*n),x=p(0,u,h),b=p(he,d,f),m=g(H,u,h),y=g(H+he,d,f);i=(x-m)/2,s=(b-y)/2,r=-(x+m)/2,o=-(b+y)/2}return{ratioX:i,ratioY:s,offsetX:r,offsetY:o}}class En extends pt{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let r=a=>+i[a];if(B(i[e])){const{key:a="value"}=this._parsing;r=u=>+fn(i[u],a)}let o,l;for(o=e,l=e+n;o<l;++o)s._parsed[o]=r(o)}}_getRotation(){return dt(this.options.rotation-90)}_getCircumference(){return dt(this.options.circumference)}_getRotationExtents(){let e=ee,n=-ee;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,r=s._getRotation(),o=s._getCircumference();e=Math.min(e,r),n=Math.max(n,r+o)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:i}=n,s=this._cachedMeta,r=s.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,l=Math.max((Math.min(i.width,i.height)-o)/2,0),a=Math.min(cy(this.options.cutout,l),1),u=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:p,offsetX:g,offsetY:x}=qv(h,d,a),b=(i.width-o)/f,m=(i.height-o)/p,y=Math.max(Math.min(b,m)/2,0),v=em(this.options.radius,y),_=Math.max(v*a,0),w=(v-_)/this._getVisibleDatasetWeightTotal();this.offsetX=g*v,this.offsetY=x*v,s.total=this.calculateTotal(),this.outerRadius=v-w*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-w*u,0),this.updateElements(r,0,r.length,e)}_circumference(e,n){const i=this.options,s=this._cachedMeta,r=this._getCircumference();return n&&i.animation.animateRotate||!this.chart.getDataVisibility(e)||s._parsed[e]===null||s.data[e].hidden?0:this.calculateCircumference(s._parsed[e]*r/ee)}updateElements(e,n,i,s){const r=s==="reset",o=this.chart,l=o.chartArea,u=o.options.animation,d=(l.left+l.right)/2,h=(l.top+l.bottom)/2,f=r&&u.animateScale,p=f?0:this.innerRadius,g=f?0:this.outerRadius,{sharedOptions:x,includeOptions:b}=this._getSharedOptions(n,s);let m=this._getRotation(),y;for(y=0;y<n;++y)m+=this._circumference(y,r);for(y=n;y<n+i;++y){const v=this._circumference(y,r),_=e[y],w={x:d+this.offsetX,y:h+this.offsetY,startAngle:m,endAngle:m+v,circumference:v,outerRadius:g,innerRadius:p};b&&(w.options=x||this.resolveDataElementOptions(y,_.active?"active":s)),m+=v,this.updateElement(_,y,w,s)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let i=0,s;for(s=0;s<n.length;s++){const r=e._parsed[s];r!==null&&!isNaN(r)&&this.chart.getDataVisibility(s)&&!n[s].hidden&&(i+=Math.abs(r))}return i}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?ee*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=Hs(n._parsed[e],i.options.locale);return{label:s[e]||"",value:r}}getMaxBorderWidth(e){let n=0;const i=this.chart;let s,r,o,l,a;if(!e){for(s=0,r=i.data.datasets.length;s<r;++s)if(i.isDatasetVisible(s)){o=i.getDatasetMeta(s),e=o.data,l=o.controller;break}}if(!e)return 0;for(s=0,r=e.length;s<r;++s)a=l.resolveDataElementOptions(s),a.borderAlign!=="inner"&&(n=Math.max(n,a.borderWidth||0,a.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let i=0,s=e.length;i<s;++i){const r=this.resolveDataElementOptions(i);n=Math.max(n,r.offset||0,r.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let i=0;i<e;++i)this.chart.isDatasetVisible(i)&&(n+=this._getRingWeight(i));return n}_getRingWeight(e){return Math.max(R(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}P(En,"id","doughnut"),P(En,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),P(En,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),P(En,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:i,color:s}}=e.legend.options;return n.labels.map((r,o)=>{const a=e.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:s,lineWidth:a.borderWidth,pointStyle:i,hidden:!e.getDataVisibility(o),index:o}})}return[]}},onClick(e,n,i){i.chart.toggleDataVisibility(n.index),i.chart.update()}}}});class Rr extends pt{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(e){const n=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=n,o=this.chart._animationsDisabled;let{start:l,count:a}=am(n,s,o);this._drawStart=l,this._drawCount=a,cm(n)&&(l=0,a=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;const u=this.resolveDatasetElementOptions(e);this.options.showLine||(u.borderWidth=0),u.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:u},e),this.updateElements(s,l,a,e)}updateElements(e,n,i,s){const r=s==="reset",{iScale:o,vScale:l,_stacked:a,_dataset:u}=this._cachedMeta,{sharedOptions:d,includeOptions:h}=this._getSharedOptions(n,s),f=o.axis,p=l.axis,{spanGaps:g,segment:x}=this.options,b=_i(g)?g:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||r||s==="none",y=n+i,v=e.length;let _=n>0&&this.getParsed(n-1);for(let w=0;w<v;++w){const j=e[w],k=m?j:{};if(w<n||w>=y){k.skip=!0;continue}const S=this.getParsed(w),C=z(S[p]),M=k[f]=o.getPixelForValue(S[f],w),A=k[p]=r||C?l.getBasePixel():l.getPixelForValue(a?this.applyStack(l,S,a):S[p],w);k.skip=isNaN(M)||isNaN(A)||C,k.stop=w>0&&Math.abs(S[f]-_[f])>b,x&&(k.parsed=S,k.raw=u.data[w]),h&&(k.options=d||this.resolveDataElementOptions(w,j.active?"active":s)),m||this.updateElement(j,w,k,s),_=S}}getMaxOverflow(){const e=this._cachedMeta,n=e.dataset,i=n.options&&n.options.borderWidth||0,s=e.data||[];if(!s.length)return i;const r=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,r,o)/2}draw(){const e=this._cachedMeta;e.dataset.updateControlPoints(this.chart.chartArea,e.iScale.axis),super.draw()}}P(Rr,"id","line"),P(Rr,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),P(Rr,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class cs extends pt{constructor(e,n){super(e,n),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(e){const n=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=Hs(n._parsed[e].r,i.options.locale);return{label:s[e]||"",value:r}}parseObjectData(e,n,i,s){return xm.bind(this)(e,n,i,s)}update(e){const n=this._cachedMeta.data;this._updateRadius(),this.updateElements(n,0,n.length,e)}getMinMax(){const e=this._cachedMeta,n={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return e.data.forEach((i,s)=>{const r=this.getParsed(s).r;!isNaN(r)&&this.chart.getDataVisibility(s)&&(r<n.min&&(n.min=r),r>n.max&&(n.max=r))}),n}_updateRadius(){const e=this.chart,n=e.chartArea,i=e.options,s=Math.min(n.right-n.left,n.bottom-n.top),r=Math.max(s/2,0),o=Math.max(i.cutoutPercentage?r/100*i.cutoutPercentage:1,0),l=(r-o)/e.getVisibleDatasetCount();this.outerRadius=r-l*this.index,this.innerRadius=this.outerRadius-l}updateElements(e,n,i,s){const r=s==="reset",o=this.chart,a=o.options.animation,u=this._cachedMeta.rScale,d=u.xCenter,h=u.yCenter,f=u.getIndexAngle(0)-.5*H;let p=f,g;const x=360/this.countVisibleElements();for(g=0;g<n;++g)p+=this._computeAngle(g,s,x);for(g=n;g<n+i;g++){const b=e[g];let m=p,y=p+this._computeAngle(g,s,x),v=o.getDataVisibility(g)?u.getDistanceFromCenterForValue(this.getParsed(g).r):0;p=y,r&&(a.animateScale&&(v=0),a.animateRotate&&(m=y=f));const _={x:d,y:h,innerRadius:0,outerRadius:v,startAngle:m,endAngle:y,options:this.resolveDataElementOptions(g,b.active?"active":s)};this.updateElement(b,g,_,s)}}countVisibleElements(){const e=this._cachedMeta;let n=0;return e.data.forEach((i,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&n++}),n}_computeAngle(e,n,i){return this.chart.getDataVisibility(e)?dt(this.resolveDataElementOptions(e,n).angle||i):0}}P(cs,"id","polarArea"),P(cs,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),P(cs,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:i,color:s}}=e.legend.options;return n.labels.map((r,o)=>{const a=e.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:s,lineWidth:a.borderWidth,pointStyle:i,hidden:!e.getDataVisibility(o),index:o}})}return[]}},onClick(e,n,i){i.chart.toggleDataVisibility(n.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Pa extends En{}P(Pa,"id","pie"),P(Pa,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class Ir extends pt{getLabelAndValue(e){const n=this._cachedMeta.vScale,i=this.getParsed(e);return{label:n.getLabels()[e],value:""+n.getLabelForValue(i[n.axis])}}parseObjectData(e,n,i,s){return xm.bind(this)(e,n,i,s)}update(e){const n=this._cachedMeta,i=n.dataset,s=n.data||[],r=n.iScale.getLabels();if(i.points=s,e!=="resize"){const o=this.resolveDatasetElementOptions(e);this.options.showLine||(o.borderWidth=0);const l={_loop:!0,_fullLoop:r.length===s.length,options:o};this.updateElement(i,void 0,l,e)}this.updateElements(s,0,s.length,e)}updateElements(e,n,i,s){const r=this._cachedMeta.rScale,o=s==="reset";for(let l=n;l<n+i;l++){const a=e[l],u=this.resolveDataElementOptions(l,a.active?"active":s),d=r.getPointPositionForValue(l,this.getParsed(l).r),h=o?r.xCenter:d.x,f=o?r.yCenter:d.y,p={x:h,y:f,angle:d.angle,skip:isNaN(h)||isNaN(f),options:u};this.updateElement(a,l,p,s)}}}P(Ir,"id","radar"),P(Ir,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),P(Ir,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class zr extends pt{getLabelAndValue(e){const n=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=n,o=this.getParsed(e),l=s.getLabelForValue(o.x),a=r.getLabelForValue(o.y);return{label:i[e]||"",value:"("+l+", "+a+")"}}update(e){const n=this._cachedMeta,{data:i=[]}=n,s=this.chart._animationsDisabled;let{start:r,count:o}=am(n,i,s);if(this._drawStart=r,this._drawCount=o,cm(n)&&(r=0,o=i.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:l,_dataset:a}=n;l._chart=this.chart,l._datasetIndex=this.index,l._decimated=!!a._decimated,l.points=i;const u=this.resolveDatasetElementOptions(e);u.segment=this.options.segment,this.updateElement(l,void 0,{animated:!s,options:u},e)}else this.datasetElementType&&(delete n.dataset,this.datasetElementType=!1);this.updateElements(i,r,o,e)}addElements(){const{showLine:e}=this.options;!this.datasetElementType&&e&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(e,n,i,s){const r=s==="reset",{iScale:o,vScale:l,_stacked:a,_dataset:u}=this._cachedMeta,d=this.resolveDataElementOptions(n,s),h=this.getSharedOptions(d),f=this.includeOptions(s,h),p=o.axis,g=l.axis,{spanGaps:x,segment:b}=this.options,m=_i(x)?x:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||r||s==="none";let v=n>0&&this.getParsed(n-1);for(let _=n;_<n+i;++_){const w=e[_],j=this.getParsed(_),k=y?w:{},S=z(j[g]),C=k[p]=o.getPixelForValue(j[p],_),M=k[g]=r||S?l.getBasePixel():l.getPixelForValue(a?this.applyStack(l,j,a):j[g],_);k.skip=isNaN(C)||isNaN(M)||S,k.stop=_>0&&Math.abs(j[p]-v[p])>m,b&&(k.parsed=j,k.raw=u.data[_]),f&&(k.options=h||this.resolveDataElementOptions(_,w.active?"active":s)),y||this.updateElement(w,_,k,s),v=j}this.updateSharedOptions(h,s,d)}getMaxOverflow(){const e=this._cachedMeta,n=e.data||[];if(!this.options.showLine){let l=0;for(let a=n.length-1;a>=0;--a)l=Math.max(l,n[a].size(this.resolveDataElementOptions(a))/2);return l>0&&l}const i=e.dataset,s=i.options&&i.options.borderWidth||0;if(!n.length)return s;const r=n[0].size(this.resolveDataElementOptions(0)),o=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,r,o)/2}}P(zr,"id","scatter"),P(zr,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),P(zr,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var Zv=Object.freeze({__proto__:null,BarController:Ar,BubbleController:Or,DoughnutController:En,LineController:Rr,PieController:Pa,PolarAreaController:cs,RadarController:Ir,ScatterController:zr});function _n(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Xc{constructor(e){P(this,"options");this.options=e||{}}static override(e){Object.assign(Xc.prototype,e)}init(){}formats(){return _n()}parse(){return _n()}format(){return _n()}add(){return _n()}diff(){return _n()}startOf(){return _n()}endOf(){return _n()}}var Jv={_date:Xc};function e1(t,e,n,i){const{controller:s,data:r,_sorted:o}=t,l=s._cachedMeta.iScale,a=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(l&&e===l.axis&&e!=="r"&&o&&r.length){const u=l._reversePixels?wy:Rt;if(i){if(s._sharedOptions){const d=r[0],h=typeof d.getRange=="function"&&d.getRange(e);if(h){const f=u(r,e,n-h),p=u(r,e,n+h);return{lo:f.lo,hi:p.hi}}}}else{const d=u(r,e,n);if(a){const{vScale:h}=s._cachedMeta,{_parsed:f}=t,p=f.slice(0,d.lo+1).reverse().findIndex(x=>!z(x[h.axis]));d.lo-=Math.max(0,p);const g=f.slice(d.hi).findIndex(x=>!z(x[h.axis]));d.hi+=Math.max(0,g)}return d}}return{lo:0,hi:r.length-1}}function Ws(t,e,n,i,s){const r=t.getSortedVisibleDatasetMetas(),o=n[e];for(let l=0,a=r.length;l<a;++l){const{index:u,data:d}=r[l],{lo:h,hi:f}=e1(r[l],e,o,s);for(let p=h;p<=f;++p){const g=d[p];g.skip||i(g,u,p)}}}function t1(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(i,s){const r=e?Math.abs(i.x-s.x):0,o=n?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}function jl(t,e,n,i,s){const r=[];return!s&&!t.isPointInArea(e)||Ws(t,n,e,function(l,a,u){!s&&!It(l,t.chartArea,0)||l.inRange(e.x,e.y,i)&&r.push({element:l,datasetIndex:a,index:u})},!0),r}function n1(t,e,n,i){let s=[];function r(o,l,a){const{startAngle:u,endAngle:d}=o.getProps(["startAngle","endAngle"],i),{angle:h}=im(o,{x:e.x,y:e.y});Es(h,u,d)&&s.push({element:o,datasetIndex:l,index:a})}return Ws(t,n,e,r),s}function i1(t,e,n,i,s,r){let o=[];const l=t1(n);let a=Number.POSITIVE_INFINITY;function u(d,h,f){const p=d.inRange(e.x,e.y,s);if(i&&!p)return;const g=d.getCenterPoint(s);if(!(!!r||t.isPointInArea(g))&&!p)return;const b=l(e,g);b<a?(o=[{element:d,datasetIndex:h,index:f}],a=b):b===a&&o.push({element:d,datasetIndex:h,index:f})}return Ws(t,n,e,u),o}function Nl(t,e,n,i,s,r){return!r&&!t.isPointInArea(e)?[]:n==="r"&&!i?n1(t,e,n,s):i1(t,e,n,i,s,r)}function Bd(t,e,n,i,s){const r=[],o=n==="x"?"inXRange":"inYRange";let l=!1;return Ws(t,n,e,(a,u,d)=>{a[o]&&a[o](e[n],s)&&(r.push({element:a,datasetIndex:u,index:d}),l=l||a.inRange(e.x,e.y,s))}),i&&!l?[]:r}var s1={evaluateInteractionItems:Ws,modes:{index(t,e,n,i){const s=jn(e,t),r=n.axis||"x",o=n.includeInvisible||!1,l=n.intersect?jl(t,s,r,i,o):Nl(t,s,r,!1,i,o),a=[];return l.length?(t.getSortedVisibleDatasetMetas().forEach(u=>{const d=l[0].index,h=u.data[d];h&&!h.skip&&a.push({element:h,datasetIndex:u.index,index:d})}),a):[]},dataset(t,e,n,i){const s=jn(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;let l=n.intersect?jl(t,s,r,i,o):Nl(t,s,r,!1,i,o);if(l.length>0){const a=l[0].datasetIndex,u=t.getDatasetMeta(a).data;l=[];for(let d=0;d<u.length;++d)l.push({element:u[d],datasetIndex:a,index:d})}return l},point(t,e,n,i){const s=jn(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;return jl(t,s,r,i,o)},nearest(t,e,n,i){const s=jn(e,t),r=n.axis||"xy",o=n.includeInvisible||!1;return Nl(t,s,r,n.intersect,i,o)},x(t,e,n,i){const s=jn(e,t);return Bd(t,s,"x",n.intersect,i)},y(t,e,n,i){const s=jn(e,t);return Bd(t,s,"y",n.intersect,i)}}};const Cm=["left","top","right","bottom"];function Ri(t,e){return t.filter(n=>n.pos===e)}function Vd(t,e){return t.filter(n=>Cm.indexOf(n.pos)===-1&&n.box.axis===e)}function Ii(t,e){return t.sort((n,i)=>{const s=e?i:n,r=e?n:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function r1(t){const e=[];let n,i,s,r,o,l;for(n=0,i=(t||[]).length;n<i;++n)s=t[n],{position:r,options:{stack:o,stackWeight:l=1}}=s,e.push({index:n,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:o&&r+o,stackWeight:l});return e}function o1(t){const e={};for(const n of t){const{stack:i,pos:s,stackWeight:r}=n;if(!i||!Cm.includes(s))continue;const o=e[i]||(e[i]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return e}function l1(t,e){const n=o1(t),{vBoxMaxWidth:i,hBoxMaxHeight:s}=e;let r,o,l;for(r=0,o=t.length;r<o;++r){l=t[r];const{fullSize:a}=l.box,u=n[l.stack],d=u&&l.stackWeight/u.weight;l.horizontal?(l.width=d?d*i:a&&e.availableWidth,l.height=s):(l.width=i,l.height=d?d*s:a&&e.availableHeight)}return n}function a1(t){const e=r1(t),n=Ii(e.filter(u=>u.box.fullSize),!0),i=Ii(Ri(e,"left"),!0),s=Ii(Ri(e,"right")),r=Ii(Ri(e,"top"),!0),o=Ii(Ri(e,"bottom")),l=Vd(e,"x"),a=Vd(e,"y");return{fullSize:n,leftAndTop:i.concat(r),rightAndBottom:s.concat(a).concat(o).concat(l),chartArea:Ri(e,"chartArea"),vertical:i.concat(s).concat(a),horizontal:r.concat(o).concat(l)}}function Hd(t,e,n,i){return Math.max(t[n],e[n])+Math.max(t[i],e[i])}function Pm(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function c1(t,e,n,i){const{pos:s,box:r}=n,o=t.maxPadding;if(!B(s)){n.size&&(t[s]-=n.size);const h=i[n.stack]||{size:0,count:1};h.size=Math.max(h.size,n.horizontal?r.height:r.width),n.size=h.size/h.count,t[s]+=n.size}r.getPadding&&Pm(o,r.getPadding());const l=Math.max(0,e.outerWidth-Hd(o,t,"left","right")),a=Math.max(0,e.outerHeight-Hd(o,t,"top","bottom")),u=l!==t.w,d=a!==t.h;return t.w=l,t.h=a,n.horizontal?{same:u,other:d}:{same:d,other:u}}function u1(t){const e=t.maxPadding;function n(i){const s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function d1(t,e){const n=e.maxPadding;function i(s){const r={left:0,top:0,right:0,bottom:0};return s.forEach(o=>{r[o]=Math.max(e[o],n[o])}),r}return i(t?["left","right"]:["top","bottom"])}function Ki(t,e,n,i){const s=[];let r,o,l,a,u,d;for(r=0,o=t.length,u=0;r<o;++r){l=t[r],a=l.box,a.update(l.width||e.w,l.height||e.h,d1(l.horizontal,e));const{same:h,other:f}=c1(e,n,l,i);u|=h&&s.length,d=d||f,a.fullSize||s.push(l)}return u&&Ki(s,e,n,i)||d}function mr(t,e,n,i,s){t.top=n,t.left=e,t.right=e+i,t.bottom=n+s,t.width=i,t.height=s}function Wd(t,e,n,i){const s=n.padding;let{x:r,y:o}=e;for(const l of t){const a=l.box,u=i[l.stack]||{count:1,placed:0,weight:1},d=l.stackWeight/u.weight||1;if(l.horizontal){const h=e.w*d,f=u.size||a.height;Ps(u.start)&&(o=u.start),a.fullSize?mr(a,s.left,o,n.outerWidth-s.right-s.left,f):mr(a,e.left+u.placed,o,h,f),u.start=o,u.placed+=h,o=a.bottom}else{const h=e.h*d,f=u.size||a.width;Ps(u.start)&&(r=u.start),a.fullSize?mr(a,r,s.top,f,n.outerHeight-s.bottom-s.top):mr(a,r,e.top+u.placed,f,h),u.start=r,u.placed+=h,r=a.right}}e.x=r,e.y=o}var Pe={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,i){if(!t)return;const s=Le(t.options.layout.padding),r=Math.max(e-s.width,0),o=Math.max(n-s.height,0),l=a1(t.boxes),a=l.vertical,u=l.horizontal;$(t.boxes,x=>{typeof x.beforeLayout=="function"&&x.beforeLayout()});const d=a.reduce((x,b)=>b.box.options&&b.box.options.display===!1?x:x+1,0)||1,h=Object.freeze({outerWidth:e,outerHeight:n,padding:s,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/d,hBoxMaxHeight:o/2}),f=Object.assign({},s);Pm(f,Le(i));const p=Object.assign({maxPadding:f,w:r,h:o,x:s.left,y:s.top},s),g=l1(a.concat(u),h);Ki(l.fullSize,p,h,g),Ki(a,p,h,g),Ki(u,p,h,g)&&Ki(a,p,h,g),u1(p),Wd(l.leftAndTop,p,h,g),p.x+=p.w,p.y+=p.h,Wd(l.rightAndBottom,p,h,g),t.chartArea={left:p.left,top:p.top,right:p.left+p.w,bottom:p.top+p.h,height:p.h,width:p.w},$(l.chartArea,x=>{const b=x.box;Object.assign(b,t.chartArea),b.update(p.w,p.h,{left:0,top:0,right:0,bottom:0})})}};class Em{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,i){}removeEventListener(e,n,i){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,i,s){return n=Math.max(0,n||e.width),i=i||e.height,{width:n,height:Math.max(0,s?Math.floor(n/s):i)}}isAttached(e){return!0}updateConfig(e){}}class h1 extends Em{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const Fr="$chartjs",f1={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},$d=t=>t===null||t==="";function p1(t,e){const n=t.style,i=t.getAttribute("height"),s=t.getAttribute("width");if(t[Fr]={initial:{height:i,width:s,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",$d(s)){const r=Md(t,"width");r!==void 0&&(t.width=r)}if($d(i))if(t.style.height==="")t.height=t.width/(e||2);else{const r=Md(t,"height");r!==void 0&&(t.height=r)}return t}const Lm=mv?{passive:!0}:!1;function m1(t,e,n){t&&t.addEventListener(e,n,Lm)}function g1(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,Lm)}function x1(t,e){const n=f1[t.type]||t.type,{x:i,y:s}=jn(t,e);return{type:n,chart:e,native:t,x:i!==void 0?i:null,y:s!==void 0?s:null}}function _o(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function y1(t,e,n){const i=t.canvas,s=new MutationObserver(r=>{let o=!1;for(const l of r)o=o||_o(l.addedNodes,i),o=o&&!_o(l.removedNodes,i);o&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}function v1(t,e,n){const i=t.canvas,s=new MutationObserver(r=>{let o=!1;for(const l of r)o=o||_o(l.removedNodes,i),o=o&&!_o(l.addedNodes,i);o&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}const Ds=new Map;let Ud=0;function Dm(){const t=window.devicePixelRatio;t!==Ud&&(Ud=t,Ds.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function b1(t,e){Ds.size||window.addEventListener("resize",Dm),Ds.set(t,e)}function _1(t){Ds.delete(t),Ds.size||window.removeEventListener("resize",Dm)}function w1(t,e,n){const i=t.canvas,s=i&&Gc(i);if(!s)return;const r=lm((l,a)=>{const u=s.clientWidth;n(l,a),u<s.clientWidth&&n()},window),o=new ResizeObserver(l=>{const a=l[0],u=a.contentRect.width,d=a.contentRect.height;u===0&&d===0||r(u,d)});return o.observe(s),b1(t,r),o}function Ml(t,e,n){n&&n.disconnect(),e==="resize"&&_1(t)}function k1(t,e,n){const i=t.canvas,s=lm(r=>{t.ctx!==null&&n(x1(r,t))},t);return m1(i,e,s),s}class S1 extends Em{acquireContext(e,n){const i=e&&e.getContext&&e.getContext("2d");return i&&i.canvas===e?(p1(e,n),i):null}releaseContext(e){const n=e.canvas;if(!n[Fr])return!1;const i=n[Fr].initial;["height","width"].forEach(r=>{const o=i[r];z(o)?n.removeAttribute(r):n.setAttribute(r,o)});const s=i.style||{};return Object.keys(s).forEach(r=>{n.style[r]=s[r]}),n.width=n.width,delete n[Fr],!0}addEventListener(e,n,i){this.removeEventListener(e,n);const s=e.$proxies||(e.$proxies={}),o={attach:y1,detach:v1,resize:w1}[n]||k1;s[n]=o(e,n,i)}removeEventListener(e,n){const i=e.$proxies||(e.$proxies={}),s=i[n];if(!s)return;({attach:Ml,detach:Ml,resize:Ml}[n]||g1)(e,n,s),i[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,i,s){return pv(e,n,i,s)}isAttached(e){const n=e&&Gc(e);return!!(n&&n.isConnected)}}function j1(t){return!Yc()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?h1:S1}class gt{constructor(){P(this,"x");P(this,"y");P(this,"active",!1);P(this,"options");P(this,"$animations")}tooltipPosition(e){const{x:n,y:i}=this.getProps(["x","y"],e);return{x:n,y:i}}hasValue(){return _i(this.x)&&_i(this.y)}getProps(e,n){const i=this.$animations;if(!n||!i)return this;const s={};return e.forEach(r=>{s[r]=i[r]&&i[r].active()?i[r]._to:this[r]}),s}}P(gt,"defaults",{}),P(gt,"defaultRoutes");function N1(t,e){const n=t.options.ticks,i=M1(t),s=Math.min(n.maxTicksLimit||i,i),r=n.major.enabled?P1(e):[],o=r.length,l=r[0],a=r[o-1],u=[];if(o>s)return E1(e,u,r,o/s),u;const d=C1(r,e,s);if(o>0){let h,f;const p=o>1?Math.round((a-l)/(o-1)):null;for(gr(e,u,d,z(p)?0:l-p,l),h=0,f=o-1;h<f;h++)gr(e,u,d,r[h],r[h+1]);return gr(e,u,d,a,z(p)?e.length:a+p),u}return gr(e,u,d),u}function M1(t){const e=t.options.offset,n=t._tickSize(),i=t._length/n+(e?0:1),s=t._maxLength/n;return Math.floor(Math.min(i,s))}function C1(t,e,n){const i=L1(t),s=e.length/n;if(!i)return Math.max(s,1);const r=xy(i);for(let o=0,l=r.length-1;o<l;o++){const a=r[o];if(a>s)return a}return Math.max(s,1)}function P1(t){const e=[];let n,i;for(n=0,i=t.length;n<i;n++)t[n].major&&e.push(n);return e}function E1(t,e,n,i){let s=0,r=n[0],o;for(i=Math.ceil(i),o=0;o<t.length;o++)o===r&&(e.push(t[o]),s++,r=n[s*i])}function gr(t,e,n,i,s){const r=R(i,0),o=Math.min(R(s,t.length),t.length);let l=0,a,u,d;for(n=Math.ceil(n),s&&(a=s-i,n=a/Math.floor(a/n)),d=r;d<0;)l++,d=Math.round(r+l*n);for(u=Math.max(r,0);u<o;u++)u===d&&(e.push(t[u]),l++,d=Math.round(r+l*n))}function L1(t){const e=t.length;let n,i;if(e<2)return!1;for(i=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==i)return!1;return i}const D1=t=>t==="left"?"right":t==="right"?"left":t,Kd=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,Yd=(t,e)=>Math.min(e||t,t);function Gd(t,e){const n=[],i=t.length/e,s=t.length;let r=0;for(;r<s;r+=i)n.push(t[Math.floor(r)]);return n}function T1(t,e,n){const i=t.ticks.length,s=Math.min(e,i-1),r=t._startPixel,o=t._endPixel,l=1e-6;let a=t.getPixelForTick(s),u;if(!(n&&(i===1?u=Math.max(a-r,o-a):e===0?u=(t.getPixelForTick(1)-a)/2:u=(a-t.getPixelForTick(s-1))/2,a+=s<e?u:-u,a<r-l||a>o+l)))return a}function A1(t,e){$(t,n=>{const i=n.gc,s=i.length/2;let r;if(s>e){for(r=0;r<s;++r)delete n.data[i[r]];i.splice(0,s)}})}function zi(t){return t.drawTicks?t.tickLength:0}function Xd(t,e){if(!t.display)return 0;const n=ge(t.font,e),i=Le(t.padding);return(ne(t.text)?t.text.length:1)*n.lineHeight+i.height}function O1(t,e){return yn(t,{scale:e,type:"scale"})}function R1(t,e,n){return yn(t,{tick:n,index:e,type:"tick"})}function I1(t,e,n){let i=Vc(t);return(n&&e!=="right"||!n&&e==="right")&&(i=D1(i)),i}function z1(t,e,n,i){const{top:s,left:r,bottom:o,right:l,chart:a}=t,{chartArea:u,scales:d}=a;let h=0,f,p,g;const x=o-s,b=l-r;if(t.isHorizontal()){if(p=Ne(i,r,l),B(n)){const m=Object.keys(n)[0],y=n[m];g=d[m].getPixelForValue(y)+x-e}else n==="center"?g=(u.bottom+u.top)/2+x-e:g=Kd(t,n,e);f=l-r}else{if(B(n)){const m=Object.keys(n)[0],y=n[m];p=d[m].getPixelForValue(y)-b+e}else n==="center"?p=(u.left+u.right)/2-b+e:p=Kd(t,n,e);g=Ne(i,o,s),h=n==="left"?-he:he}return{titleX:p,titleY:g,maxWidth:f,rotation:h}}class $n extends gt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:i,_suggestedMax:s}=this;return e=Ue(e,Number.POSITIVE_INFINITY),n=Ue(n,Number.NEGATIVE_INFINITY),i=Ue(i,Number.POSITIVE_INFINITY),s=Ue(s,Number.NEGATIVE_INFINITY),{min:Ue(e,i),max:Ue(n,s),minDefined:ue(e),maxDefined:ue(n)}}getMinMax(e){let{min:n,max:i,minDefined:s,maxDefined:r}=this.getUserBounds(),o;if(s&&r)return{min:n,max:i};const l=this.getMatchingVisibleMetas();for(let a=0,u=l.length;a<u;++a)o=l[a].controller.getMinMax(this,e),s||(n=Math.min(n,o.min)),r||(i=Math.max(i,o.max));return n=r&&n>i?i:n,i=s&&n>i?n:i,{min:Ue(n,Ue(i,n)),max:Ue(i,Ue(n,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){G(this.options.beforeUpdate,[this])}update(e,n,i){const{beginAtZero:s,grace:r,ticks:o}=this.options,l=o.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Ky(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const a=l<this.ticks.length;this._convertTicksToLabels(a?Gd(this.ticks,l):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=N1(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),a&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,i;this.isHorizontal()?(n=this.left,i=this.right):(n=this.top,i=this.bottom,e=!e),this._startPixel=n,this._endPixel=i,this._reversePixels=e,this._length=i-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){G(this.options.afterUpdate,[this])}beforeSetDimensions(){G(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){G(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),G(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){G(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let i,s,r;for(i=0,s=e.length;i<s;i++)r=e[i],r.label=G(n.callback,[r.value,i,e],this)}afterTickToLabelConversion(){G(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){G(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,i=Yd(this.ticks.length,e.ticks.maxTicksLimit),s=n.minRotation||0,r=n.maxRotation;let o=s,l,a,u;if(!this._isVisible()||!n.display||s>=r||i<=1||!this.isHorizontal()){this.labelRotation=s;return}const d=this._getLabelSizes(),h=d.widest.width,f=d.highest.height,p=ye(this.chart.width-h,0,this.maxWidth);l=e.offset?this.maxWidth/i:p/(i-1),h+6>l&&(l=p/(i-(e.offset?.5:1)),a=this.maxHeight-zi(e.grid)-n.padding-Xd(e.title,this.chart.options.font),u=Math.sqrt(h*h+f*f),o=Fc(Math.min(Math.asin(ye((d.highest.height+6)/l,-1,1)),Math.asin(ye(a/u,-1,1))-Math.asin(ye(f/u,-1,1)))),o=Math.max(s,Math.min(r,o))),this.labelRotation=o}afterCalculateLabelRotation(){G(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){G(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:i,title:s,grid:r}}=this,o=this._isVisible(),l=this.isHorizontal();if(o){const a=Xd(s,n.options.font);if(l?(e.width=this.maxWidth,e.height=zi(r)+a):(e.height=this.maxHeight,e.width=zi(r)+a),i.display&&this.ticks.length){const{first:u,last:d,widest:h,highest:f}=this._getLabelSizes(),p=i.padding*2,g=dt(this.labelRotation),x=Math.cos(g),b=Math.sin(g);if(l){const m=i.mirror?0:b*h.width+x*f.height;e.height=Math.min(this.maxHeight,e.height+m+p)}else{const m=i.mirror?0:x*h.width+b*f.height;e.width=Math.min(this.maxWidth,e.width+m+p)}this._calculatePadding(u,d,b,x)}}this._handleMargins(),l?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,i,s){const{ticks:{align:r,padding:o},position:l}=this.options,a=this.labelRotation!==0,u=l!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,p=0;a?u?(f=s*e.width,p=i*n.height):(f=i*e.height,p=s*n.width):r==="start"?p=n.width:r==="end"?f=e.width:r!=="inner"&&(f=e.width/2,p=n.width/2),this.paddingLeft=Math.max((f-d+o)*this.width/(this.width-d),0),this.paddingRight=Math.max((p-h+o)*this.width/(this.width-h),0)}else{let d=n.height/2,h=e.height/2;r==="start"?(d=0,h=e.height):r==="end"&&(d=n.height,h=0),this.paddingTop=d+o,this.paddingBottom=h+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){G(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,i;for(n=0,i=e.length;n<i;n++)z(e[n].label)&&(e.splice(n,1),i--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let i=this.ticks;n<i.length&&(i=Gd(i,n)),this._labelSizes=e=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,i){const{ctx:s,_longestTextCache:r}=this,o=[],l=[],a=Math.floor(n/Yd(n,i));let u=0,d=0,h,f,p,g,x,b,m,y,v,_,w;for(h=0;h<n;h+=a){if(g=e[h].label,x=this._resolveTickFontOptions(h),s.font=b=x.string,m=r[b]=r[b]||{data:{},gc:[]},y=x.lineHeight,v=_=0,!z(g)&&!ne(g))v=vo(s,m.data,m.gc,v,g),_=y;else if(ne(g))for(f=0,p=g.length;f<p;++f)w=g[f],!z(w)&&!ne(w)&&(v=vo(s,m.data,m.gc,v,w),_+=y);o.push(v),l.push(_),u=Math.max(v,u),d=Math.max(_,d)}A1(r,n);const j=o.indexOf(u),k=l.indexOf(d),S=C=>({width:o[C]||0,height:l[C]||0});return{first:S(0),last:S(n-1),widest:S(j),highest:S(k),widths:o,heights:l}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return _y(this._alignToPixels?bn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const i=n[e];return i.$context||(i.$context=R1(this.getContext(),e,i))}return this.$context||(this.$context=O1(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=dt(this.labelRotation),i=Math.abs(Math.cos(n)),s=Math.abs(Math.sin(n)),r=this._getLabelSizes(),o=e.autoSkipPadding||0,l=r?r.widest.width+o:0,a=r?r.highest.height+o:0;return this.isHorizontal()?a*i>l*s?l/i:a/s:a*s<l*i?a/i:l/s}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,i=this.chart,s=this.options,{grid:r,position:o,border:l}=s,a=r.offset,u=this.isHorizontal(),h=this.ticks.length+(a?1:0),f=zi(r),p=[],g=l.setContext(this.getContext()),x=g.display?g.width:0,b=x/2,m=function(K){return bn(i,K,x)};let y,v,_,w,j,k,S,C,M,A,I,se;if(o==="top")y=m(this.bottom),k=this.bottom-f,C=y-b,A=m(e.top)+b,se=e.bottom;else if(o==="bottom")y=m(this.top),A=e.top,se=m(e.bottom)-b,k=y+b,C=this.top+f;else if(o==="left")y=m(this.right),j=this.right-f,S=y-b,M=m(e.left)+b,I=e.right;else if(o==="right")y=m(this.left),M=e.left,I=m(e.right)-b,j=y+b,S=this.left+f;else if(n==="x"){if(o==="center")y=m((e.top+e.bottom)/2+.5);else if(B(o)){const K=Object.keys(o)[0],Q=o[K];y=m(this.chart.scales[K].getPixelForValue(Q))}A=e.top,se=e.bottom,k=y+b,C=k+f}else if(n==="y"){if(o==="center")y=m((e.left+e.right)/2);else if(B(o)){const K=Object.keys(o)[0],Q=o[K];y=m(this.chart.scales[K].getPixelForValue(Q))}j=y-b,S=j-f,M=e.left,I=e.right}const ke=R(s.ticks.maxTicksLimit,h),W=Math.max(1,Math.ceil(h/ke));for(v=0;v<h;v+=W){const K=this.getContext(v),Q=r.setContext(K),E=l.setContext(K),D=Q.lineWidth,O=Q.color,Y=E.dash||[],q=E.dashOffset,xt=Q.tickWidth,De=Q.tickColor,Nt=Q.tickBorderDash||[],Te=Q.tickBorderDashOffset;_=T1(this,v,a),_!==void 0&&(w=bn(i,_,D),u?j=S=M=I=w:k=C=A=se=w,p.push({tx1:j,ty1:k,tx2:S,ty2:C,x1:M,y1:A,x2:I,y2:se,width:D,color:O,borderDash:Y,borderDashOffset:q,tickWidth:xt,tickColor:De,tickBorderDash:Nt,tickBorderDashOffset:Te}))}return this._ticksLength=h,this._borderValue=y,p}_computeLabelItems(e){const n=this.axis,i=this.options,{position:s,ticks:r}=i,o=this.isHorizontal(),l=this.ticks,{align:a,crossAlign:u,padding:d,mirror:h}=r,f=zi(i.grid),p=f+d,g=h?-d:p,x=-dt(this.labelRotation),b=[];let m,y,v,_,w,j,k,S,C,M,A,I,se="middle";if(s==="top")j=this.bottom-g,k=this._getXAxisLabelAlignment();else if(s==="bottom")j=this.top+g,k=this._getXAxisLabelAlignment();else if(s==="left"){const W=this._getYAxisLabelAlignment(f);k=W.textAlign,w=W.x}else if(s==="right"){const W=this._getYAxisLabelAlignment(f);k=W.textAlign,w=W.x}else if(n==="x"){if(s==="center")j=(e.top+e.bottom)/2+p;else if(B(s)){const W=Object.keys(s)[0],K=s[W];j=this.chart.scales[W].getPixelForValue(K)+p}k=this._getXAxisLabelAlignment()}else if(n==="y"){if(s==="center")w=(e.left+e.right)/2-p;else if(B(s)){const W=Object.keys(s)[0],K=s[W];w=this.chart.scales[W].getPixelForValue(K)}k=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(a==="start"?se="top":a==="end"&&(se="bottom"));const ke=this._getLabelSizes();for(m=0,y=l.length;m<y;++m){v=l[m],_=v.label;const W=r.setContext(this.getContext(m));S=this.getPixelForTick(m)+r.labelOffset,C=this._resolveTickFontOptions(m),M=C.lineHeight,A=ne(_)?_.length:1;const K=A/2,Q=W.color,E=W.textStrokeColor,D=W.textStrokeWidth;let O=k;o?(w=S,k==="inner"&&(m===y-1?O=this.options.reverse?"left":"right":m===0?O=this.options.reverse?"right":"left":O="center"),s==="top"?u==="near"||x!==0?I=-A*M+M/2:u==="center"?I=-ke.highest.height/2-K*M+M:I=-ke.highest.height+M/2:u==="near"||x!==0?I=M/2:u==="center"?I=ke.highest.height/2-K*M:I=ke.highest.height-A*M,h&&(I*=-1),x!==0&&!W.showLabelBackdrop&&(w+=M/2*Math.sin(x))):(j=S,I=(1-A)*M/2);let Y;if(W.showLabelBackdrop){const q=Le(W.backdropPadding),xt=ke.heights[m],De=ke.widths[m];let Nt=I-q.top,Te=0-q.left;switch(se){case"middle":Nt-=xt/2;break;case"bottom":Nt-=xt;break}switch(k){case"center":Te-=De/2;break;case"right":Te-=De;break;case"inner":m===y-1?Te-=De:m>0&&(Te-=De/2);break}Y={left:Te,top:Nt,width:De+q.width,height:xt+q.height,color:W.backdropColor}}b.push({label:_,font:C,textOffset:I,options:{rotation:x,color:Q,strokeColor:E,strokeWidth:D,textAlign:O,textBaseline:se,translation:[w,j],backdrop:Y}})}return b}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-dt(this.labelRotation))return e==="top"?"left":"right";let s="center";return n.align==="start"?s="left":n.align==="end"?s="right":n.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:i,mirror:s,padding:r}}=this.options,o=this._getLabelSizes(),l=e+r,a=o.widest.width;let u,d;return n==="left"?s?(d=this.right+r,i==="near"?u="left":i==="center"?(u="center",d+=a/2):(u="right",d+=a)):(d=this.right-l,i==="near"?u="right":i==="center"?(u="center",d-=a/2):(u="left",d=this.left)):n==="right"?s?(d=this.left+r,i==="near"?u="right":i==="center"?(u="center",d-=a/2):(u="left",d-=a)):(d=this.left+l,i==="near"?u="left":i==="center"?(u="center",d+=a/2):(u="right",d=this.right)):u="right",{textAlign:u,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:i,top:s,width:r,height:o}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(i,s,r,o),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const s=this.ticks.findIndex(r=>r.value===e);return s>=0?n.setContext(this.getContext(s)).lineWidth:0}drawGrid(e){const n=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let r,o;const l=(a,u,d)=>{!d.width||!d.color||(i.save(),i.lineWidth=d.width,i.strokeStyle=d.color,i.setLineDash(d.borderDash||[]),i.lineDashOffset=d.borderDashOffset,i.beginPath(),i.moveTo(a.x,a.y),i.lineTo(u.x,u.y),i.stroke(),i.restore())};if(n.display)for(r=0,o=s.length;r<o;++r){const a=s[r];n.drawOnChartArea&&l({x:a.x1,y:a.y1},{x:a.x2,y:a.y2},a),n.drawTicks&&l({x:a.tx1,y:a.ty1},{x:a.tx2,y:a.ty2},{color:a.tickColor,width:a.tickWidth,borderDash:a.tickBorderDash,borderDashOffset:a.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:i,grid:s}}=this,r=i.setContext(this.getContext()),o=i.display?r.width:0;if(!o)return;const l=s.setContext(this.getContext(0)).lineWidth,a=this._borderValue;let u,d,h,f;this.isHorizontal()?(u=bn(e,this.left,o)-o/2,d=bn(e,this.right,l)+l/2,h=f=a):(h=bn(e,this.top,o)-o/2,f=bn(e,this.bottom,l)+l/2,u=d=a),n.save(),n.lineWidth=r.width,n.strokeStyle=r.color,n.beginPath(),n.moveTo(u,h),n.lineTo(d,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const i=this.ctx,s=this._computeLabelArea();s&&Vo(i,s);const r=this.getLabelItems(e);for(const o of r){const l=o.options,a=o.font,u=o.label,d=o.textOffset;Vn(i,u,0,d,a,l)}s&&Ho(i)}drawTitle(){const{ctx:e,options:{position:n,title:i,reverse:s}}=this;if(!i.display)return;const r=ge(i.font),o=Le(i.padding),l=i.align;let a=r.lineHeight/2;n==="bottom"||n==="center"||B(n)?(a+=o.bottom,ne(i.text)&&(a+=r.lineHeight*(i.text.length-1))):a+=o.top;const{titleX:u,titleY:d,maxWidth:h,rotation:f}=z1(this,a,n,l);Vn(e,i.text,0,0,r,{color:i.color,maxWidth:h,rotation:f,textAlign:I1(l,n,s),textBaseline:"middle",translation:[u,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,i=R(e.grid&&e.grid.z,-1),s=R(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==$n.prototype.draw?[{z:n,draw:r=>{this.draw(r)}}]:[{z:i,draw:r=>{this.drawBackground(),this.drawGrid(r),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:n,draw:r=>{this.drawLabels(r)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let r,o;for(r=0,o=n.length;r<o;++r){const l=n[r];l[i]===this.id&&(!e||l.type===e)&&s.push(l)}return s}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return ge(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class xr{constructor(e,n,i){this.type=e,this.scope=n,this.override=i,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let i;V1(n)&&(i=this.register(n));const s=this.items,r=e.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+e);return r in s||(s[r]=e,F1(e,o,i),this.override&&ie.override(e.id,e.overrides)),o}get(e){return this.items[e]}unregister(e){const n=this.items,i=e.id,s=this.scope;i in n&&delete n[i],s&&i in ie[s]&&(delete ie[s][i],this.override&&delete Bn[i])}}function F1(t,e,n){const i=Cs(Object.create(null),[n?ie.get(n):{},ie.get(e),t.defaults]);ie.set(e,i),t.defaultRoutes&&B1(e,t.defaultRoutes),t.descriptors&&ie.describe(e,t.descriptors)}function B1(t,e){Object.keys(e).forEach(n=>{const i=n.split("."),s=i.pop(),r=[t].concat(i).join("."),o=e[n].split("."),l=o.pop(),a=o.join(".");ie.route(r,s,a,l)})}function V1(t){return"id"in t&&"defaults"in t}class H1{constructor(){this.controllers=new xr(pt,"datasets",!0),this.elements=new xr(gt,"elements"),this.plugins=new xr(Object,"plugins"),this.scales=new xr($n,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,i){[...n].forEach(s=>{const r=i||this._getRegistryForType(s);i||r.isForType(s)||r===this.plugins&&s.id?this._exec(e,r,s):$(s,o=>{const l=i||this._getRegistryForType(o);this._exec(e,l,o)})})}_exec(e,n,i){const s=zc(e);G(i["before"+s],[],i),n[e](i),G(i["after"+s],[],i)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const i=this._typedRegistries[n];if(i.isForType(e))return i}return this.plugins}_get(e,n,i){const s=n.get(e);if(s===void 0)throw new Error('"'+e+'" is not a registered '+i+".");return s}}var _t=new H1;class W1{constructor(){this._init=[]}notify(e,n,i,s){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const r=s?this._descriptors(e).filter(s):this._descriptors(e),o=this._notify(r,e,n,i);return n==="afterDestroy"&&(this._notify(r,e,"stop"),this._notify(this._init,e,"uninstall")),o}_notify(e,n,i,s){s=s||{};for(const r of e){const o=r.plugin,l=o[i],a=[n,s,r.options];if(G(l,a,o)===!1&&s.cancelable)return!1}return!0}invalidate(){z(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const i=e&&e.config,s=R(i.options&&i.options.plugins,{}),r=$1(i);return s===!1&&!n?[]:K1(e,r,s,n)}_notifyStateChanges(e){const n=this._oldCache||[],i=this._cache,s=(r,o)=>r.filter(l=>!o.some(a=>l.plugin.id===a.plugin.id));this._notify(s(n,i),e,"stop"),this._notify(s(i,n),e,"start")}}function $1(t){const e={},n=[],i=Object.keys(_t.plugins.items);for(let r=0;r<i.length;r++)n.push(_t.getPlugin(i[r]));const s=t.plugins||[];for(let r=0;r<s.length;r++){const o=s[r];n.indexOf(o)===-1&&(n.push(o),e[o.id]=!0)}return{plugins:n,localIds:e}}function U1(t,e){return!e&&t===!1?null:t===!0?{}:t}function K1(t,{plugins:e,localIds:n},i,s){const r=[],o=t.getContext();for(const l of e){const a=l.id,u=U1(i[a],s);u!==null&&r.push({plugin:l,options:Y1(t.config,{plugin:l,local:n[a]},u,o)})}return r}function Y1(t,{plugin:e,local:n},i,s){const r=t.pluginScopeKeys(e),o=t.getOptionScopes(i,r);return n&&e.defaults&&o.push(e.defaults),t.createResolver(o,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ea(t,e){const n=ie.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function G1(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function X1(t,e){return t===e?"_index_":"_value_"}function Qd(t){if(t==="x"||t==="y"||t==="r")return t}function Q1(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function La(t,...e){if(Qd(t))return t;for(const n of e){const i=n.axis||Q1(n.position)||t.length>1&&Qd(t[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function qd(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function q1(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(i=>i.xAxisID===t||i.yAxisID===t);if(n.length)return qd(t,"x",n[0])||qd(t,"y",n[0])}return{}}function Z1(t,e){const n=Bn[t.type]||{scales:{}},i=e.scales||{},s=Ea(t.type,e),r=Object.create(null);return Object.keys(i).forEach(o=>{const l=i[o];if(!B(l))return console.error(`Invalid scale configuration for scale: ${o}`);if(l._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const a=La(o,l,q1(o,t),ie.scales[l.type]),u=X1(a,s),d=n.scales||{};r[o]=rs(Object.create(null),[{axis:a},l,d[a],d[u]])}),t.data.datasets.forEach(o=>{const l=o.type||t.type,a=o.indexAxis||Ea(l,e),d=(Bn[l]||{}).scales||{};Object.keys(d).forEach(h=>{const f=G1(h,a),p=o[f+"AxisID"]||f;r[p]=r[p]||Object.create(null),rs(r[p],[{axis:f},i[p],d[h]])})}),Object.keys(r).forEach(o=>{const l=r[o];rs(l,[ie.scales[l.type],ie.scale])}),r}function Tm(t){const e=t.options||(t.options={});e.plugins=R(e.plugins,{}),e.scales=Z1(t,e)}function Am(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function J1(t){return t=t||{},t.data=Am(t.data),Tm(t),t}const Zd=new Map,Om=new Set;function yr(t,e){let n=Zd.get(t);return n||(n=e(),Zd.set(t,n),Om.add(n)),n}const Fi=(t,e,n)=>{const i=fn(e,n);i!==void 0&&t.add(i)};class eb{constructor(e){this._config=J1(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=Am(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),Tm(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return yr(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return yr(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return yr(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,i=this.type;return yr(`${i}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const i=this._scopeCache;let s=i.get(e);return(!s||n)&&(s=new Map,i.set(e,s)),s}getOptionScopes(e,n,i){const{options:s,type:r}=this,o=this._cachedScopes(e,i),l=o.get(n);if(l)return l;const a=new Set;n.forEach(d=>{e&&(a.add(e),d.forEach(h=>Fi(a,e,h))),d.forEach(h=>Fi(a,s,h)),d.forEach(h=>Fi(a,Bn[r]||{},h)),d.forEach(h=>Fi(a,ie,h)),d.forEach(h=>Fi(a,Ma,h))});const u=Array.from(a);return u.length===0&&u.push(Object.create(null)),Om.has(n)&&o.set(n,u),u}chartOptionScopes(){const{options:e,type:n}=this;return[e,Bn[n]||{},ie.datasets[n]||{},{type:n},ie,Ma]}resolveNamedOptions(e,n,i,s=[""]){const r={$shared:!0},{resolver:o,subPrefixes:l}=Jd(this._resolverCache,e,s);let a=o;if(nb(o,n)){r.$shared=!1,i=pn(i)?i():i;const u=this.createResolver(e,i,l);a=wi(o,i,u)}for(const u of n)r[u]=a[u];return r}createResolver(e,n,i=[""],s){const{resolver:r}=Jd(this._resolverCache,e,i);return B(n)?wi(r,n,void 0,s):r}}function Jd(t,e,n){let i=t.get(e);i||(i=new Map,t.set(e,i));const s=n.join();let r=i.get(s);return r||(r={resolver:$c(e,n),subPrefixes:n.filter(l=>!l.toLowerCase().includes("hover"))},i.set(s,r)),r}const tb=t=>B(t)&&Object.getOwnPropertyNames(t).some(e=>pn(t[e]));function nb(t,e){const{isScriptable:n,isIndexable:i}=fm(t);for(const s of e){const r=n(s),o=i(s),l=(o||r)&&t[s];if(r&&(pn(l)||tb(l))||o&&ne(l))return!0}return!1}var ib="4.5.0";const sb=["top","bottom","left","right","chartArea"];function eh(t,e){return t==="top"||t==="bottom"||sb.indexOf(t)===-1&&e==="x"}function th(t,e){return function(n,i){return n[t]===i[t]?n[e]-i[e]:n[t]-i[t]}}function nh(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),G(n&&n.onComplete,[t],e)}function rb(t){const e=t.chart,n=e.options.animation;G(n&&n.onProgress,[t],e)}function Rm(t){return Yc()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const Br={},ih=t=>{const e=Rm(t);return Object.values(Br).filter(n=>n.canvas===e).pop()};function ob(t,e,n){const i=Object.keys(t);for(const s of i){const r=+s;if(r>=e){const o=t[s];delete t[s],(n>0||r>e)&&(t[r+n]=o)}}}function lb(t,e,n,i){return!n||t.type==="mouseout"?null:i?e:t}class Ye{static register(...e){_t.add(...e),sh()}static unregister(...e){_t.remove(...e),sh()}constructor(e,n){const i=this.config=new eb(n),s=Rm(e),r=ih(s);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||j1(s)),this.platform.updateConfig(i);const l=this.platform.acquireContext(s,o.aspectRatio),a=l&&l.canvas,u=a&&a.height,d=a&&a.width;if(this.id=ay(),this.ctx=l,this.canvas=a,this.width=d,this.height=u,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new W1,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=jy(h=>this.update(h),o.resizeDelay||0),this._dataChanges=[],Br[this.id]=this,!l||!a){console.error("Failed to create chart: can't acquire context from the given item");return}Ct.listen(this,"complete",nh),Ct.listen(this,"progress",rb),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:i,height:s,_aspectRatio:r}=this;return z(e)?n&&r?r:s?i/s:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return _t}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Nd(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return kd(this.canvas,this.ctx),this}stop(){return Ct.stop(this),this}resize(e,n){Ct.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(s,e,n,r),l=i.devicePixelRatio||this.platform.getDevicePixelRatio(),a=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,Nd(this,l,!0)&&(this.notifyPlugins("resize",{size:o}),G(i.onResize,[this,o],this),this.attached&&this._doResize(a)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};$(n,(i,s)=>{i.id=s})}buildOrUpdateScales(){const e=this.options,n=e.scales,i=this.scales,s=Object.keys(i).reduce((o,l)=>(o[l]=!1,o),{});let r=[];n&&(r=r.concat(Object.keys(n).map(o=>{const l=n[o],a=La(o,l),u=a==="r",d=a==="x";return{options:l,dposition:u?"chartArea":d?"bottom":"left",dtype:u?"radialLinear":d?"category":"linear"}}))),$(r,o=>{const l=o.options,a=l.id,u=La(a,l),d=R(l.type,o.dtype);(l.position===void 0||eh(l.position,u)!==eh(o.dposition))&&(l.position=o.dposition),s[a]=!0;let h=null;if(a in i&&i[a].type===d)h=i[a];else{const f=_t.getScale(d);h=new f({id:a,type:d,ctx:this.ctx,chart:this}),i[h.id]=h}h.init(l,e)}),$(s,(o,l)=>{o||delete i[l]}),$(i,o=>{Pe.configure(this,o,o.options),Pe.addBox(this,o)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,i=e.length;if(e.sort((s,r)=>s.index-r.index),i>n){for(let s=n;s<i;++s)this._destroyDatasetMeta(s);e.splice(n,i-n)}this._sortedMetasets=e.slice(0).sort(th("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((i,s)=>{n.filter(r=>r===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=n.length;i<s;i++){const r=n[i];let o=this.getDatasetMeta(i);const l=r.type||this.config.type;if(o.type&&o.type!==l&&(this._destroyDatasetMeta(i),o=this.getDatasetMeta(i)),o.type=l,o.indexAxis=r.indexAxis||Ea(l,this.options),o.order=r.order||0,o.index=i,o.label=""+r.label,o.visible=this.isDatasetVisible(i),o.controller)o.controller.updateIndex(i),o.controller.linkScales();else{const a=_t.getController(l),{datasetElementType:u,dataElementType:d}=ie.datasets[l];Object.assign(a,{dataElementType:_t.getElement(d),datasetElementType:u&&_t.getElement(u)}),o.controller=new a(this,i),e.push(o.controller)}}return this._updateMetasets(),e}_resetElements(){$(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const i=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let u=0,d=this.data.datasets.length;u<d;u++){const{controller:h}=this.getDatasetMeta(u),f=!s&&r.indexOf(h)===-1;h.buildOrUpdateElements(f),o=Math.max(+h.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),s||$(r,u=>{u.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(th("z","_idx"));const{_active:l,_lastEvent:a}=this;a?this._eventHandler(a,!0):l.length&&this._updateHoverStyles(l,l,!0),this.render()}_updateScales(){$(this.scales,e=>{Pe.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),i=new Set(e.events);(!pd(n,i)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:r}of n){const o=i==="_removeElements"?-r:r;ob(e,s,o)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,i=r=>new Set(e.filter(o=>o[0]===r).map((o,l)=>l+","+o.splice(1).join(","))),s=i(0);for(let r=1;r<n;r++)if(!pd(s,i(r)))return;return Array.from(s).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Pe.update(this,this.width,this.height,e);const n=this.chartArea,i=n.width<=0||n.height<=0;this._layers=[],$(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,r)=>{s._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,i=this.data.datasets.length;n<i;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,i=this.data.datasets.length;n<i;++n)this._updateDataset(n,pn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const i=this.getDatasetMeta(e),s={meta:i,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(n),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Ct.has(this)?this.attached&&!Ct.running(this)&&Ct.start(this):(this.draw(),nh({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:i,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,s)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,i=[];let s,r;for(s=0,r=n.length;s<r;++s){const o=n[s];(!e||o.visible)&&i.push(o)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,i={meta:e,index:e.index,cancelable:!0},s=Sm(this,e);this.notifyPlugins("beforeDatasetDraw",i)!==!1&&(s&&Vo(n,s),e.controller.draw(),s&&Ho(n),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(e){return It(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,i,s){const r=s1.modes[n];return typeof r=="function"?r(this,e,i,s):[]}getDatasetMeta(e){const n=this.data.datasets[e],i=this._metasets;let s=i.filter(r=>r&&r._dataset===n).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=yn(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const i=this.getDatasetMeta(e);return typeof i.hidden=="boolean"?!i.hidden:!n.hidden}setDatasetVisibility(e,n){const i=this.getDatasetMeta(e);i.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,i){const s=i?"show":"hide",r=this.getDatasetMeta(e),o=r.controller._resolveAnimations(void 0,s);Ps(n)?(r.data[n].hidden=!i,this.update()):(this.setDatasetVisibility(e,i),o.update(r,{visible:i}),this.update(l=>l.datasetIndex===e?s:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Ct.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),kd(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete Br[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,i=(r,o)=>{n.addEventListener(this,r,o),e[r]=o},s=(r,o,l)=>{r.offsetX=o,r.offsetY=l,this._eventHandler(r)};$(this.options.events,r=>i(r,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,i=(a,u)=>{n.addEventListener(this,a,u),e[a]=u},s=(a,u)=>{e[a]&&(n.removeEventListener(this,a,u),delete e[a])},r=(a,u)=>{this.canvas&&this.resize(a,u)};let o;const l=()=>{s("attach",l),this.attached=!0,this.resize(),i("resize",r),i("detach",o)};o=()=>{this.attached=!1,s("resize",r),this._stop(),this._resize(0,0),i("attach",l)},n.isAttached(this.canvas)?l():o()}unbindEvents(){$(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},$(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,i){const s=i?"set":"remove";let r,o,l,a;for(n==="dataset"&&(r=this.getDatasetMeta(e[0].datasetIndex),r.controller["_"+s+"DatasetHoverStyle"]()),l=0,a=e.length;l<a;++l){o=e[l];const u=o&&this.getDatasetMeta(o.datasetIndex).controller;u&&u[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],i=e.map(({datasetIndex:r,index:o})=>{const l=this.getDatasetMeta(r);if(!l)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:l.data[o],index:o}});!go(i,n)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,n))}notifyPlugins(e,n,i){return this._plugins.notify(this,e,n,i)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,i){const s=this.options.hover,r=(a,u)=>a.filter(d=>!u.some(h=>d.datasetIndex===h.datasetIndex&&d.index===h.index)),o=r(n,e),l=i?e:r(e,n);o.length&&this.updateHoverStyle(o,s.mode,!1),l.length&&s.mode&&this.updateHoverStyle(l,s.mode,!0)}_eventHandler(e,n){const i={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},s=o=>(o.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;const r=this._handleEvent(e,n,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(e,n,i){const{_active:s=[],options:r}=this,o=n,l=this._getActiveElements(e,s,i,o),a=py(e),u=lb(e,this._lastEvent,i,a);i&&(this._lastEvent=null,G(r.onHover,[e,l,this],this),a&&G(r.onClick,[e,l,this],this));const d=!go(l,s);return(d||n)&&(this._active=l,this._updateHoverStyles(l,s,n)),this._lastEvent=u,d}_getActiveElements(e,n,i,s){if(e.type==="mouseout")return[];if(!i)return n;const r=this.options.hover;return this.getElementsAtEventForMode(e,r.mode,r,s)}}P(Ye,"defaults",ie),P(Ye,"instances",Br),P(Ye,"overrides",Bn),P(Ye,"registry",_t),P(Ye,"version",ib),P(Ye,"getChart",ih);function sh(){return $(Ye.instances,t=>t._plugins.invalidate())}function ab(t,e,n){const{startAngle:i,x:s,y:r,outerRadius:o,innerRadius:l,options:a}=e,{borderWidth:u,borderJoinStyle:d}=a,h=Math.min(u/o,Me(i-n));if(t.beginPath(),t.arc(s,r,o-u/2,i+h/2,n-h/2),l>0){const f=Math.min(u/l,Me(i-n));t.arc(s,r,l+u/2,n-f/2,i+f/2,!0)}else{const f=Math.min(u/2,o*Me(i-n));if(d==="round")t.arc(s,r,f,n-H/2,i+H/2,!0);else if(d==="bevel"){const p=2*f*f,g=-p*Math.cos(n+H/2)+s,x=-p*Math.sin(n+H/2)+r,b=p*Math.cos(i+H/2)+s,m=p*Math.sin(i+H/2)+r;t.lineTo(g,x),t.lineTo(b,m)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}function cb(t,e,n){const{startAngle:i,pixelMargin:s,x:r,y:o,outerRadius:l,innerRadius:a}=e;let u=s/l;t.beginPath(),t.arc(r,o,l,i-u,n+u),a>s?(u=s/a,t.arc(r,o,a,n+u,i-u,!0)):t.arc(r,o,s,n+he,i-he),t.closePath(),t.clip()}function ub(t){return Wc(t,["outerStart","outerEnd","innerStart","innerEnd"])}function db(t,e,n,i){const s=ub(t.options.borderRadius),r=(n-e)/2,o=Math.min(r,i*e/2),l=a=>{const u=(n-Math.min(r,a))*i/2;return ye(a,0,Math.min(r,u))};return{outerStart:l(s.outerStart),outerEnd:l(s.outerEnd),innerStart:ye(s.innerStart,0,o),innerEnd:ye(s.innerEnd,0,o)}}function Yn(t,e,n,i){return{x:n+t*Math.cos(e),y:i+t*Math.sin(e)}}function wo(t,e,n,i,s,r){const{x:o,y:l,startAngle:a,pixelMargin:u,innerRadius:d}=e,h=Math.max(e.outerRadius+i+n-u,0),f=d>0?d+i+n+u:0;let p=0;const g=s-a;if(i){const W=d>0?d-i:0,K=h>0?h-i:0,Q=(W+K)/2,E=Q!==0?g*Q/(Q+i):g;p=(g-E)/2}const x=Math.max(.001,g*h-n/H)/h,b=(g-x)/2,m=a+b+p,y=s-b-p,{outerStart:v,outerEnd:_,innerStart:w,innerEnd:j}=db(e,f,h,y-m),k=h-v,S=h-_,C=m+v/k,M=y-_/S,A=f+w,I=f+j,se=m+w/A,ke=y-j/I;if(t.beginPath(),r){const W=(C+M)/2;if(t.arc(o,l,h,C,W),t.arc(o,l,h,W,M),_>0){const D=Yn(S,M,o,l);t.arc(D.x,D.y,_,M,y+he)}const K=Yn(I,y,o,l);if(t.lineTo(K.x,K.y),j>0){const D=Yn(I,ke,o,l);t.arc(D.x,D.y,j,y+he,ke+Math.PI)}const Q=(y-j/f+(m+w/f))/2;if(t.arc(o,l,f,y-j/f,Q,!0),t.arc(o,l,f,Q,m+w/f,!0),w>0){const D=Yn(A,se,o,l);t.arc(D.x,D.y,w,se+Math.PI,m-he)}const E=Yn(k,m,o,l);if(t.lineTo(E.x,E.y),v>0){const D=Yn(k,C,o,l);t.arc(D.x,D.y,v,m-he,C)}}else{t.moveTo(o,l);const W=Math.cos(C)*h+o,K=Math.sin(C)*h+l;t.lineTo(W,K);const Q=Math.cos(M)*h+o,E=Math.sin(M)*h+l;t.lineTo(Q,E)}t.closePath()}function hb(t,e,n,i,s){const{fullCircles:r,startAngle:o,circumference:l}=e;let a=e.endAngle;if(r){wo(t,e,n,i,a,s);for(let u=0;u<r;++u)t.fill();isNaN(l)||(a=o+(l%ee||ee))}return wo(t,e,n,i,a,s),t.fill(),a}function fb(t,e,n,i,s){const{fullCircles:r,startAngle:o,circumference:l,options:a}=e,{borderWidth:u,borderJoinStyle:d,borderDash:h,borderDashOffset:f,borderRadius:p}=a,g=a.borderAlign==="inner";if(!u)return;t.setLineDash(h||[]),t.lineDashOffset=f,g?(t.lineWidth=u*2,t.lineJoin=d||"round"):(t.lineWidth=u,t.lineJoin=d||"bevel");let x=e.endAngle;if(r){wo(t,e,n,i,x,s);for(let b=0;b<r;++b)t.stroke();isNaN(l)||(x=o+(l%ee||ee))}g&&cb(t,e,x),a.selfJoin&&x-o>=H&&p===0&&d!=="miter"&&ab(t,e,x),r||(wo(t,e,n,i,x,s),t.stroke())}class Yi extends gt{constructor(n){super();P(this,"circumference");P(this,"endAngle");P(this,"fullCircles");P(this,"innerRadius");P(this,"outerRadius");P(this,"pixelMargin");P(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,i,s){const r=this.getProps(["x","y"],s),{angle:o,distance:l}=im(r,{x:n,y:i}),{startAngle:a,endAngle:u,innerRadius:d,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),p=(this.options.spacing+this.options.borderWidth)/2,g=R(f,u-a),x=Es(o,a,u)&&a!==u,b=g>=ee||x,m=Ot(l,d+p,h+p);return b&&m}getCenterPoint(n){const{x:i,y:s,startAngle:r,endAngle:o,innerRadius:l,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:u,spacing:d}=this.options,h=(r+o)/2,f=(l+a+d+u)/2;return{x:i+Math.cos(h)*f,y:s+Math.sin(h)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:i,circumference:s}=this,r=(i.offset||0)/4,o=(i.spacing||0)/2,l=i.circular;if(this.pixelMargin=i.borderAlign==="inner"?.33:0,this.fullCircles=s>ee?Math.floor(s/ee):0,s===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const a=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(a)*r,Math.sin(a)*r);const u=1-Math.sin(Math.min(H,s||0)),d=r*u;n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,hb(n,this,d,o,l),fb(n,this,d,o,l),n.restore()}}P(Yi,"id","arc"),P(Yi,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1}),P(Yi,"defaultRoutes",{backgroundColor:"backgroundColor"}),P(Yi,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function Im(t,e,n=e){t.lineCap=R(n.borderCapStyle,e.borderCapStyle),t.setLineDash(R(n.borderDash,e.borderDash)),t.lineDashOffset=R(n.borderDashOffset,e.borderDashOffset),t.lineJoin=R(n.borderJoinStyle,e.borderJoinStyle),t.lineWidth=R(n.borderWidth,e.borderWidth),t.strokeStyle=R(n.borderColor,e.borderColor)}function pb(t,e,n){t.lineTo(n.x,n.y)}function mb(t){return t.stepped?Iy:t.tension||t.cubicInterpolationMode==="monotone"?zy:pb}function zm(t,e,n={}){const i=t.length,{start:s=0,end:r=i-1}=n,{start:o,end:l}=e,a=Math.max(s,o),u=Math.min(r,l),d=s<o&&r<o||s>l&&r>l;return{count:i,start:a,loop:e.loop,ilen:u<a&&!d?i+u-a:u-a}}function gb(t,e,n,i){const{points:s,options:r}=e,{count:o,start:l,loop:a,ilen:u}=zm(s,n,i),d=mb(r);let{move:h=!0,reverse:f}=i||{},p,g,x;for(p=0;p<=u;++p)g=s[(l+(f?u-p:p))%o],!g.skip&&(h?(t.moveTo(g.x,g.y),h=!1):d(t,x,g,f,r.stepped),x=g);return a&&(g=s[(l+(f?u:0))%o],d(t,x,g,f,r.stepped)),!!a}function xb(t,e,n,i){const s=e.points,{count:r,start:o,ilen:l}=zm(s,n,i),{move:a=!0,reverse:u}=i||{};let d=0,h=0,f,p,g,x,b,m;const y=_=>(o+(u?l-_:_))%r,v=()=>{x!==b&&(t.lineTo(d,b),t.lineTo(d,x),t.lineTo(d,m))};for(a&&(p=s[y(0)],t.moveTo(p.x,p.y)),f=0;f<=l;++f){if(p=s[y(f)],p.skip)continue;const _=p.x,w=p.y,j=_|0;j===g?(w<x?x=w:w>b&&(b=w),d=(h*d+_)/++h):(v(),t.lineTo(_,w),g=j,h=0,x=b=w),m=w}v()}function Da(t){const e=t.options,n=e.borderDash&&e.borderDash.length;return!t._decimated&&!t._loop&&!e.tension&&e.cubicInterpolationMode!=="monotone"&&!e.stepped&&!n?xb:gb}function yb(t){return t.stepped?gv:t.tension||t.cubicInterpolationMode==="monotone"?xv:Nn}function vb(t,e,n,i){let s=e._path;s||(s=e._path=new Path2D,e.path(s,n,i)&&s.closePath()),Im(t,e.options),t.stroke(s)}function bb(t,e,n,i){const{segments:s,options:r}=e,o=Da(e);for(const l of s)Im(t,r,l.style),t.beginPath(),o(t,e,l,{start:n,end:n+i-1})&&t.closePath(),t.stroke()}const _b=typeof Path2D=="function";function wb(t,e,n,i){_b&&!e.options.segment?vb(t,e,n,i):bb(t,e,n,i)}class Jt extends gt{constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,n){const i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;av(this._points,i,e,s,n),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=kv(this,this.options.segment))}first(){const e=this.segments,n=this.points;return e.length&&n[e[0].start]}last(){const e=this.segments,n=this.points,i=e.length;return i&&n[e[i-1].end]}interpolate(e,n){const i=this.options,s=e[n],r=this.points,o=km(this,{property:n,start:s,end:s});if(!o.length)return;const l=[],a=yb(i);let u,d;for(u=0,d=o.length;u<d;++u){const{start:h,end:f}=o[u],p=r[h],g=r[f];if(p===g){l.push(p);continue}const x=Math.abs((s-p[n])/(g[n]-p[n])),b=a(p,g,x,i.stepped);b[n]=e[n],l.push(b)}return l.length===1?l[0]:l}pathSegment(e,n,i){return Da(this)(e,this,n,i)}path(e,n,i){const s=this.segments,r=Da(this);let o=this._loop;n=n||0,i=i||this.points.length-n;for(const l of s)o&=r(e,this,l,{start:n,end:n+i-1});return!!o}draw(e,n,i,s){const r=this.options||{};(this.points||[]).length&&r.borderWidth&&(e.save(),wb(e,this,i,s),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}P(Jt,"id","line"),P(Jt,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),P(Jt,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),P(Jt,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"&&e!=="fill"});function rh(t,e,n,i){const s=t.options,{[n]:r}=t.getProps([n],i);return Math.abs(e-r)<s.radius+s.hitRadius}class Vr extends gt{constructor(n){super();P(this,"parsed");P(this,"skip");P(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,n&&Object.assign(this,n)}inRange(n,i,s){const r=this.options,{x:o,y:l}=this.getProps(["x","y"],s);return Math.pow(n-o,2)+Math.pow(i-l,2)<Math.pow(r.hitRadius+r.radius,2)}inXRange(n,i){return rh(this,n,"x",i)}inYRange(n,i){return rh(this,n,"y",i)}getCenterPoint(n){const{x:i,y:s}=this.getProps(["x","y"],n);return{x:i,y:s}}size(n){n=n||this.options||{};let i=n.radius||0;i=Math.max(i,i&&n.hoverRadius||0);const s=i&&n.borderWidth||0;return(i+s)*2}draw(n,i){const s=this.options;this.skip||s.radius<.1||!It(this,i,this.size(s)/2)||(n.strokeStyle=s.borderColor,n.lineWidth=s.borderWidth,n.fillStyle=s.backgroundColor,Ca(n,s,this.x,this.y))}getRange(){const n=this.options||{};return n.radius+n.hitRadius}}P(Vr,"id","point"),P(Vr,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),P(Vr,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Fm(t,e){const{x:n,y:i,base:s,width:r,height:o}=t.getProps(["x","y","base","width","height"],e);let l,a,u,d,h;return t.horizontal?(h=o/2,l=Math.min(n,s),a=Math.max(n,s),u=i-h,d=i+h):(h=r/2,l=n-h,a=n+h,u=Math.min(i,s),d=Math.max(i,s)),{left:l,top:u,right:a,bottom:d}}function en(t,e,n,i){return t?0:ye(e,n,i)}function kb(t,e,n){const i=t.options.borderWidth,s=t.borderSkipped,r=hm(i);return{t:en(s.top,r.top,0,n),r:en(s.right,r.right,0,e),b:en(s.bottom,r.bottom,0,n),l:en(s.left,r.left,0,e)}}function Sb(t,e,n){const{enableBorderRadius:i}=t.getProps(["enableBorderRadius"]),s=t.options.borderRadius,r=Tn(s),o=Math.min(e,n),l=t.borderSkipped,a=i||B(s);return{topLeft:en(!a||l.top||l.left,r.topLeft,0,o),topRight:en(!a||l.top||l.right,r.topRight,0,o),bottomLeft:en(!a||l.bottom||l.left,r.bottomLeft,0,o),bottomRight:en(!a||l.bottom||l.right,r.bottomRight,0,o)}}function jb(t){const e=Fm(t),n=e.right-e.left,i=e.bottom-e.top,s=kb(t,n/2,i/2),r=Sb(t,n/2,i/2);return{outer:{x:e.left,y:e.top,w:n,h:i,radius:r},inner:{x:e.left+s.l,y:e.top+s.t,w:n-s.l-s.r,h:i-s.t-s.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,r.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(s.b,s.r))}}}}function Cl(t,e,n,i){const s=e===null,r=n===null,l=t&&!(s&&r)&&Fm(t,i);return l&&(s||Ot(e,l.left,l.right))&&(r||Ot(n,l.top,l.bottom))}function Nb(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function Mb(t,e){t.rect(e.x,e.y,e.w,e.h)}function Pl(t,e,n={}){const i=t.x!==n.x?-e:0,s=t.y!==n.y?-e:0,r=(t.x+t.w!==n.x+n.w?e:0)-i,o=(t.y+t.h!==n.y+n.h?e:0)-s;return{x:t.x+i,y:t.y+s,w:t.w+r,h:t.h+o,radius:t.radius}}class Hr extends gt{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:i,backgroundColor:s}}=this,{inner:r,outer:o}=jb(this),l=Nb(o.radius)?Ls:Mb;e.save(),(o.w!==r.w||o.h!==r.h)&&(e.beginPath(),l(e,Pl(o,n,r)),e.clip(),l(e,Pl(r,-n,o)),e.fillStyle=i,e.fill("evenodd")),e.beginPath(),l(e,Pl(r,n)),e.fillStyle=s,e.fill(),e.restore()}inRange(e,n,i){return Cl(this,e,n,i)}inXRange(e,n){return Cl(this,e,null,n)}inYRange(e,n){return Cl(this,null,e,n)}getCenterPoint(e){const{x:n,y:i,base:s,horizontal:r}=this.getProps(["x","y","base","horizontal"],e);return{x:r?(n+s)/2:n,y:r?i:(i+s)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}P(Hr,"id","bar"),P(Hr,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),P(Hr,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var Cb=Object.freeze({__proto__:null,ArcElement:Yi,BarElement:Hr,LineElement:Jt,PointElement:Vr});const Ta=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],oh=Ta.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function Bm(t){return Ta[t%Ta.length]}function Vm(t){return oh[t%oh.length]}function Pb(t,e){return t.borderColor=Bm(e),t.backgroundColor=Vm(e),++e}function Eb(t,e){return t.backgroundColor=t.data.map(()=>Bm(e++)),e}function Lb(t,e){return t.backgroundColor=t.data.map(()=>Vm(e++)),e}function Db(t){let e=0;return(n,i)=>{const s=t.getDatasetMeta(i).controller;s instanceof En?e=Eb(n,e):s instanceof cs?e=Lb(n,e):s&&(e=Pb(n,e))}}function lh(t){let e;for(e in t)if(t[e].borderColor||t[e].backgroundColor)return!0;return!1}function Tb(t){return t&&(t.borderColor||t.backgroundColor)}function Ab(){return ie.borderColor!=="rgba(0,0,0,0.1)"||ie.backgroundColor!=="rgba(0,0,0,0.1)"}var Ob={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(t,e,n){if(!n.enabled)return;const{data:{datasets:i},options:s}=t.config,{elements:r}=s,o=lh(i)||Tb(s)||r&&lh(r)||Ab();if(!n.forceOverride&&o)return;const l=Db(t);i.forEach(l)}};function Rb(t,e,n,i,s){const r=s.samples||i;if(r>=n)return t.slice(e,e+n);const o=[],l=(n-2)/(r-2);let a=0;const u=e+n-1;let d=e,h,f,p,g,x;for(o[a++]=t[d],h=0;h<r-2;h++){let b=0,m=0,y;const v=Math.floor((h+1)*l)+1+e,_=Math.min(Math.floor((h+2)*l)+1,n)+e,w=_-v;for(y=v;y<_;y++)b+=t[y].x,m+=t[y].y;b/=w,m/=w;const j=Math.floor(h*l)+1+e,k=Math.min(Math.floor((h+1)*l)+1,n)+e,{x:S,y:C}=t[d];for(p=g=-1,y=j;y<k;y++)g=.5*Math.abs((S-b)*(t[y].y-C)-(S-t[y].x)*(m-C)),g>p&&(p=g,f=t[y],x=y);o[a++]=f,d=x}return o[a++]=t[u],o}function Ib(t,e,n,i){let s=0,r=0,o,l,a,u,d,h,f,p,g,x;const b=[],m=e+n-1,y=t[e].x,_=t[m].x-y;for(o=e;o<e+n;++o){l=t[o],a=(l.x-y)/_*i,u=l.y;const w=a|0;if(w===d)u<g?(g=u,h=o):u>x&&(x=u,f=o),s=(r*s+l.x)/++r;else{const j=o-1;if(!z(h)&&!z(f)){const k=Math.min(h,f),S=Math.max(h,f);k!==p&&k!==j&&b.push({...t[k],x:s}),S!==p&&S!==j&&b.push({...t[S],x:s})}o>0&&j!==p&&b.push(t[j]),b.push(l),d=w,r=0,g=x=u,h=f=p=o}}return b}function Hm(t){if(t._decimated){const e=t._data;delete t._decimated,delete t._data,Object.defineProperty(t,"data",{configurable:!0,enumerable:!0,writable:!0,value:e})}}function ah(t){t.data.datasets.forEach(e=>{Hm(e)})}function zb(t,e){const n=e.length;let i=0,s;const{iScale:r}=t,{min:o,max:l,minDefined:a,maxDefined:u}=r.getUserBounds();return a&&(i=ye(Rt(e,r.axis,o).lo,0,n-1)),u?s=ye(Rt(e,r.axis,l).hi+1,i,n)-i:s=n-i,{start:i,count:s}}var Fb={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(t,e,n)=>{if(!n.enabled){ah(t);return}const i=t.width;t.data.datasets.forEach((s,r)=>{const{_data:o,indexAxis:l}=s,a=t.getDatasetMeta(r),u=o||s.data;if(Ui([l,t.options.indexAxis])==="y"||!a.controller.supportsDecimation)return;const d=t.scales[a.xAxisID];if(d.type!=="linear"&&d.type!=="time"||t.options.parsing)return;let{start:h,count:f}=zb(a,u);const p=n.threshold||4*i;if(f<=p){Hm(s);return}z(o)&&(s._data=u,delete s.data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(x){this._data=x}}));let g;switch(n.algorithm){case"lttb":g=Rb(u,h,f,i,n);break;case"min-max":g=Ib(u,h,f,i);break;default:throw new Error(`Unsupported decimation algorithm '${n.algorithm}'`)}s._decimated=g})},destroy(t){ah(t)}};function Bb(t,e,n){const i=t.segments,s=t.points,r=e.points,o=[];for(const l of i){let{start:a,end:u}=l;u=$o(a,u,s);const d=Aa(n,s[a],s[u],l.loop);if(!e.segments){o.push({source:l,target:d,start:s[a],end:s[u]});continue}const h=km(e,d);for(const f of h){const p=Aa(n,r[f.start],r[f.end],f.loop),g=wm(l,s,p);for(const x of g)o.push({source:x,target:f,start:{[n]:ch(d,p,"start",Math.max)},end:{[n]:ch(d,p,"end",Math.min)}})}}return o}function Aa(t,e,n,i){if(i)return;let s=e[t],r=n[t];return t==="angle"&&(s=Me(s),r=Me(r)),{property:t,start:s,end:r}}function Vb(t,e){const{x:n=null,y:i=null}=t||{},s=e.points,r=[];return e.segments.forEach(({start:o,end:l})=>{l=$o(o,l,s);const a=s[o],u=s[l];i!==null?(r.push({x:a.x,y:i}),r.push({x:u.x,y:i})):n!==null&&(r.push({x:n,y:a.y}),r.push({x:n,y:u.y}))}),r}function $o(t,e,n){for(;e>t;e--){const i=n[e];if(!isNaN(i.x)&&!isNaN(i.y))break}return e}function ch(t,e,n,i){return t&&e?i(t[n],e[n]):t?t[n]:e?e[n]:0}function Wm(t,e){let n=[],i=!1;return ne(t)?(i=!0,n=t):n=Vb(t,e),n.length?new Jt({points:n,options:{tension:0},_loop:i,_fullLoop:i}):null}function uh(t){return t&&t.fill!==!1}function Hb(t,e,n){let s=t[e].fill;const r=[e];let o;if(!n)return s;for(;s!==!1&&r.indexOf(s)===-1;){if(!ue(s))return s;if(o=t[s],!o)return!1;if(o.visible)return s;r.push(s),s=o.fill}return!1}function Wb(t,e,n){const i=Yb(t);if(B(i))return isNaN(i.value)?!1:i;let s=parseFloat(i);return ue(s)&&Math.floor(s)===s?$b(i[0],e,s,n):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function $b(t,e,n,i){return(t==="-"||t==="+")&&(n=e+n),n===e||n<0||n>=i?!1:n}function Ub(t,e){let n=null;return t==="start"?n=e.bottom:t==="end"?n=e.top:B(t)?n=e.getPixelForValue(t.value):e.getBasePixel&&(n=e.getBasePixel()),n}function Kb(t,e,n){let i;return t==="start"?i=n:t==="end"?i=e.options.reverse?e.min:e.max:B(t)?i=t.value:i=e.getBaseValue(),i}function Yb(t){const e=t.options,n=e.fill;let i=R(n&&n.target,n);return i===void 0&&(i=!!e.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function Gb(t){const{scale:e,index:n,line:i}=t,s=[],r=i.segments,o=i.points,l=Xb(e,n);l.push(Wm({x:null,y:e.bottom},i));for(let a=0;a<r.length;a++){const u=r[a];for(let d=u.start;d<=u.end;d++)Qb(s,o[d],l)}return new Jt({points:s,options:{}})}function Xb(t,e){const n=[],i=t.getMatchingVisibleMetas("line");for(let s=0;s<i.length;s++){const r=i[s];if(r.index===e)break;r.hidden||n.unshift(r.dataset)}return n}function Qb(t,e,n){const i=[];for(let s=0;s<n.length;s++){const r=n[s],{first:o,last:l,point:a}=qb(r,e,"x");if(!(!a||o&&l)){if(o)i.unshift(a);else if(t.push(a),!l)break}}t.push(...i)}function qb(t,e,n){const i=t.interpolate(e,n);if(!i)return{};const s=i[n],r=t.segments,o=t.points;let l=!1,a=!1;for(let u=0;u<r.length;u++){const d=r[u],h=o[d.start][n],f=o[d.end][n];if(Ot(s,h,f)){l=s===h,a=s===f;break}}return{first:l,last:a,point:i}}class $m{constructor(e){this.x=e.x,this.y=e.y,this.radius=e.radius}pathSegment(e,n,i){const{x:s,y:r,radius:o}=this;return n=n||{start:0,end:ee},e.arc(s,r,o,n.end,n.start,!0),!i.bounds}interpolate(e){const{x:n,y:i,radius:s}=this,r=e.angle;return{x:n+Math.cos(r)*s,y:i+Math.sin(r)*s,angle:r}}}function Zb(t){const{chart:e,fill:n,line:i}=t;if(ue(n))return Jb(e,n);if(n==="stack")return Gb(t);if(n==="shape")return!0;const s=e_(t);return s instanceof $m?s:Wm(s,i)}function Jb(t,e){const n=t.getDatasetMeta(e);return n&&t.isDatasetVisible(e)?n.dataset:null}function e_(t){return(t.scale||{}).getPointPositionForValue?n_(t):t_(t)}function t_(t){const{scale:e={},fill:n}=t,i=Ub(n,e);if(ue(i)){const s=e.isHorizontal();return{x:s?i:null,y:s?null:i}}return null}function n_(t){const{scale:e,fill:n}=t,i=e.options,s=e.getLabels().length,r=i.reverse?e.max:e.min,o=Kb(n,e,r),l=[];if(i.grid.circular){const a=e.getPointPositionForValue(0,r);return new $m({x:a.x,y:a.y,radius:e.getDistanceFromCenterForValue(o)})}for(let a=0;a<s;++a)l.push(e.getPointPositionForValue(a,o));return l}function El(t,e,n){const i=Zb(e),{chart:s,index:r,line:o,scale:l,axis:a}=e,u=o.options,d=u.fill,h=u.backgroundColor,{above:f=h,below:p=h}=d||{},g=s.getDatasetMeta(r),x=Sm(s,g);i&&o.points.length&&(Vo(t,n),i_(t,{line:o,target:i,above:f,below:p,area:n,scale:l,axis:a,clip:x}),Ho(t))}function i_(t,e){const{line:n,target:i,above:s,below:r,area:o,scale:l,clip:a}=e,u=n._loop?"angle":e.axis;t.save();let d=r;r!==s&&(u==="x"?(dh(t,i,o.top),Ll(t,{line:n,target:i,color:s,scale:l,property:u,clip:a}),t.restore(),t.save(),dh(t,i,o.bottom)):u==="y"&&(hh(t,i,o.left),Ll(t,{line:n,target:i,color:r,scale:l,property:u,clip:a}),t.restore(),t.save(),hh(t,i,o.right),d=s)),Ll(t,{line:n,target:i,color:d,scale:l,property:u,clip:a}),t.restore()}function dh(t,e,n){const{segments:i,points:s}=e;let r=!0,o=!1;t.beginPath();for(const l of i){const{start:a,end:u}=l,d=s[a],h=s[$o(a,u,s)];r?(t.moveTo(d.x,d.y),r=!1):(t.lineTo(d.x,n),t.lineTo(d.x,d.y)),o=!!e.pathSegment(t,l,{move:o}),o?t.closePath():t.lineTo(h.x,n)}t.lineTo(e.first().x,n),t.closePath(),t.clip()}function hh(t,e,n){const{segments:i,points:s}=e;let r=!0,o=!1;t.beginPath();for(const l of i){const{start:a,end:u}=l,d=s[a],h=s[$o(a,u,s)];r?(t.moveTo(d.x,d.y),r=!1):(t.lineTo(n,d.y),t.lineTo(d.x,d.y)),o=!!e.pathSegment(t,l,{move:o}),o?t.closePath():t.lineTo(n,h.y)}t.lineTo(n,e.first().y),t.closePath(),t.clip()}function Ll(t,e){const{line:n,target:i,property:s,color:r,scale:o,clip:l}=e,a=Bb(n,i,s);for(const{source:u,target:d,start:h,end:f}of a){const{style:{backgroundColor:p=r}={}}=u,g=i!==!0;t.save(),t.fillStyle=p,s_(t,o,l,g&&Aa(s,h,f)),t.beginPath();const x=!!n.pathSegment(t,u);let b;if(g){x?t.closePath():fh(t,i,f,s);const m=!!i.pathSegment(t,d,{move:x,reverse:!0});b=x&&m,b||fh(t,i,h,s)}t.closePath(),t.fill(b?"evenodd":"nonzero"),t.restore()}}function s_(t,e,n,i){const s=e.chart.chartArea,{property:r,start:o,end:l}=i||{};if(r==="x"||r==="y"){let a,u,d,h;r==="x"?(a=o,u=s.top,d=l,h=s.bottom):(a=s.left,u=o,d=s.right,h=l),t.beginPath(),n&&(a=Math.max(a,n.left),d=Math.min(d,n.right),u=Math.max(u,n.top),h=Math.min(h,n.bottom)),t.rect(a,u,d-a,h-u),t.clip()}}function fh(t,e,n,i){const s=e.interpolate(n,i);s&&t.lineTo(s.x,s.y)}var r_={id:"filler",afterDatasetsUpdate(t,e,n){const i=(t.data.datasets||[]).length,s=[];let r,o,l,a;for(o=0;o<i;++o)r=t.getDatasetMeta(o),l=r.dataset,a=null,l&&l.options&&l instanceof Jt&&(a={visible:t.isDatasetVisible(o),index:o,fill:Wb(l,o,i),chart:t,axis:r.controller.options.indexAxis,scale:r.vScale,line:l}),r.$filler=a,s.push(a);for(o=0;o<i;++o)a=s[o],!(!a||a.fill===!1)&&(a.fill=Hb(s,o,n.propagate))},beforeDraw(t,e,n){const i=n.drawTime==="beforeDraw",s=t.getSortedVisibleDatasetMetas(),r=t.chartArea;for(let o=s.length-1;o>=0;--o){const l=s[o].$filler;l&&(l.line.updateControlPoints(r,l.axis),i&&l.fill&&El(t.ctx,l,r))}},beforeDatasetsDraw(t,e,n){if(n.drawTime!=="beforeDatasetsDraw")return;const i=t.getSortedVisibleDatasetMetas();for(let s=i.length-1;s>=0;--s){const r=i[s].$filler;uh(r)&&El(t.ctx,r,t.chartArea)}},beforeDatasetDraw(t,e,n){const i=e.meta.$filler;!uh(i)||n.drawTime!=="beforeDatasetDraw"||El(t.ctx,i,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const ph=(t,e)=>{let{boxHeight:n=e,boxWidth:i=e}=t;return t.usePointStyle&&(n=Math.min(n,e),i=t.pointStyleWidth||Math.min(i,e)),{boxWidth:i,boxHeight:n,itemHeight:Math.max(e,n)}},o_=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class mh extends gt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,i){this.maxWidth=e,this.maxHeight=n,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=G(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(i=>e.filter(i,this.chart.data))),e.sort&&(n=n.sort((i,s)=>e.sort(i,s,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const i=e.labels,s=ge(i.font),r=s.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:a}=ph(i,r);let u,d;n.font=s.string,this.isHorizontal()?(u=this.maxWidth,d=this._fitRows(o,r,l,a)+10):(d=this.maxHeight,u=this._fitCols(o,s,l,a)+10),this.width=Math.min(u,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,n,i,s){const{ctx:r,maxWidth:o,options:{labels:{padding:l}}}=this,a=this.legendHitBoxes=[],u=this.lineWidths=[0],d=s+l;let h=e;r.textAlign="left",r.textBaseline="middle";let f=-1,p=-d;return this.legendItems.forEach((g,x)=>{const b=i+n/2+r.measureText(g.text).width;(x===0||u[u.length-1]+b+2*l>o)&&(h+=d,u[u.length-(x>0?0:1)]=0,p+=d,f++),a[x]={left:0,top:p,row:f,width:b,height:s},u[u.length-1]+=b+l}),h}_fitCols(e,n,i,s){const{ctx:r,maxHeight:o,options:{labels:{padding:l}}}=this,a=this.legendHitBoxes=[],u=this.columnSizes=[],d=o-e;let h=l,f=0,p=0,g=0,x=0;return this.legendItems.forEach((b,m)=>{const{itemWidth:y,itemHeight:v}=l_(i,n,r,b,s);m>0&&p+v+2*l>d&&(h+=f+l,u.push({width:f,height:p}),g+=f+l,x++,f=p=0),a[m]={left:g,top:p,col:x,width:y,height:v},f=Math.max(f,y),p+=v+l}),h+=f,u.push({width:f,height:p}),h}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:i,labels:{padding:s},rtl:r}}=this,o=fi(r,this.left,this.width);if(this.isHorizontal()){let l=0,a=Ne(i,this.left+s,this.right-this.lineWidths[l]);for(const u of n)l!==u.row&&(l=u.row,a=Ne(i,this.left+s,this.right-this.lineWidths[l])),u.top+=this.top+e+s,u.left=o.leftForLtr(o.x(a),u.width),a+=u.width+s}else{let l=0,a=Ne(i,this.top+e+s,this.bottom-this.columnSizes[l].height);for(const u of n)u.col!==l&&(l=u.col,a=Ne(i,this.top+e+s,this.bottom-this.columnSizes[l].height)),u.top=a,u.left+=this.left+s,u.left=o.leftForLtr(o.x(u.left),u.width),a+=u.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;Vo(e,this),this._draw(),Ho(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:i,ctx:s}=this,{align:r,labels:o}=e,l=ie.color,a=fi(e.rtl,this.left,this.width),u=ge(o.font),{padding:d}=o,h=u.size,f=h/2;let p;this.drawTitle(),s.textAlign=a.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=u.string;const{boxWidth:g,boxHeight:x,itemHeight:b}=ph(o,h),m=function(j,k,S){if(isNaN(g)||g<=0||isNaN(x)||x<0)return;s.save();const C=R(S.lineWidth,1);if(s.fillStyle=R(S.fillStyle,l),s.lineCap=R(S.lineCap,"butt"),s.lineDashOffset=R(S.lineDashOffset,0),s.lineJoin=R(S.lineJoin,"miter"),s.lineWidth=C,s.strokeStyle=R(S.strokeStyle,l),s.setLineDash(R(S.lineDash,[])),o.usePointStyle){const M={radius:x*Math.SQRT2/2,pointStyle:S.pointStyle,rotation:S.rotation,borderWidth:C},A=a.xPlus(j,g/2),I=k+f;dm(s,M,A,I,o.pointStyleWidth&&g)}else{const M=k+Math.max((h-x)/2,0),A=a.leftForLtr(j,g),I=Tn(S.borderRadius);s.beginPath(),Object.values(I).some(se=>se!==0)?Ls(s,{x:A,y:M,w:g,h:x,radius:I}):s.rect(A,M,g,x),s.fill(),C!==0&&s.stroke()}s.restore()},y=function(j,k,S){Vn(s,S.text,j,k+b/2,u,{strikethrough:S.hidden,textAlign:a.textAlign(S.textAlign)})},v=this.isHorizontal(),_=this._computeTitleHeight();v?p={x:Ne(r,this.left+d,this.right-i[0]),y:this.top+d+_,line:0}:p={x:this.left+d,y:Ne(r,this.top+_+d,this.bottom-n[0].height),line:0},vm(this.ctx,e.textDirection);const w=b+d;this.legendItems.forEach((j,k)=>{s.strokeStyle=j.fontColor,s.fillStyle=j.fontColor;const S=s.measureText(j.text).width,C=a.textAlign(j.textAlign||(j.textAlign=o.textAlign)),M=g+f+S;let A=p.x,I=p.y;a.setWidth(this.width),v?k>0&&A+M+d>this.right&&(I=p.y+=w,p.line++,A=p.x=Ne(r,this.left+d,this.right-i[p.line])):k>0&&I+w>this.bottom&&(A=p.x=A+n[p.line].width+d,p.line++,I=p.y=Ne(r,this.top+_+d,this.bottom-n[p.line].height));const se=a.x(A);if(m(se,I,j),A=Ny(C,A+g+f,v?A+M:this.right,e.rtl),y(a.x(A),I,j),v)p.x+=M+d;else if(typeof j.text!="string"){const ke=u.lineHeight;p.y+=Um(j,ke)+d}else p.y+=w}),bm(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,i=ge(n.font),s=Le(n.padding);if(!n.display)return;const r=fi(e.rtl,this.left,this.width),o=this.ctx,l=n.position,a=i.size/2,u=s.top+a;let d,h=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),d=this.top+u,h=Ne(e.align,h,this.right-f);else{const g=this.columnSizes.reduce((x,b)=>Math.max(x,b.height),0);d=u+Ne(e.align,this.top,this.bottom-g-e.labels.padding-this._computeTitleHeight())}const p=Ne(l,h,h+f);o.textAlign=r.textAlign(Vc(l)),o.textBaseline="middle",o.strokeStyle=n.color,o.fillStyle=n.color,o.font=i.string,Vn(o,n.text,p,d,i)}_computeTitleHeight(){const e=this.options.title,n=ge(e.font),i=Le(e.padding);return e.display?n.lineHeight+i.height:0}_getLegendItemAt(e,n){let i,s,r;if(Ot(e,this.left,this.right)&&Ot(n,this.top,this.bottom)){for(r=this.legendHitBoxes,i=0;i<r.length;++i)if(s=r[i],Ot(e,s.left,s.left+s.width)&&Ot(n,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(e){const n=this.options;if(!u_(e.type,n))return;const i=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const s=this._hoveredItem,r=o_(s,i);s&&!r&&G(n.onLeave,[e,s,this],this),this._hoveredItem=i,i&&!r&&G(n.onHover,[e,i,this],this)}else i&&G(n.onClick,[e,i,this],this)}}function l_(t,e,n,i,s){const r=a_(i,t,e,n),o=c_(s,i,e.lineHeight);return{itemWidth:r,itemHeight:o}}function a_(t,e,n,i){let s=t.text;return s&&typeof s!="string"&&(s=s.reduce((r,o)=>r.length>o.length?r:o)),e+n.size/2+i.measureText(s).width}function c_(t,e,n){let i=t;return typeof e.text!="string"&&(i=Um(e,n)),i}function Um(t,e){const n=t.text?t.text.length:0;return e*n}function u_(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var d_={id:"legend",_element:mh,start(t,e,n){const i=t.legend=new mh({ctx:t.ctx,options:n,chart:t});Pe.configure(t,i,n),Pe.addBox(t,i)},stop(t){Pe.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const i=t.legend;Pe.configure(t,i,n),i.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const i=e.datasetIndex,s=n.chart;s.isDatasetVisible(i)?(s.hide(i),e.hidden=!0):(s.show(i),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:i,textAlign:s,color:r,useBorderRadius:o,borderRadius:l}}=t.legend.options;return t._getSortedDatasetMetas().map(a=>{const u=a.controller.getStyle(n?0:void 0),d=Le(u.borderWidth);return{text:e[a.index].label,fillStyle:u.backgroundColor,fontColor:r,hidden:!a.visible,lineCap:u.borderCapStyle,lineDash:u.borderDash,lineDashOffset:u.borderDashOffset,lineJoin:u.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:u.borderColor,pointStyle:i||u.pointStyle,rotation:u.rotation,textAlign:s||u.textAlign,borderRadius:o&&(l||u.borderRadius),datasetIndex:a.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Qc extends gt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const s=ne(i.text)?i.text.length:1;this._padding=Le(i.padding);const r=s*ge(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:i,bottom:s,right:r,options:o}=this,l=o.align;let a=0,u,d,h;return this.isHorizontal()?(d=Ne(l,i,r),h=n+e,u=r-i):(o.position==="left"?(d=i+e,h=Ne(l,s,n),a=H*-.5):(d=r-e,h=Ne(l,n,s),a=H*.5),u=s-n),{titleX:d,titleY:h,maxWidth:u,rotation:a}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const i=ge(n.font),r=i.lineHeight/2+this._padding.top,{titleX:o,titleY:l,maxWidth:a,rotation:u}=this._drawArgs(r);Vn(e,n.text,0,0,i,{color:n.color,maxWidth:a,rotation:u,textAlign:Vc(n.align),textBaseline:"middle",translation:[o,l]})}}function h_(t,e){const n=new Qc({ctx:t.ctx,options:e,chart:t});Pe.configure(t,n,e),Pe.addBox(t,n),t.titleBlock=n}var f_={id:"title",_element:Qc,start(t,e,n){h_(t,n)},stop(t){const e=t.titleBlock;Pe.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const i=t.titleBlock;Pe.configure(t,i,n),i.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const vr=new WeakMap;var p_={id:"subtitle",start(t,e,n){const i=new Qc({ctx:t.ctx,options:n,chart:t});Pe.configure(t,i,n),Pe.addBox(t,i),vr.set(t,i)},stop(t){Pe.removeBox(t,vr.get(t)),vr.delete(t)},beforeUpdate(t,e,n){const i=vr.get(t);Pe.configure(t,i,n),i.options=n},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Gi={average(t){if(!t.length)return!1;let e,n,i=new Set,s=0,r=0;for(e=0,n=t.length;e<n;++e){const l=t[e].element;if(l&&l.hasValue()){const a=l.tooltipPosition();i.add(a.x),s+=a.y,++r}}return r===0||i.size===0?!1:{x:[...i].reduce((l,a)=>l+a)/i.size,y:s/r}},nearest(t,e){if(!t.length)return!1;let n=e.x,i=e.y,s=Number.POSITIVE_INFINITY,r,o,l;for(r=0,o=t.length;r<o;++r){const a=t[r].element;if(a&&a.hasValue()){const u=a.getCenterPoint(),d=Na(e,u);d<s&&(s=d,l=a)}}if(l){const a=l.tooltipPosition();n=a.x,i=a.y}return{x:n,y:i}}};function vt(t,e){return e&&(ne(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Pt(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function m_(t,e){const{element:n,datasetIndex:i,index:s}=e,r=t.getDatasetMeta(i).controller,{label:o,value:l}=r.getLabelAndValue(s);return{chart:t,label:o,parsed:r.getParsed(s),raw:t.data.datasets[i].data[s],formattedValue:l,dataset:r.getDataset(),dataIndex:s,datasetIndex:i,element:n}}function gh(t,e){const n=t.chart.ctx,{body:i,footer:s,title:r}=t,{boxWidth:o,boxHeight:l}=e,a=ge(e.bodyFont),u=ge(e.titleFont),d=ge(e.footerFont),h=r.length,f=s.length,p=i.length,g=Le(e.padding);let x=g.height,b=0,m=i.reduce((_,w)=>_+w.before.length+w.lines.length+w.after.length,0);if(m+=t.beforeBody.length+t.afterBody.length,h&&(x+=h*u.lineHeight+(h-1)*e.titleSpacing+e.titleMarginBottom),m){const _=e.displayColors?Math.max(l,a.lineHeight):a.lineHeight;x+=p*_+(m-p)*a.lineHeight+(m-1)*e.bodySpacing}f&&(x+=e.footerMarginTop+f*d.lineHeight+(f-1)*e.footerSpacing);let y=0;const v=function(_){b=Math.max(b,n.measureText(_).width+y)};return n.save(),n.font=u.string,$(t.title,v),n.font=a.string,$(t.beforeBody.concat(t.afterBody),v),y=e.displayColors?o+2+e.boxPadding:0,$(i,_=>{$(_.before,v),$(_.lines,v),$(_.after,v)}),y=0,n.font=d.string,$(t.footer,v),n.restore(),b+=g.width,{width:b,height:x}}function g_(t,e){const{y:n,height:i}=e;return n<i/2?"top":n>t.height-i/2?"bottom":"center"}function x_(t,e,n,i){const{x:s,width:r}=i,o=n.caretSize+n.caretPadding;if(t==="left"&&s+r+o>e.width||t==="right"&&s-r-o<0)return!0}function y_(t,e,n,i){const{x:s,width:r}=n,{width:o,chartArea:{left:l,right:a}}=t;let u="center";return i==="center"?u=s<=(l+a)/2?"left":"right":s<=r/2?u="left":s>=o-r/2&&(u="right"),x_(u,t,e,n)&&(u="center"),u}function xh(t,e,n){const i=n.yAlign||e.yAlign||g_(t,n);return{xAlign:n.xAlign||e.xAlign||y_(t,e,n,i),yAlign:i}}function v_(t,e){let{x:n,width:i}=t;return e==="right"?n-=i:e==="center"&&(n-=i/2),n}function b_(t,e,n){let{y:i,height:s}=t;return e==="top"?i+=n:e==="bottom"?i-=s+n:i-=s/2,i}function yh(t,e,n,i){const{caretSize:s,caretPadding:r,cornerRadius:o}=t,{xAlign:l,yAlign:a}=n,u=s+r,{topLeft:d,topRight:h,bottomLeft:f,bottomRight:p}=Tn(o);let g=v_(e,l);const x=b_(e,a,u);return a==="center"?l==="left"?g+=u:l==="right"&&(g-=u):l==="left"?g-=Math.max(d,f)+s:l==="right"&&(g+=Math.max(h,p)+s),{x:ye(g,0,i.width-e.width),y:ye(x,0,i.height-e.height)}}function br(t,e,n){const i=Le(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-i.right:t.x+i.left}function vh(t){return vt([],Pt(t))}function __(t,e,n){return yn(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function bh(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const Km={beforeTitle:Mt,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,i=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(i>0&&e.dataIndex<i)return n[e.dataIndex]}return""},afterTitle:Mt,beforeBody:Mt,beforeLabel:Mt,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return z(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Mt,afterBody:Mt,beforeFooter:Mt,footer:Mt,afterFooter:Mt};function ze(t,e,n,i){const s=t[e].call(n,i);return typeof s>"u"?Km[e].call(n,i):s}class Oa extends gt{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&n.options.animation&&i.animations,r=new jm(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=__(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:i}=n,s=ze(i,"beforeTitle",this,e),r=ze(i,"title",this,e),o=ze(i,"afterTitle",this,e);let l=[];return l=vt(l,Pt(s)),l=vt(l,Pt(r)),l=vt(l,Pt(o)),l}getBeforeBody(e,n){return vh(ze(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:i}=n,s=[];return $(e,r=>{const o={before:[],lines:[],after:[]},l=bh(i,r);vt(o.before,Pt(ze(l,"beforeLabel",this,r))),vt(o.lines,ze(l,"label",this,r)),vt(o.after,Pt(ze(l,"afterLabel",this,r))),s.push(o)}),s}getAfterBody(e,n){return vh(ze(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:i}=n,s=ze(i,"beforeFooter",this,e),r=ze(i,"footer",this,e),o=ze(i,"afterFooter",this,e);let l=[];return l=vt(l,Pt(s)),l=vt(l,Pt(r)),l=vt(l,Pt(o)),l}_createItems(e){const n=this._active,i=this.chart.data,s=[],r=[],o=[];let l=[],a,u;for(a=0,u=n.length;a<u;++a)l.push(m_(this.chart,n[a]));return e.filter&&(l=l.filter((d,h,f)=>e.filter(d,h,f,i))),e.itemSort&&(l=l.sort((d,h)=>e.itemSort(d,h,i))),$(l,d=>{const h=bh(e.callbacks,d);s.push(ze(h,"labelColor",this,d)),r.push(ze(h,"labelPointStyle",this,d)),o.push(ze(h,"labelTextColor",this,d))}),this.labelColors=s,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=l,l}update(e,n){const i=this.options.setContext(this.getContext()),s=this._active;let r,o=[];if(!s.length)this.opacity!==0&&(r={opacity:0});else{const l=Gi[i.position].call(this,s,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const a=this._size=gh(this,i),u=Object.assign({},l,a),d=xh(this.chart,i,u),h=yh(i,u,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,r={opacity:1,x:h.x,y:h.y,width:a.width,height:a.height,caretX:l.x,caretY:l.y}}this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),e&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,i,s){const r=this.getCaretPosition(e,i,s);n.lineTo(r.x1,r.y1),n.lineTo(r.x2,r.y2),n.lineTo(r.x3,r.y3)}getCaretPosition(e,n,i){const{xAlign:s,yAlign:r}=this,{caretSize:o,cornerRadius:l}=i,{topLeft:a,topRight:u,bottomLeft:d,bottomRight:h}=Tn(l),{x:f,y:p}=e,{width:g,height:x}=n;let b,m,y,v,_,w;return r==="center"?(_=p+x/2,s==="left"?(b=f,m=b-o,v=_+o,w=_-o):(b=f+g,m=b+o,v=_-o,w=_+o),y=b):(s==="left"?m=f+Math.max(a,d)+o:s==="right"?m=f+g-Math.max(u,h)-o:m=this.caretX,r==="top"?(v=p,_=v-o,b=m-o,y=m+o):(v=p+x,_=v+o,b=m+o,y=m-o),w=v),{x1:b,x2:m,x3:y,y1:v,y2:_,y3:w}}drawTitle(e,n,i){const s=this.title,r=s.length;let o,l,a;if(r){const u=fi(i.rtl,this.x,this.width);for(e.x=br(this,i.titleAlign,i),n.textAlign=u.textAlign(i.titleAlign),n.textBaseline="middle",o=ge(i.titleFont),l=i.titleSpacing,n.fillStyle=i.titleColor,n.font=o.string,a=0;a<r;++a)n.fillText(s[a],u.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+l,a+1===r&&(e.y+=i.titleMarginBottom-l)}}_drawColorBox(e,n,i,s,r){const o=this.labelColors[i],l=this.labelPointStyles[i],{boxHeight:a,boxWidth:u}=r,d=ge(r.bodyFont),h=br(this,"left",r),f=s.x(h),p=a<d.lineHeight?(d.lineHeight-a)/2:0,g=n.y+p;if(r.usePointStyle){const x={radius:Math.min(u,a)/2,pointStyle:l.pointStyle,rotation:l.rotation,borderWidth:1},b=s.leftForLtr(f,u)+u/2,m=g+a/2;e.strokeStyle=r.multiKeyBackground,e.fillStyle=r.multiKeyBackground,Ca(e,x,b,m),e.strokeStyle=o.borderColor,e.fillStyle=o.backgroundColor,Ca(e,x,b,m)}else{e.lineWidth=B(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,e.strokeStyle=o.borderColor,e.setLineDash(o.borderDash||[]),e.lineDashOffset=o.borderDashOffset||0;const x=s.leftForLtr(f,u),b=s.leftForLtr(s.xPlus(f,1),u-2),m=Tn(o.borderRadius);Object.values(m).some(y=>y!==0)?(e.beginPath(),e.fillStyle=r.multiKeyBackground,Ls(e,{x,y:g,w:u,h:a,radius:m}),e.fill(),e.stroke(),e.fillStyle=o.backgroundColor,e.beginPath(),Ls(e,{x:b,y:g+1,w:u-2,h:a-2,radius:m}),e.fill()):(e.fillStyle=r.multiKeyBackground,e.fillRect(x,g,u,a),e.strokeRect(x,g,u,a),e.fillStyle=o.backgroundColor,e.fillRect(b,g+1,u-2,a-2))}e.fillStyle=this.labelTextColors[i]}drawBody(e,n,i){const{body:s}=this,{bodySpacing:r,bodyAlign:o,displayColors:l,boxHeight:a,boxWidth:u,boxPadding:d}=i,h=ge(i.bodyFont);let f=h.lineHeight,p=0;const g=fi(i.rtl,this.x,this.width),x=function(S){n.fillText(S,g.x(e.x+p),e.y+f/2),e.y+=f+r},b=g.textAlign(o);let m,y,v,_,w,j,k;for(n.textAlign=o,n.textBaseline="middle",n.font=h.string,e.x=br(this,b,i),n.fillStyle=i.bodyColor,$(this.beforeBody,x),p=l&&b!=="right"?o==="center"?u/2+d:u+2+d:0,_=0,j=s.length;_<j;++_){for(m=s[_],y=this.labelTextColors[_],n.fillStyle=y,$(m.before,x),v=m.lines,l&&v.length&&(this._drawColorBox(n,e,_,g,i),f=Math.max(h.lineHeight,a)),w=0,k=v.length;w<k;++w)x(v[w]),f=h.lineHeight;$(m.after,x)}p=0,f=h.lineHeight,$(this.afterBody,x),e.y-=r}drawFooter(e,n,i){const s=this.footer,r=s.length;let o,l;if(r){const a=fi(i.rtl,this.x,this.width);for(e.x=br(this,i.footerAlign,i),e.y+=i.footerMarginTop,n.textAlign=a.textAlign(i.footerAlign),n.textBaseline="middle",o=ge(i.footerFont),n.fillStyle=i.footerColor,n.font=o.string,l=0;l<r;++l)n.fillText(s[l],a.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+i.footerSpacing}}drawBackground(e,n,i,s){const{xAlign:r,yAlign:o}=this,{x:l,y:a}=e,{width:u,height:d}=i,{topLeft:h,topRight:f,bottomLeft:p,bottomRight:g}=Tn(s.cornerRadius);n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,n.lineWidth=s.borderWidth,n.beginPath(),n.moveTo(l+h,a),o==="top"&&this.drawCaret(e,n,i,s),n.lineTo(l+u-f,a),n.quadraticCurveTo(l+u,a,l+u,a+f),o==="center"&&r==="right"&&this.drawCaret(e,n,i,s),n.lineTo(l+u,a+d-g),n.quadraticCurveTo(l+u,a+d,l+u-g,a+d),o==="bottom"&&this.drawCaret(e,n,i,s),n.lineTo(l+p,a+d),n.quadraticCurveTo(l,a+d,l,a+d-p),o==="center"&&r==="left"&&this.drawCaret(e,n,i,s),n.lineTo(l,a+h),n.quadraticCurveTo(l,a,l+h,a),n.closePath(),n.fill(),s.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){const o=Gi[e.position].call(this,this._active,this._eventPosition);if(!o)return;const l=this._size=gh(this,e),a=Object.assign({},o,this._size),u=xh(n,e,a),d=yh(e,a,u,n);(s._to!==d.x||r._to!==d.y)&&(this.xAlign=u.xAlign,this.yAlign=u.yAlign,this.width=l.width,this.height=l.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(n);const s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=Le(n.padding),l=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&l&&(e.save(),e.globalAlpha=i,this.drawBackground(r,e,s,n),vm(e,n.textDirection),r.y+=o.top,this.drawTitle(r,e,n),this.drawBody(r,e,n),this.drawFooter(r,e,n),bm(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const i=this._active,s=e.map(({datasetIndex:l,index:a})=>{const u=this.chart.getDatasetMeta(l);if(!u)throw new Error("Cannot find a dataset at index "+l);return{datasetIndex:l,element:u.data[a],index:a}}),r=!go(i,s),o=this._positionChanged(s,n);(r||o)&&(this._active=s,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,i=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,r=this._active||[],o=this._getActiveElements(e,r,n,i),l=this._positionChanged(o,e),a=n||!go(o,r)||l;return a&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),a}_getActiveElements(e,n,i,s){const r=this.options;if(e.type==="mouseout")return[];if(!s)return n.filter(l=>this.chart.data.datasets[l.datasetIndex]&&this.chart.getDatasetMeta(l.datasetIndex).controller.getParsed(l.index)!==void 0);const o=this.chart.getElementsAtEventForMode(e,r.mode,r,i);return r.reverse&&o.reverse(),o}_positionChanged(e,n){const{caretX:i,caretY:s,options:r}=this,o=Gi[r.position].call(this,e,n);return o!==!1&&(i!==o.x||s!==o.y)}}P(Oa,"positioners",Gi);var w_={id:"tooltip",_element:Oa,positioners:Gi,afterInit(t,e,n){n&&(t.tooltip=new Oa({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Km},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},k_=Object.freeze({__proto__:null,Colors:Ob,Decimation:Fb,Filler:r_,Legend:d_,SubTitle:p_,Title:f_,Tooltip:w_});const S_=(t,e,n,i)=>(typeof e=="string"?(n=t.push(e)-1,i.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function j_(t,e,n,i){const s=t.indexOf(e);if(s===-1)return S_(t,e,n,i);const r=t.lastIndexOf(e);return s!==r?n:s}const N_=(t,e)=>t===null?null:ye(Math.round(t),0,e);function _h(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class Ra extends $n{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const i=this.getLabels();for(const{index:s,label:r}of n)i[s]===r&&i.splice(s,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(z(e))return null;const i=this.getLabels();return n=isFinite(n)&&i[n]===e?n:j_(i,e,R(n,e),this._addedLabels),N_(n,i.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(i=0),n||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const e=this.min,n=this.max,i=this.options.offset,s=[];let r=this.getLabels();r=e===0&&n===r.length-1?r:r.slice(e,n+1),this._valueRange=Math.max(r.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=e;o<=n;o++)s.push({value:o});return s}getLabelForValue(e){return _h.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}P(Ra,"id","category"),P(Ra,"defaults",{ticks:{callback:_h}});function M_(t,e){const n=[],{bounds:s,step:r,min:o,max:l,precision:a,count:u,maxTicks:d,maxDigits:h,includeBounds:f}=t,p=r||1,g=d-1,{min:x,max:b}=e,m=!z(o),y=!z(l),v=!z(u),_=(b-x)/(h+1);let w=gd((b-x)/g/p)*p,j,k,S,C;if(w<1e-14&&!m&&!y)return[{value:x},{value:b}];C=Math.ceil(b/w)-Math.floor(x/w),C>g&&(w=gd(C*w/g/p)*p),z(a)||(j=Math.pow(10,a),w=Math.ceil(w*j)/j),s==="ticks"?(k=Math.floor(x/w)*w,S=Math.ceil(b/w)*w):(k=x,S=b),m&&y&&r&&vy((l-o)/r,w/1e3)?(C=Math.round(Math.min((l-o)/w,d)),w=(l-o)/C,k=o,S=l):v?(k=m?o:k,S=y?l:S,C=u-1,w=(S-k)/C):(C=(S-k)/w,os(C,Math.round(C),w/1e3)?C=Math.round(C):C=Math.ceil(C));const M=Math.max(xd(w),xd(k));j=Math.pow(10,z(a)?M:a),k=Math.round(k*j)/j,S=Math.round(S*j)/j;let A=0;for(m&&(f&&k!==o?(n.push({value:o}),k<o&&A++,os(Math.round((k+A*w)*j)/j,o,wh(o,_,t))&&A++):k<o&&A++);A<C;++A){const I=Math.round((k+A*w)*j)/j;if(y&&I>l)break;n.push({value:I})}return y&&f&&S!==l?n.length&&os(n[n.length-1].value,l,wh(l,_,t))?n[n.length-1].value=l:n.push({value:l}):(!y||S===l)&&n.push({value:S}),n}function wh(t,e,{horizontal:n,minRotation:i}){const s=dt(i),r=(n?Math.sin(s):Math.cos(s))||.001,o=.75*e*(""+t).length;return Math.min(e/r,o)}class ko extends $n{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return z(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:i}=this.getUserBounds();let{min:s,max:r}=this;const o=a=>s=n?s:a,l=a=>r=i?r:a;if(e){const a=jt(s),u=jt(r);a<0&&u<0?l(0):a>0&&u>0&&o(0)}if(s===r){let a=r===0?1:Math.abs(r*.05);l(r+a),e||o(s-a)}this.min=s,this.max=r}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:i}=e,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),n=n||11),n&&(s=Math.min(n,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s={maxTicks:i,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},r=this._range||this,o=M_(s,r);return e.bounds==="ticks"&&nm(o,this,"value"),e.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const e=this.ticks;let n=this.min,i=this.max;if(super.configure(),this.options.offset&&e.length){const s=(i-n)/Math.max(e.length-1,1)/2;n-=s,i+=s}this._startValue=n,this._endValue=i,this._valueRange=i-n}getLabelForValue(e){return Hs(e,this.chart.options.locale,this.options.ticks.format)}}class Ia extends ko{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=ue(e)?e:0,this.max=ue(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,i=dt(this.options.ticks.minRotation),s=(e?Math.sin(i):Math.cos(i))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,r.lineHeight/s))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}P(Ia,"id","linear"),P(Ia,"defaults",{ticks:{callback:Bo.formatters.numeric}});const Ts=t=>Math.floor(Zt(t)),wn=(t,e)=>Math.pow(10,Ts(t)+e);function kh(t){return t/Math.pow(10,Ts(t))===1}function Sh(t,e,n){const i=Math.pow(10,n),s=Math.floor(t/i);return Math.ceil(e/i)-s}function C_(t,e){const n=e-t;let i=Ts(n);for(;Sh(t,e,i)>10;)i++;for(;Sh(t,e,i)<10;)i--;return Math.min(i,Ts(t))}function P_(t,{min:e,max:n}){e=Ue(t.min,e);const i=[],s=Ts(e);let r=C_(e,n),o=r<0?Math.pow(10,Math.abs(r)):1;const l=Math.pow(10,r),a=s>r?Math.pow(10,s):0,u=Math.round((e-a)*o)/o,d=Math.floor((e-a)/l/10)*l*10;let h=Math.floor((u-d)/Math.pow(10,r)),f=Ue(t.min,Math.round((a+d+h*Math.pow(10,r))*o)/o);for(;f<n;)i.push({value:f,major:kh(f),significand:h}),h>=10?h=h<15?15:20:h++,h>=20&&(r++,h=2,o=r>=0?1:o),f=Math.round((a+d+h*Math.pow(10,r))*o)/o;const p=Ue(t.max,f);return i.push({value:p,major:kh(p),significand:h}),i}class za extends $n{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(e,n){const i=ko.prototype.parse.apply(this,[e,n]);if(i===0){this._zero=!0;return}return ue(i)&&i>0?i:null}determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=ue(e)?Math.max(0,e):null,this.max=ue(n)?Math.max(0,n):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!ue(this._userMin)&&(this.min=e===wn(this.min,0)?wn(this.min,-1):wn(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let i=this.min,s=this.max;const r=l=>i=e?i:l,o=l=>s=n?s:l;i===s&&(i<=0?(r(1),o(10)):(r(wn(i,-1)),o(wn(s,1)))),i<=0&&r(wn(s,-1)),s<=0&&o(wn(i,1)),this.min=i,this.max=s}buildTicks(){const e=this.options,n={min:this._userMin,max:this._userMax},i=P_(n,this);return e.bounds==="ticks"&&nm(i,this,"value"),e.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(e){return e===void 0?"0":Hs(e,this.chart.options.locale,this.options.ticks.format)}configure(){const e=this.min;super.configure(),this._startValue=Zt(e),this._valueRange=Zt(this.max)-Zt(e)}getPixelForValue(e){return(e===void 0||e===0)&&(e=this.min),e===null||isNaN(e)?NaN:this.getPixelForDecimal(e===this.min?0:(Zt(e)-this._startValue)/this._valueRange)}getValueForPixel(e){const n=this.getDecimalForPixel(e);return Math.pow(10,this._startValue+n*this._valueRange)}}P(za,"id","logarithmic"),P(za,"defaults",{ticks:{callback:Bo.formatters.logarithmic,major:{enabled:!0}}});function Fa(t){const e=t.ticks;if(e.display&&t.display){const n=Le(e.backdropPadding);return R(e.font&&e.font.size,ie.font.size)+n.height}return 0}function E_(t,e,n){return n=ne(n)?n:[n],{w:Ry(t,e.string,n),h:n.length*e.lineHeight}}function jh(t,e,n,i,s){return t===i||t===s?{start:e-n/2,end:e+n/2}:t<i||t>s?{start:e-n,end:e}:{start:e,end:e+n}}function L_(t){const e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},n=Object.assign({},e),i=[],s=[],r=t._pointLabels.length,o=t.options.pointLabels,l=o.centerPointLabels?H/r:0;for(let a=0;a<r;a++){const u=o.setContext(t.getPointLabelContext(a));s[a]=u.padding;const d=t.getPointPosition(a,t.drawingArea+s[a],l),h=ge(u.font),f=E_(t.ctx,h,t._pointLabels[a]);i[a]=f;const p=Me(t.getIndexAngle(a)+l),g=Math.round(Fc(p)),x=jh(g,d.x,f.w,0,180),b=jh(g,d.y,f.h,90,270);D_(n,e,p,x,b)}t.setCenterPoint(e.l-n.l,n.r-e.r,e.t-n.t,n.b-e.b),t._pointLabelItems=O_(t,i,s)}function D_(t,e,n,i,s){const r=Math.abs(Math.sin(n)),o=Math.abs(Math.cos(n));let l=0,a=0;i.start<e.l?(l=(e.l-i.start)/r,t.l=Math.min(t.l,e.l-l)):i.end>e.r&&(l=(i.end-e.r)/r,t.r=Math.max(t.r,e.r+l)),s.start<e.t?(a=(e.t-s.start)/o,t.t=Math.min(t.t,e.t-a)):s.end>e.b&&(a=(s.end-e.b)/o,t.b=Math.max(t.b,e.b+a))}function T_(t,e,n){const i=t.drawingArea,{extra:s,additionalAngle:r,padding:o,size:l}=n,a=t.getPointPosition(e,i+s+o,r),u=Math.round(Fc(Me(a.angle+he))),d=z_(a.y,l.h,u),h=R_(u),f=I_(a.x,l.w,h);return{visible:!0,x:a.x,y:d,textAlign:h,left:f,top:d,right:f+l.w,bottom:d+l.h}}function A_(t,e){if(!e)return!0;const{left:n,top:i,right:s,bottom:r}=t;return!(It({x:n,y:i},e)||It({x:n,y:r},e)||It({x:s,y:i},e)||It({x:s,y:r},e))}function O_(t,e,n){const i=[],s=t._pointLabels.length,r=t.options,{centerPointLabels:o,display:l}=r.pointLabels,a={extra:Fa(r)/2,additionalAngle:o?H/s:0};let u;for(let d=0;d<s;d++){a.padding=n[d],a.size=e[d];const h=T_(t,d,a);i.push(h),l==="auto"&&(h.visible=A_(h,u),h.visible&&(u=h))}return i}function R_(t){return t===0||t===180?"center":t<180?"left":"right"}function I_(t,e,n){return n==="right"?t-=e:n==="center"&&(t-=e/2),t}function z_(t,e,n){return n===90||n===270?t-=e/2:(n>270||n<90)&&(t-=e),t}function F_(t,e,n){const{left:i,top:s,right:r,bottom:o}=n,{backdropColor:l}=e;if(!z(l)){const a=Tn(e.borderRadius),u=Le(e.backdropPadding);t.fillStyle=l;const d=i-u.left,h=s-u.top,f=r-i+u.width,p=o-s+u.height;Object.values(a).some(g=>g!==0)?(t.beginPath(),Ls(t,{x:d,y:h,w:f,h:p,radius:a}),t.fill()):t.fillRect(d,h,f,p)}}function B_(t,e){const{ctx:n,options:{pointLabels:i}}=t;for(let s=e-1;s>=0;s--){const r=t._pointLabelItems[s];if(!r.visible)continue;const o=i.setContext(t.getPointLabelContext(s));F_(n,o,r);const l=ge(o.font),{x:a,y:u,textAlign:d}=r;Vn(n,t._pointLabels[s],a,u+l.lineHeight/2,l,{color:o.color,textAlign:d,textBaseline:"middle"})}}function Ym(t,e,n,i){const{ctx:s}=t;if(n)s.arc(t.xCenter,t.yCenter,e,0,ee);else{let r=t.getPointPosition(0,e);s.moveTo(r.x,r.y);for(let o=1;o<i;o++)r=t.getPointPosition(o,e),s.lineTo(r.x,r.y)}}function V_(t,e,n,i,s){const r=t.ctx,o=e.circular,{color:l,lineWidth:a}=e;!o&&!i||!l||!a||n<0||(r.save(),r.strokeStyle=l,r.lineWidth=a,r.setLineDash(s.dash||[]),r.lineDashOffset=s.dashOffset,r.beginPath(),Ym(t,n,o,i),r.closePath(),r.stroke(),r.restore())}function H_(t,e,n){return yn(t,{label:n,index:e,type:"pointLabel"})}class Xi extends ko{constructor(e){super(e),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const e=this._padding=Le(Fa(this.options)/2),n=this.width=this.maxWidth-e.width,i=this.height=this.maxHeight-e.height;this.xCenter=Math.floor(this.left+n/2+e.left),this.yCenter=Math.floor(this.top+i/2+e.top),this.drawingArea=Math.floor(Math.min(n,i)/2)}determineDataLimits(){const{min:e,max:n}=this.getMinMax(!1);this.min=ue(e)&&!isNaN(e)?e:0,this.max=ue(n)&&!isNaN(n)?n:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Fa(this.options))}generateTickLabels(e){ko.prototype.generateTickLabels.call(this,e),this._pointLabels=this.getLabels().map((n,i)=>{const s=G(this.options.pointLabels.callback,[n,i],this);return s||s===0?s:""}).filter((n,i)=>this.chart.getDataVisibility(i))}fit(){const e=this.options;e.display&&e.pointLabels.display?L_(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(e,n,i,s){this.xCenter+=Math.floor((e-n)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(e,n,i,s))}getIndexAngle(e){const n=ee/(this._pointLabels.length||1),i=this.options.startAngle||0;return Me(e*n+dt(i))}getDistanceFromCenterForValue(e){if(z(e))return NaN;const n=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-e)*n:(e-this.min)*n}getValueForDistanceFromCenter(e){if(z(e))return NaN;const n=e/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-n:this.min+n}getPointLabelContext(e){const n=this._pointLabels||[];if(e>=0&&e<n.length){const i=n[e];return H_(this.getContext(),e,i)}}getPointPosition(e,n,i=0){const s=this.getIndexAngle(e)-he+i;return{x:Math.cos(s)*n+this.xCenter,y:Math.sin(s)*n+this.yCenter,angle:s}}getPointPositionForValue(e,n){return this.getPointPosition(e,this.getDistanceFromCenterForValue(n))}getBasePosition(e){return this.getPointPositionForValue(e||0,this.getBaseValue())}getPointLabelPosition(e){const{left:n,top:i,right:s,bottom:r}=this._pointLabelItems[e];return{left:n,top:i,right:s,bottom:r}}drawBackground(){const{backgroundColor:e,grid:{circular:n}}=this.options;if(e){const i=this.ctx;i.save(),i.beginPath(),Ym(this,this.getDistanceFromCenterForValue(this._endValue),n,this._pointLabels.length),i.closePath(),i.fillStyle=e,i.fill(),i.restore()}}drawGrid(){const e=this.ctx,n=this.options,{angleLines:i,grid:s,border:r}=n,o=this._pointLabels.length;let l,a,u;if(n.pointLabels.display&&B_(this,o),s.display&&this.ticks.forEach((d,h)=>{if(h!==0||h===0&&this.min<0){a=this.getDistanceFromCenterForValue(d.value);const f=this.getContext(h),p=s.setContext(f),g=r.setContext(f);V_(this,p,a,o,g)}}),i.display){for(e.save(),l=o-1;l>=0;l--){const d=i.setContext(this.getPointLabelContext(l)),{color:h,lineWidth:f}=d;!f||!h||(e.lineWidth=f,e.strokeStyle=h,e.setLineDash(d.borderDash),e.lineDashOffset=d.borderDashOffset,a=this.getDistanceFromCenterForValue(n.reverse?this.min:this.max),u=this.getPointPosition(l,a),e.beginPath(),e.moveTo(this.xCenter,this.yCenter),e.lineTo(u.x,u.y),e.stroke())}e.restore()}}drawBorder(){}drawLabels(){const e=this.ctx,n=this.options,i=n.ticks;if(!i.display)return;const s=this.getIndexAngle(0);let r,o;e.save(),e.translate(this.xCenter,this.yCenter),e.rotate(s),e.textAlign="center",e.textBaseline="middle",this.ticks.forEach((l,a)=>{if(a===0&&this.min>=0&&!n.reverse)return;const u=i.setContext(this.getContext(a)),d=ge(u.font);if(r=this.getDistanceFromCenterForValue(this.ticks[a].value),u.showLabelBackdrop){e.font=d.string,o=e.measureText(l.label).width,e.fillStyle=u.backdropColor;const h=Le(u.backdropPadding);e.fillRect(-o/2-h.left,-r-d.size/2-h.top,o+h.width,d.size+h.height)}Vn(e,l.label,0,-r,d,{color:u.color,strokeColor:u.textStrokeColor,strokeWidth:u.textStrokeWidth})}),e.restore()}drawTitle(){}}P(Xi,"id","radialLinear"),P(Xi,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Bo.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(e){return e},padding:5,centerPointLabels:!1}}),P(Xi,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),P(Xi,"descriptors",{angleLines:{_fallback:"grid"}});const Uo={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Be=Object.keys(Uo);function Nh(t,e){return t-e}function Mh(t,e){if(z(e))return null;const n=t._adapter,{parser:i,round:s,isoWeekday:r}=t._parseOpts;let o=e;return typeof i=="function"&&(o=i(o)),ue(o)||(o=typeof i=="string"?n.parse(o,i):n.parse(o)),o===null?null:(s&&(o=s==="week"&&(_i(r)||r===!0)?n.startOf(o,"isoWeek",r):n.startOf(o,s)),+o)}function Ch(t,e,n,i){const s=Be.length;for(let r=Be.indexOf(t);r<s-1;++r){const o=Uo[Be[r]],l=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((n-e)/(l*o.size))<=i)return Be[r]}return Be[s-1]}function W_(t,e,n,i,s){for(let r=Be.length-1;r>=Be.indexOf(n);r--){const o=Be[r];if(Uo[o].common&&t._adapter.diff(s,i,o)>=e-1)return o}return Be[n?Be.indexOf(n):0]}function $_(t){for(let e=Be.indexOf(t)+1,n=Be.length;e<n;++e)if(Uo[Be[e]].common)return Be[e]}function Ph(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:i,hi:s}=Bc(n,e),r=n[i]>=e?n[i]:n[s];t[r]=!0}}function U_(t,e,n,i){const s=t._adapter,r=+s.startOf(e[0].value,i),o=e[e.length-1].value;let l,a;for(l=r;l<=o;l=+s.add(l,1,i))a=n[l],a>=0&&(e[a].major=!0);return e}function Eh(t,e,n){const i=[],s={},r=e.length;let o,l;for(o=0;o<r;++o)l=e[o],s[l]=o,i.push({value:l,major:!1});return r===0||!n?i:U_(t,i,s,n)}class As extends $n{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const i=e.time||(e.time={}),s=this._adapter=new Jv._date(e.adapters.date);s.init(n),rs(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:Mh(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,i=e.time.unit||"day";let{min:s,max:r,minDefined:o,maxDefined:l}=this.getUserBounds();function a(u){!o&&!isNaN(u.min)&&(s=Math.min(s,u.min)),!l&&!isNaN(u.max)&&(r=Math.max(r,u.max))}(!o||!l)&&(a(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&a(this.getMinMax(!1))),s=ue(s)&&!isNaN(s)?s:+n.startOf(Date.now(),i),r=ue(r)&&!isNaN(r)?r:+n.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],i=e[e.length-1]),{min:n,max:i}}buildTicks(){const e=this.options,n=e.time,i=e.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const r=this.min,o=this.max,l=ky(s,r,o);return this._unit=n.unit||(i.autoSkip?Ch(n.minUnit,this.min,this.max,this._getLabelCapacity(r)):W_(this,l.length,n.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:$_(this._unit),this.initOffsets(s),e.reverse&&l.reverse(),Eh(this,l,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,i=0,s,r;this.options.offset&&e.length&&(s=this.getDecimalForValue(e[0]),e.length===1?n=1-s:n=(this.getDecimalForValue(e[1])-s)/2,r=this.getDecimalForValue(e[e.length-1]),e.length===1?i=r:i=(r-this.getDecimalForValue(e[e.length-2]))/2);const o=e.length<3?.5:.25;n=ye(n,0,o),i=ye(i,0,o),this._offsets={start:n,end:i,factor:1/(n+1+i)}}_generate(){const e=this._adapter,n=this.min,i=this.max,s=this.options,r=s.time,o=r.unit||Ch(r.minUnit,n,i,this._getLabelCapacity(n)),l=R(s.ticks.stepSize,1),a=o==="week"?r.isoWeekday:!1,u=_i(a)||a===!0,d={};let h=n,f,p;if(u&&(h=+e.startOf(h,"isoWeek",a)),h=+e.startOf(h,u?"day":o),e.diff(i,n,o)>1e5*l)throw new Error(n+" and "+i+" are too far apart with stepSize of "+l+" "+o);const g=s.ticks.source==="data"&&this.getDataTimestamps();for(f=h,p=0;f<i;f=+e.add(f,l,o),p++)Ph(d,f,g);return(f===i||s.bounds==="ticks"||p===1)&&Ph(d,f,g),Object.keys(d).sort(Nh).map(x=>+x)}getLabelForValue(e){const n=this._adapter,i=this.options.time;return i.tooltipFormat?n.format(e,i.tooltipFormat):n.format(e,i.displayFormats.datetime)}format(e,n){const s=this.options.time.displayFormats,r=this._unit,o=n||s[r];return this._adapter.format(e,o)}_tickFormatFunction(e,n,i,s){const r=this.options,o=r.ticks.callback;if(o)return G(o,[e,n,i],this);const l=r.time.displayFormats,a=this._unit,u=this._majorUnit,d=a&&l[a],h=u&&l[u],f=i[n],p=u&&h&&f&&f.major;return this._adapter.format(e,s||(p?h:d))}generateTickLabels(e){let n,i,s;for(n=0,i=e.length;n<i;++n)s=e[n],s.label=this._tickFormatFunction(s.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,i=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+i)*n.factor)}getValueForPixel(e){const n=this._offsets,i=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+i*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,i=this.ctx.measureText(e).width,s=dt(this.isHorizontal()?n.maxRotation:n.minRotation),r=Math.cos(s),o=Math.sin(s),l=this._resolveTickFontOptions(0).size;return{w:i*r+l*o,h:i*o+l*r}}_getLabelCapacity(e){const n=this.options.time,i=n.displayFormats,s=i[n.unit]||i.millisecond,r=this._tickFormatFunction(e,0,Eh(this,[e],this._majorUnit),s),o=this._getLabelSize(r),l=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return l>0?l:1}getDataTimestamps(){let e=this._cache.data||[],n,i;if(e.length)return e;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(n=0,i=s.length;n<i;++n)e=e.concat(s[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,i;if(e.length)return e;const s=this.getLabels();for(n=0,i=s.length;n<i;++n)e.push(Mh(this,s[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return rm(e.sort(Nh))}}P(As,"id","time"),P(As,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function _r(t,e,n){let i=0,s=t.length-1,r,o,l,a;n?(e>=t[i].pos&&e<=t[s].pos&&({lo:i,hi:s}=Rt(t,"pos",e)),{pos:r,time:l}=t[i],{pos:o,time:a}=t[s]):(e>=t[i].time&&e<=t[s].time&&({lo:i,hi:s}=Rt(t,"time",e)),{time:r,pos:l}=t[i],{time:o,pos:a}=t[s]);const u=o-r;return u?l+(a-l)*(e-r)/u:l}class Ba extends As{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=_r(n,this.min),this._tableRange=_r(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:i}=this,s=[],r=[];let o,l,a,u,d;for(o=0,l=e.length;o<l;++o)u=e[o],u>=n&&u<=i&&s.push(u);if(s.length<2)return[{time:n,pos:0},{time:i,pos:1}];for(o=0,l=s.length;o<l;++o)d=s[o+1],a=s[o-1],u=s[o],Math.round((d+a)/2)!==u&&r.push({time:u,pos:o/(l-1)});return r}_generate(){const e=this.min,n=this.max;let i=super.getDataTimestamps();return(!i.includes(e)||!i.length)&&i.splice(0,0,e),(!i.includes(n)||i.length===1)&&i.push(n),i.sort((s,r)=>s-r)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),i=this.getLabelTimestamps();return n.length&&i.length?e=this.normalize(n.concat(i)):e=n.length?n:i,e=this._cache.all=e,e}getDecimalForValue(e){return(_r(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,i=this.getDecimalForPixel(e)/n.factor-n.end;return _r(this._table,i*this._tableRange+this._minPos,!0)}}P(Ba,"id","timeseries"),P(Ba,"defaults",As.defaults);var K_=Object.freeze({__proto__:null,CategoryScale:Ra,LinearScale:Ia,LogarithmicScale:za,RadialLinearScale:Xi,TimeScale:As,TimeSeriesScale:Ba});const qc=[Zv,Cb,k_,K_];Ye.register(...qc);const Os=({leadId:t,isMonitoring:e,artifactType:n="clean",pathologyType:i="normal",showGrid:s=!0,speed:r=25})=>{const o=T.useRef(null),l=T.useRef(null);return T.useEffect(()=>{if(!o.current)return;const a=o.current.getContext("2d");if(!a)return;l.current&&l.current.destroy();const u=()=>{const x=[],y=i==="afib"?80+Math.random()*40:75;for(let v=0;v<3*500;v++){const _=v/500;let w=0;const j=60/y,k=_%j/j;if(i==="normal"){if(k<.1)w=.3*Math.sin(k*20*Math.PI);else if(k>.2&&k<.35){const S=(k-.2)/.15;S<.3?w=-.2*Math.sin(S*10*Math.PI):S<.7?w=1.2*Math.sin((S-.3)*7.5*Math.PI):w=-.3*Math.sin((S-.7)*10*Math.PI)}else if(k>.5&&k<.7){const S=(k-.5)/.2;w=.4*Math.sin(S*Math.PI)}}else if(i==="mi"){if(k<.1)w=.3*Math.sin(k*20*Math.PI);else if(k>.2&&k<.35){const S=(k-.2)/.15;S<.3?w=-.2*Math.sin(S*10*Math.PI):S<.7?w=1.2*Math.sin((S-.3)*7.5*Math.PI):w=-.3*Math.sin((S-.7)*10*Math.PI)}else if(k>.35&&k<.5)w=.4;else if(k>.5&&k<.7){const S=(k-.5)/.2;w=-.3*Math.sin(S*Math.PI)}}else if(i==="afib"){const S=j*(.7+Math.random()*.6),C=_%S/S;if(C>.1&&C<.25){const M=(C-.1)/.15;M<.3?w=-.2*Math.sin(M*10*Math.PI):M<.7?w=1.2*Math.sin((M-.3)*7.5*Math.PI):w=-.3*Math.sin((M-.7)*10*Math.PI)}else if(C>.4&&C<.6){const M=(C-.4)/.2;w=.4*Math.sin(M*Math.PI)}w+=.1*Math.sin(_*40*Math.PI)*Math.random()}n==="baseline"?w+=.2*Math.sin(_*.5*Math.PI):n==="muscle"?w+=.1*(Math.random()-.5):n==="power"&&(w+=.15*Math.sin(_*2*Math.PI*60)),x.push({x:v,y:w})}return x},d=u(),h={type:"line",data:{datasets:[{label:`Lead ${t}`,data:d,borderColor:"#00ff00",backgroundColor:"transparent",borderWidth:2,pointRadius:0,tension:0}]},options:{responsive:!0,maintainAspectRatio:!1,animation:{duration:0},scales:{x:{type:"linear",display:s,grid:{display:s,color:"#444444"},ticks:{color:"#888888"}},y:{display:s,grid:{display:s,color:"#444444"},ticks:{color:"#888888"},min:-2,max:2}},plugins:{legend:{display:!1}}}};l.current=new Ye(a,h);let f,p=0;const g=()=>{if(!e||!l.current)return;p+=r/10;const x=u();l.current.data.datasets[0].data=x.map(b=>({x:b.x-p,y:b.y})),l.current.update("none"),f=requestAnimationFrame(g)};return e&&g(),()=>{f&&cancelAnimationFrame(f),l.current&&l.current.destroy()}},[t,e,n,i,s,r]),c.jsx("div",{className:"w-full h-full",children:c.jsx("canvas",{ref:o,className:"w-full h-full"})})},Y_=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState("I"),[s,r]=T.useState(!1),o=[{id:"I",name:"Lead I",type:"bipolar",description:"LA - RA"},{id:"II",name:"Lead II",type:"bipolar",description:"LL - RA"},{id:"III",name:"Lead III",type:"bipolar",description:"LL - LA"},{id:"aVR",name:"aVR",type:"augmented",description:"Right arm"},{id:"aVL",name:"aVL",type:"augmented",description:"Left arm"},{id:"aVF",name:"aVF",type:"augmented",description:"Left foot"},{id:"V1",name:"V1",type:"precordial",description:"4th intercostal space, right sternal border"},{id:"V2",name:"V2",type:"precordial",description:"4th intercostal space, left sternal border"},{id:"V3",name:"V3",type:"precordial",description:"Between V2 and V4"},{id:"V4",name:"V4",type:"precordial",description:"5th intercostal space, midclavicular line"},{id:"V5",name:"V5",type:"precordial",description:"5th intercostal space, anterior axillary line"},{id:"V6",name:"V6",type:"precordial",description:"5th intercostal space, midaxillary line"}],l=d=>{switch(d){case"bipolar":return"bg-blue-100 text-blue-800";case"augmented":return"bg-green-100 text-green-800";case"precordial":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},a=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-blue-800",children:"Standard 12-Lead ECG"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"The standard 12-lead ECG provides a comprehensive view of the heart's electrical activity from multiple angles and planes. It consists of:"}),c.jsxs("ul",{className:"space-y-2 text-gray-700",children:[c.jsxs("li",{children:[c.jsx("strong",{children:"Limb Leads (6):"})," I, II, III, aVR, aVL, aVF"]}),c.jsxs("li",{children:[c.jsx("strong",{children:"Precordial Leads (6):"})," V1, V2, V3, V4, V5, V6"]})]})]}),c.jsxs("div",{className:"bg-green-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-green-800",children:"Lead Types"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"inline-block w-4 h-4 bg-blue-500 rounded-full"}),c.jsx("span",{className:"font-medium",children:"Bipolar Leads (I, II, III)"})]}),c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"inline-block w-4 h-4 bg-green-500 rounded-full"}),c.jsx("span",{className:"font-medium",children:"Augmented Leads (aVR, aVL, aVF)"})]}),c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"inline-block w-4 h-4 bg-purple-500 rounded-full"}),c.jsx("span",{className:"font-medium",children:"Precordial Leads (V1-V6)"})]})]})]})]}),c.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Lead Placement Diagram"}),c.jsx("div",{className:"bg-white rounded-lg p-4 shadow-inner",children:c.jsxs("svg",{width:"100%",height:"400",viewBox:"0 0 300 400",className:"mx-auto",children:[c.jsx("path",{d:"M75 50 Q75 40 85 40 L215 40 Q225 40 225 50 L225 120 Q225 130 215 130 L205 130 L205 280 Q205 290 195 290 L105 290 Q95 290 95 280 L95 130 L85 130 Q75 130 75 120 Z",fill:"#f8fafc",stroke:"#e2e8f0",strokeWidth:"2"}),[{id:"V1",x:170,y:110,color:"#8b5cf6"},{id:"V2",x:130,y:110,color:"#8b5cf6"},{id:"V3",x:110,y:140,color:"#8b5cf6"},{id:"V4",x:110,y:170,color:"#8b5cf6"},{id:"V5",x:90,y:170,color:"#8b5cf6"},{id:"V6",x:75,y:170,color:"#8b5cf6"}].map(d=>c.jsxs("g",{children:[c.jsx("circle",{cx:d.x,cy:d.y,r:"12",fill:d.color,opacity:"0.8"}),c.jsx("text",{x:d.x,y:d.y+4,textAnchor:"middle",className:"text-xs font-bold fill-white",children:d.id})]},d.id)),c.jsx("circle",{cx:"75",cy:"80",r:"10",fill:"#3b82f6",opacity:"0.8"}),c.jsx("text",{x:"75",y:"84",textAnchor:"middle",className:"text-xs font-bold fill-white",children:"LA"}),c.jsx("circle",{cx:"225",cy:"80",r:"10",fill:"#3b82f6",opacity:"0.8"}),c.jsx("text",{x:"225",y:"84",textAnchor:"middle",className:"text-xs font-bold fill-white",children:"RA"}),c.jsx("circle",{cx:"225",cy:"320",r:"10",fill:"#3b82f6",opacity:"0.8"}),c.jsx("text",{x:"225",y:"324",textAnchor:"middle",className:"text-xs font-bold fill-white",children:"LL"})]})})]})]})}),u=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-3 gap-6",children:[c.jsx("div",{className:"lg:col-span-1",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:e("lead_selection")}),c.jsx("button",{onClick:()=>r(!s),className:`px-4 py-2 rounded-lg font-medium transition-all ${s?"bg-red-500 text-white hover:bg-red-600":"bg-green-500 text-white hover:bg-green-600"}`,children:s?"Stop":"Start"})]}),c.jsx("div",{className:"space-y-2 max-h-80 overflow-y-auto",children:o.map(d=>c.jsx("button",{onClick:()=>i(d.id),className:`w-full text-left p-3 rounded-lg transition-all ${n===d.id?"bg-blue-50 border-2 border-blue-200":"hover:bg-gray-50 border-2 border-transparent"}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("div",{className:"font-medium text-gray-900",children:d.name}),c.jsx("div",{className:"text-sm text-gray-500",children:d.description})]}),c.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${l(d.type)}`,children:d.type})]})},d.id))})]})}),c.jsx("div",{className:"lg:col-span-2",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:e("waveform_monitor")}),c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(Up,{className:`h-5 w-5 ${s?"text-green-500 animate-pulse":"text-gray-400"}`}),c.jsx("span",{className:"text-sm font-medium text-gray-600",children:n})]})]}),c.jsx("div",{className:"bg-gray-900 rounded-lg p-4 h-80",children:c.jsx(Os,{leadId:n,isMonitoring:s,showGrid:!0,speed:25})})]})})]})});return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&a(),t==="simulation"&&u()]})},G_=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState("clean"),[s,r]=T.useState(!1),o=[{id:"clean",labelKey:"clean_signal",icon:"✨",color:"bg-green-100 text-green-800",description:"Normal ECG signal without artifacts"},{id:"baseline",labelKey:"baseline_wander",icon:"🌊",color:"bg-blue-100 text-blue-800",description:"Low-frequency drift caused by respiration or movement"},{id:"muscle",labelKey:"muscle_tremor",icon:"💪",color:"bg-yellow-100 text-yellow-800",description:"High-frequency noise from muscle contractions"},{id:"power",labelKey:"power_interference",icon:"⚡",color:"bg-red-100 text-red-800",description:"50/60 Hz power line interference"}],l=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"ECG Artifacts"}),c.jsx("p",{className:"text-lg text-gray-600",children:"Understanding and identifying common ECG artifacts is crucial for accurate interpretation"})]}),c.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.slice(1).map(d=>c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow",children:[c.jsxs("div",{className:"text-center mb-4",children:[c.jsx("div",{className:"text-4xl mb-2",children:d.icon}),c.jsx("h3",{className:"text-xl font-semibold text-gray-800",children:e(d.labelKey)})]}),c.jsx("p",{className:"text-gray-600 text-sm mb-4",children:d.description}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Causes:"}),c.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[d.id==="baseline"&&c.jsxs(c.Fragment,{children:[c.jsx("li",{children:"• Patient movement"}),c.jsx("li",{children:"• Respiratory motion"}),c.jsx("li",{children:"• Loose electrodes"})]}),d.id==="muscle"&&c.jsxs(c.Fragment,{children:[c.jsx("li",{children:"• Muscle contractions"}),c.jsx("li",{children:"• Patient shivering"}),c.jsx("li",{children:"• Tension/anxiety"})]}),d.id==="power"&&c.jsxs(c.Fragment,{children:[c.jsx("li",{children:"• Power line interference"}),c.jsx("li",{children:"• Poor grounding"}),c.jsx("li",{children:"• Nearby electrical equipment"})]})]})]}),c.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-blue-800 mb-2",children:"Solutions:"}),c.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[d.id==="baseline"&&c.jsxs(c.Fragment,{children:[c.jsx("li",{children:"• Ensure patient is relaxed"}),c.jsx("li",{children:"• Check electrode adhesion"}),c.jsx("li",{children:"• Use high-pass filtering"})]}),d.id==="muscle"&&c.jsxs(c.Fragment,{children:[c.jsx("li",{children:"• Instruct patient to relax"}),c.jsx("li",{children:"• Warm room temperature"}),c.jsx("li",{children:"• Use EMG filtering"})]}),d.id==="power"&&c.jsxs(c.Fragment,{children:[c.jsx("li",{children:"• Proper grounding"}),c.jsx("li",{children:"• Notch filtering (50/60Hz)"}),c.jsx("li",{children:"• Shield from interference"})]})]})]})]})]},d.id))}),c.jsxs("div",{className:"bg-orange-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:"Clinical Importance"}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-4 text-orange-700",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium mb-2",children:"Diagnostic Accuracy:"}),c.jsx("p",{className:"text-sm",children:"Artifacts can mask or mimic pathological conditions, leading to misdiagnosis."})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium mb-2",children:"Treatment Decisions:"}),c.jsx("p",{className:"text-sm",children:"Clean signals are essential for proper treatment planning and monitoring."})]})]})]})]})}),a=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-4 gap-6",children:[c.jsx("div",{className:"lg:col-span-1",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:e("inject_artifact")}),c.jsx("div",{className:"space-y-3",children:o.map(d=>c.jsx("button",{onClick:()=>i(d.id),className:`w-full p-3 rounded-lg transition-all text-left ${n===d.id?"bg-blue-50 border-2 border-blue-200":"hover:bg-gray-50 border-2 border-transparent"}`,children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"text-2xl",children:d.icon}),c.jsxs("div",{children:[c.jsx("div",{className:"font-medium text-gray-900",children:e(d.labelKey)}),c.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${d.color}`,children:d.id==="clean"?"Normal":"Artifact"})]})]})},d.id))}),c.jsx("div",{className:"mt-6 pt-4 border-t",children:c.jsx("button",{onClick:()=>r(!s),className:`w-full py-3 rounded-lg font-medium transition-all ${s?"bg-red-500 text-white hover:bg-red-600":"bg-green-500 text-white hover:bg-green-600"}`,children:s?"Stop Monitor":"Start Monitor"})})]})}),c.jsx("div",{className:"lg:col-span-3",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"ECG Monitor - Lead II"}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(mo,{className:`h-5 w-5 ${s?"text-green-500 animate-pulse":"text-gray-400"}`}),c.jsx("span",{className:"text-sm font-medium text-gray-600",children:n==="clean"?"Clean Signal":"Artifact Present"})]}),c.jsx("div",{className:"text-sm text-gray-500",children:"Speed: 25 mm/s | Gain: 10 mm/mV"})]})]}),c.jsx("div",{className:"bg-gray-900 rounded-lg p-4 h-96",children:c.jsx(Os,{leadId:"II",isMonitoring:s,artifactType:n,showGrid:!0,speed:25})})]})})]})}),u=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Artifact Analysis Challenge"}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-4",children:[c.jsx("h4",{className:"font-medium text-gray-700",children:"Instructions:"}),c.jsxs("ol",{className:"list-decimal list-inside space-y-2 text-sm text-gray-600",children:[c.jsx("li",{children:"Observe the ECG waveform on the right"}),c.jsx("li",{children:"Identify any artifacts present"}),c.jsx("li",{children:"Click on the suspected artifact type"}),c.jsx("li",{children:"Suggest appropriate filtering solutions"})]}),c.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-blue-800 mb-2",children:"Analysis Tips:"}),c.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[c.jsx("li",{children:"• Look for baseline drift (low frequency)"}),c.jsx("li",{children:"• Check for high-frequency noise"}),c.jsx("li",{children:"• Identify regular interference patterns"}),c.jsx("li",{children:"• Consider clinical context"})]})]})]}),c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-700 mb-3",children:"Sample ECG for Analysis"}),c.jsx("div",{className:"bg-gray-900 rounded-lg p-3 h-48",children:c.jsx(Os,{leadId:"II",isMonitoring:!0,artifactType:"muscle",showGrid:!0,speed:25})})]})]})]}),c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Filtering Solutions"}),c.jsxs("div",{className:"grid md:grid-cols-3 gap-4",children:[c.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg",children:[c.jsx(Ox,{className:"h-8 w-8 text-blue-600 mb-2"}),c.jsx("h4",{className:"font-medium text-blue-800",children:"High-Pass Filter"}),c.jsx("p",{className:"text-sm text-blue-700 mt-2",children:"Removes baseline wander (0.5-1 Hz cutoff)"})]}),c.jsxs("div",{className:"bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg",children:[c.jsx(Mx,{className:"h-8 w-8 text-green-600 mb-2"}),c.jsx("h4",{className:"font-medium text-green-800",children:"Low-Pass Filter"}),c.jsx("p",{className:"text-sm text-green-700 mt-2",children:"Removes muscle noise (35-40 Hz cutoff)"})]}),c.jsxs("div",{className:"bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg",children:[c.jsx(mo,{className:"h-8 w-8 text-red-600 mb-2"}),c.jsx("h4",{className:"font-medium text-red-800",children:"Notch Filter"}),c.jsx("p",{className:"text-sm text-red-700 mt-2",children:"Removes power line interference (50/60 Hz)"})]})]})]})]})});return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&l(),t==="simulation"&&a(),t==="analysis"&&u()]})},X_=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState("normal"),[s,r]=T.useState(!1),[o,l]=T.useState([]),a=[{id:"normal",labelKey:"normal_ecg",icon:"💚",color:"bg-green-100 text-green-800",description:"Normal sinus rhythm with regular QRS complexes"},{id:"mi",labelKey:"myocardial_infarction",icon:"🔴",color:"bg-red-100 text-red-800",description:"ST-elevation myocardial infarction (STEMI)"},{id:"afib",labelKey:"atrial_fibrillation",icon:"🌊",color:"bg-orange-100 text-orange-800",description:"Irregular rhythm with absent P waves"},{id:"bbb",labelKey:"bundle_branch_block",icon:"🔀",color:"bg-purple-100 text-purple-800",description:"Widened QRS complexes with conduction delay"}],u={normal:["Regular R-R intervals","Normal P waves","Normal QRS duration","Normal ST segment"],mi:["ST elevation","Pathological Q waves","T wave inversion","Reciprocal changes"],afib:["Irregular R-R intervals","Absent P waves","Fibrillatory waves","Variable QRS"],bbb:["Wide QRS > 120ms","rsR' pattern","Delayed activation","ST-T changes"]},d=g=>{l(x=>x.includes(g)?x.filter(b=>b!==g):[...x,g])},h=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Pathological ECG Patterns"}),c.jsx("p",{className:"text-lg text-gray-600",children:"Learn to recognize common pathological ECG findings and their clinical significance"})]}),c.jsx("div",{className:"grid md:grid-cols-2 gap-8",children:a.slice(1).map(g=>c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[c.jsx("span",{className:"text-3xl",children:g.icon}),c.jsx("h3",{className:"text-xl font-semibold text-gray-800",children:e(g.labelKey)})]}),c.jsx("p",{className:"text-gray-600 mb-4",children:g.description}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"bg-red-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-red-800 mb-2",children:"Key Features:"}),c.jsx("ul",{className:"text-sm text-red-700 space-y-1",children:u[g.id].map((x,b)=>c.jsxs("li",{children:["• ",x]},b))})]}),c.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-blue-800 mb-2",children:"Clinical Significance:"}),c.jsxs("p",{className:"text-sm text-blue-700",children:[g.id==="mi"&&"Immediate medical emergency requiring reperfusion therapy",g.id==="afib"&&"Increases risk of stroke, requires anticoagulation",g.id==="bbb"&&"May indicate underlying cardiac disease, affects pacing"]})]})]})]},g.id))}),c.jsxs("div",{className:"bg-yellow-50 p-6 rounded-lg",children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[c.jsx(po,{className:"h-6 w-6 text-yellow-600"}),c.jsx("h3",{className:"text-xl font-semibold text-yellow-800",children:"Clinical Considerations"})]}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-4 text-yellow-700",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium mb-2",children:"Immediate Actions:"}),c.jsxs("ul",{className:"text-sm space-y-1",children:[c.jsx("li",{children:"• Assess patient symptoms"}),c.jsx("li",{children:"• Check vital signs"}),c.jsx("li",{children:"• Obtain clinical history"}),c.jsx("li",{children:"• Consider serial ECGs"})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium mb-2",children:"Differential Diagnosis:"}),c.jsxs("ul",{className:"text-sm space-y-1",children:[c.jsx("li",{children:"• Compare with previous ECGs"}),c.jsx("li",{children:"• Correlate with clinical presentation"}),c.jsx("li",{children:"• Consider mimics and artifacts"}),c.jsx("li",{children:"• Consult cardiology if needed"})]})]})]})]})]})}),f=()=>{var g;return c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-4 gap-6",children:[c.jsx("div",{className:"lg:col-span-1",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:e("pathology_selection")}),c.jsx("div",{className:"space-y-3",children:a.map(x=>c.jsx("button",{onClick:()=>i(x.id),className:`w-full p-3 rounded-lg transition-all text-left ${n===x.id?"bg-blue-50 border-2 border-blue-200":"hover:bg-gray-50 border-2 border-transparent"}`,children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"text-2xl",children:x.icon}),c.jsxs("div",{children:[c.jsx("div",{className:"font-medium text-gray-900",children:e(x.labelKey)}),c.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${x.color}`,children:x.id==="normal"?"Normal":"Pathological"})]})]})},x.id))}),c.jsx("div",{className:"mt-6 pt-4 border-t",children:c.jsx("button",{onClick:()=>r(!s),className:`w-full py-3 rounded-lg font-medium transition-all ${s?"bg-red-500 text-white hover:bg-red-600":"bg-green-500 text-white hover:bg-green-600"}`,children:s?"Stop Monitor":"Start Monitor"})})]})}),c.jsx("div",{className:"lg:col-span-3",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"ECG Monitor - Lead II"}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(Sa,{className:`h-5 w-5 ${s?"text-red-500 animate-pulse":"text-gray-400"}`}),c.jsx("span",{className:"text-sm font-medium text-gray-600",children:e(((g=a.find(x=>x.id===n))==null?void 0:g.labelKey)||"normal_ecg")})]}),c.jsx("div",{className:"text-sm text-gray-500",children:"Speed: 25 mm/s | Gain: 10 mm/mV"})]})]}),c.jsx("div",{className:"bg-gray-900 rounded-lg p-4 h-96",children:c.jsx(Os,{leadId:"II",isMonitoring:s,pathologyType:n,showGrid:!0,speed:25})})]})})]})})},p=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"ECG Analysis Challenge"}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-4",children:[c.jsx("h4",{className:"font-medium text-gray-700",children:"Identify Key Features:"}),c.jsx("p",{className:"text-sm text-gray-600",children:"Click on the pathological features you can identify in the ECG waveform."}),c.jsx("div",{className:"space-y-2",children:u[n].map((g,x)=>c.jsx("button",{onClick:()=>d(g),className:`w-full p-3 rounded-lg text-left transition-all ${o.includes(g)?"bg-green-100 border-2 border-green-300":"bg-gray-50 border-2 border-gray-200 hover:bg-gray-100"}`,children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`w-4 h-4 rounded-full ${o.includes(g)?"bg-green-500":"bg-gray-300"}`}),c.jsx("span",{className:"text-sm font-medium",children:g})]})},x))}),c.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-blue-800 mb-2",children:"Analysis Score:"}),c.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[o.length," / ",u[n].length]})]})]}),c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-700 mb-3",children:"ECG for Analysis"}),c.jsx("div",{className:"bg-gray-900 rounded-lg p-3 h-64",children:c.jsx(Os,{leadId:"II",isMonitoring:!0,pathologyType:n,showGrid:!0,speed:25})})]})]})]}),c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Clinical Decision Making"}),c.jsxs("div",{className:"grid md:grid-cols-3 gap-4",children:[c.jsxs("div",{className:"bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg",children:[c.jsx(po,{className:"h-8 w-8 text-red-600 mb-2"}),c.jsx("h4",{className:"font-medium text-red-800",children:"Emergency"}),c.jsx("p",{className:"text-sm text-red-700 mt-2",children:"STEMI: Immediate cath lab activation"})]}),c.jsxs("div",{className:"bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-lg",children:[c.jsx(Sa,{className:"h-8 w-8 text-orange-600 mb-2"}),c.jsx("h4",{className:"font-medium text-orange-800",children:"Urgent"}),c.jsx("p",{className:"text-sm text-orange-700 mt-2",children:"Atrial Fib: Anticoagulation assessment"})]}),c.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg",children:[c.jsx(Up,{className:"h-8 w-8 text-purple-600 mb-2"}),c.jsx("h4",{className:"font-medium text-purple-800",children:"Monitoring"}),c.jsx("p",{className:"text-sm text-purple-700 mt-2",children:"BBB: Cardiac function evaluation"})]})]})]})]})});return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&h(),t==="simulation"&&f(),t==="analysis"&&p()]})};Ye.register(...qc);const Gm=({channel:t,isMonitoring:e,dominantWave:n,showGrid:i=!0,speed:s=30})=>{const r=T.useRef(null),o=T.useRef(null);return T.useEffect(()=>{if(!r.current)return;const l=r.current.getContext("2d");if(!l)return;o.current&&o.current.destroy();const a=()=>{const g=[];for(let m=0;m<3*256;m++){const y=m/256;let v=0;n==="alpha"?(v=50*Math.sin(y*2*Math.PI*10),v+=20*Math.sin(y*2*Math.PI*8.5),v+=10*Math.sin(y*2*Math.PI*11.5)):n==="beta"?(v=30*Math.sin(y*2*Math.PI*20),v+=25*Math.sin(y*2*Math.PI*15),v+=15*Math.sin(y*2*Math.PI*25)):n==="theta"?(v=60*Math.sin(y*2*Math.PI*6),v+=30*Math.sin(y*2*Math.PI*4.5),v+=20*Math.sin(y*2*Math.PI*7)):n==="delta"&&(v=80*Math.sin(y*2*Math.PI*2),v+=40*Math.sin(y*2*Math.PI*1.5),v+=20*Math.sin(y*2*Math.PI*.8)),v+=5*(Math.random()-.5),g.push({x:m,y:v})}return g},u=a(),d={type:"line",data:{datasets:[{label:`Channel ${t}`,data:u,borderColor:"#00ff00",backgroundColor:"transparent",borderWidth:1.5,pointRadius:0,tension:0}]},options:{responsive:!0,maintainAspectRatio:!1,animation:{duration:0},scales:{x:{type:"linear",display:i,grid:{display:i,color:"#444444"},ticks:{color:"#888888"}},y:{display:i,grid:{display:i,color:"#444444"},ticks:{color:"#888888"},min:-150,max:150}},plugins:{legend:{display:!1}}}};o.current=new Ye(l,d);let h,f=0;const p=()=>{if(!e||!o.current)return;f+=s/10;const g=a();o.current.data.datasets[0].data=g.map(x=>({x:x.x-f,y:x.y})),o.current.update("none"),h=requestAnimationFrame(p)};return e&&p(),()=>{h&&cancelAnimationFrame(h),o.current&&o.current.destroy()}},[t,e,n,i,s]),c.jsx("div",{className:"w-full h-full",children:c.jsx("canvas",{ref:r,className:"w-full h-full"})})},Q_=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState("alpha"),[s,r]=T.useState("relaxed"),[o,l]=T.useState(!1),a=[{id:"alpha",labelKey:"alpha_waves",frequency:"8-12 Hz",color:"bg-blue-100 text-blue-800",description:"Relaxed, conscious state",icon:"🧘"},{id:"beta",labelKey:"beta_waves",frequency:"13-30 Hz",color:"bg-green-100 text-green-800",description:"Active, focused thinking",icon:"🎯"},{id:"theta",labelKey:"theta_waves",frequency:"4-7 Hz",color:"bg-yellow-100 text-yellow-800",description:"Drowsy, meditative state",icon:"😴"},{id:"delta",labelKey:"delta_waves",frequency:"0.5-3 Hz",color:"bg-purple-100 text-purple-800",description:"Deep sleep, unconscious",icon:"💤"}],u=[{id:"relaxed",labelKey:"relaxed",dominantWave:"alpha"},{id:"focused",labelKey:"focused",dominantWave:"beta"},{id:"drowsy",labelKey:"drowsy",dominantWave:"theta"},{id:"deep_sleep",labelKey:"deep_sleep",dominantWave:"delta"}],d=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"EEG Fundamentals"}),c.jsx("p",{className:"text-lg text-gray-600",children:"Understanding brain electrical activity and the 10-20 electrode system"})]}),c.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-blue-800",children:"What is EEG?"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"Electroencephalography (EEG) records electrical activity of the brain through electrodes placed on the scalp. It measures synchronized electrical activity of neurons in the cortex."}),c.jsx("div",{className:"flex justify-center",children:c.jsx(jx,{className:"h-16 w-16 text-blue-500 animate-pulse"})})]}),c.jsxs("div",{className:"bg-green-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-green-800",children:"10-20 System"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"The international 10-20 system is a standardized method for electrode placement, ensuring reproducible measurements across different laboratories and studies."}),c.jsxs("ul",{className:"text-sm text-green-700 space-y-1",children:[c.jsx("li",{children:"• 10% and 20% of skull dimensions"}),c.jsx("li",{children:"• Consistent electrode naming"}),c.jsx("li",{children:"• Standardized montages"}),c.jsx("li",{children:"• International compatibility"})]})]})]}),c.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Electrode Placement"}),c.jsx("div",{className:"bg-white rounded-lg p-4",children:c.jsxs("svg",{width:"100%",height:"400",viewBox:"0 0 300 400",className:"mx-auto",children:[c.jsx("ellipse",{cx:"150",cy:"200",rx:"120",ry:"150",fill:"none",stroke:"#e2e8f0",strokeWidth:"2"}),[{id:"Fp1",x:120,y:80,region:"frontal"},{id:"Fp2",x:180,y:80,region:"frontal"},{id:"F7",x:80,y:140,region:"frontal"},{id:"F3",x:120,y:140,region:"frontal"},{id:"Fz",x:150,y:140,region:"frontal"},{id:"F4",x:180,y:140,region:"frontal"},{id:"F8",x:220,y:140,region:"frontal"},{id:"T7",x:70,y:200,region:"temporal"},{id:"C3",x:120,y:200,region:"central"},{id:"Cz",x:150,y:200,region:"central"},{id:"C4",x:180,y:200,region:"central"},{id:"T8",x:230,y:200,region:"temporal"},{id:"P7",x:80,y:260,region:"parietal"},{id:"P3",x:120,y:260,region:"parietal"},{id:"Pz",x:150,y:260,region:"parietal"},{id:"P4",x:180,y:260,region:"parietal"},{id:"P8",x:220,y:260,region:"parietal"},{id:"O1",x:120,y:320,region:"occipital"},{id:"O2",x:180,y:320,region:"occipital"}].map(f=>c.jsxs("g",{children:[c.jsx("circle",{cx:f.x,cy:f.y,r:"8",fill:f.region==="frontal"?"#3b82f6":f.region==="temporal"?"#10b981":f.region==="central"?"#f59e0b":f.region==="parietal"?"#ef4444":"#8b5cf6",opacity:"0.8"}),c.jsx("text",{x:f.x,y:f.y+3,textAnchor:"middle",className:"text-xs font-bold fill-white",children:f.id})]},f.id)),c.jsx("path",{d:"M150 50 L145 65 L155 65 Z",fill:"#d1d5db"}),c.jsx("circle",{cx:"50",cy:"200",r:"15",fill:"none",stroke:"#d1d5db",strokeWidth:"2"}),c.jsx("circle",{cx:"250",cy:"200",r:"15",fill:"none",stroke:"#d1d5db",strokeWidth:"2"})]})})]})]}),c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:e("brainwave_types")}),c.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-4",children:a.map(f=>c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg text-center",children:[c.jsx("div",{className:"text-3xl mb-2",children:f.icon}),c.jsx("h4",{className:"font-medium text-gray-800",children:e(f.labelKey)}),c.jsx("p",{className:"text-sm text-gray-600 mt-1",children:f.frequency}),c.jsx("p",{className:"text-xs text-gray-500 mt-2",children:f.description})]},f.id))})]})]})}),h=()=>{var f;return c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-4 gap-6",children:[c.jsx("div",{className:"lg:col-span-1",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:e("mental_state")}),c.jsx("div",{className:"space-y-3",children:u.map(p=>{var g,x;return c.jsx("button",{onClick:()=>{r(p.id),i(p.dominantWave)},className:`w-full p-3 rounded-lg transition-all text-left ${s===p.id?"bg-blue-50 border-2 border-blue-200":"hover:bg-gray-50 border-2 border-transparent"}`,children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"text-2xl",children:(g=a.find(b=>b.id===p.dominantWave))==null?void 0:g.icon}),c.jsxs("div",{children:[c.jsx("div",{className:"font-medium text-gray-900",children:e(p.labelKey)}),c.jsxs("div",{className:"text-sm text-gray-500",children:["Dominant: ",(x=a.find(b=>b.id===p.dominantWave))==null?void 0:x.frequency]})]})]})},p.id)})}),c.jsx("div",{className:"mt-6 pt-4 border-t",children:c.jsx("button",{onClick:()=>l(!o),className:`w-full py-3 rounded-lg font-medium transition-all ${o?"bg-red-500 text-white hover:bg-red-600":"bg-green-500 text-white hover:bg-green-600"}`,children:o?"Stop Recording":"Start Recording"})})]})}),c.jsx("div",{className:"lg:col-span-3",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"EEG Monitor - Channel O1"}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(mo,{className:`h-5 w-5 ${o?"text-green-500 animate-pulse":"text-gray-400"}`}),c.jsx("span",{className:"text-sm font-medium text-gray-600",children:e(((f=u.find(p=>p.id===s))==null?void 0:f.labelKey)||"relaxed")})]}),c.jsx("div",{className:"text-sm text-gray-500",children:"Speed: 30 mm/s | Sensitivity: 70 μV/mm"})]})]}),c.jsx("div",{className:"bg-gray-900 rounded-lg p-4 h-96",children:c.jsx(Gm,{channel:"O1",isMonitoring:o,dominantWave:n,showGrid:!0,speed:30})})]})})]})})};return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&d(),t==="simulation"&&h()]})};Ye.register(...qc);const q_=({isVisible:t,dominantFrequency:e})=>{const n=T.useRef(null),i=T.useRef(null);return T.useEffect(()=>{if(!n.current||!t)return;const s=n.current.getContext("2d");if(!s)return;i.current&&i.current.destroy();const l={type:"line",data:{datasets:[{label:"Power Spectral Density",data:(()=>{const a=[];for(let u=0;u<=50;u+=.5){let d=0;u>=e-2&&u<=e+2?d=100*Math.exp(-Math.pow(u-e,2)/2):d=10*Math.exp(-u/20)+5*Math.random(),a.push({x:u,y:d})}return a})(),borderColor:"#3b82f6",backgroundColor:"rgba(59, 130, 246, 0.1)",borderWidth:2,pointRadius:0,fill:!0,tension:.2}]},options:{responsive:!0,maintainAspectRatio:!1,animation:{duration:1e3},scales:{x:{type:"linear",display:!0,title:{display:!0,text:"Frequency (Hz)",color:"#374151"},grid:{display:!0,color:"#e5e7eb"},ticks:{color:"#6b7280"}},y:{display:!0,title:{display:!0,text:"Power (μV²/Hz)",color:"#374151"},grid:{display:!0,color:"#e5e7eb"},ticks:{color:"#6b7280"},min:0}},plugins:{legend:{display:!1}}}};return i.current=new Ye(s,l),()=>{i.current&&i.current.destroy()}},[t,e]),c.jsx("div",{className:"w-full h-full",children:c.jsx("canvas",{ref:n,className:"w-full h-full"})})},Z_=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState("O1"),[s,r]=T.useState(!1),[o,l]=T.useState(!1),[a,u]=T.useState(0),d=[{id:"Fp1",name:"Fp1 (Frontal)",region:"frontal"},{id:"Fp2",name:"Fp2 (Frontal)",region:"frontal"},{id:"F3",name:"F3 (Frontal)",region:"frontal"},{id:"F4",name:"F4 (Frontal)",region:"frontal"},{id:"C3",name:"C3 (Central)",region:"central"},{id:"C4",name:"C4 (Central)",region:"central"},{id:"P3",name:"P3 (Parietal)",region:"parietal"},{id:"P4",name:"P4 (Parietal)",region:"parietal"},{id:"O1",name:"O1 (Occipital)",region:"occipital"},{id:"O2",name:"O2 (Occipital)",region:"occipital"}],h=()=>{l(!0);const x=[8,10,12,15,20,25,30],b=x.map(y=>Math.random()*100),m=b.indexOf(Math.max(...b));u(x[m])},f=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Advanced EEG Analysis"}),c.jsx("p",{className:"text-lg text-gray-600",children:"Learn advanced signal processing techniques for EEG analysis"})]}),c.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-blue-800",children:"Power Spectral Density (PSD)"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"PSD analysis reveals the frequency content of EEG signals, helping identify dominant brainwave patterns and abnormalities."}),c.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[c.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[c.jsx(Kp,{className:"h-5 w-5 text-blue-600"}),c.jsx("span",{className:"font-medium",children:"Key Applications:"})]}),c.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[c.jsx("li",{children:"• Sleep stage classification"}),c.jsx("li",{children:"• Epilepsy detection"}),c.jsx("li",{children:"• Mental state monitoring"}),c.jsx("li",{children:"• Brain-computer interfaces"})]})]})]}),c.jsxs("div",{className:"bg-green-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-green-800",children:"Event-Related Potentials (ERPs)"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"ERPs are voltage fluctuations that are time-locked to specific events or stimuli, providing insights into cognitive processes."}),c.jsx("div",{className:"bg-white p-4 rounded-lg",children:c.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium text-green-700",children:"P300 Wave"}),c.jsx("p",{className:"text-gray-600",children:"Attention & decision making"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium text-green-700",children:"N400 Wave"}),c.jsx("p",{className:"text-gray-600",children:"Language processing"})]})]})})]})]}),c.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Frequency Bands"}),c.jsx("div",{className:"space-y-4",children:[{name:"Delta",range:"0.5-4 Hz",color:"bg-purple-500",description:"Deep sleep, unconscious"},{name:"Theta",range:"4-8 Hz",color:"bg-blue-500",description:"Drowsiness, meditation"},{name:"Alpha",range:"8-13 Hz",color:"bg-green-500",description:"Relaxed wakefulness"},{name:"Beta",range:"13-30 Hz",color:"bg-yellow-500",description:"Active concentration"},{name:"Gamma",range:"30-100 Hz",color:"bg-red-500",description:"High-level cognition"}].map(x=>c.jsxs("div",{className:"flex items-center space-x-4 p-3 bg-white rounded-lg",children:[c.jsx("div",{className:`w-4 h-4 rounded-full ${x.color}`}),c.jsxs("div",{className:"flex-1",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("span",{className:"font-medium",children:x.name}),c.jsx("span",{className:"text-sm text-gray-500",children:x.range})]}),c.jsx("p",{className:"text-sm text-gray-600",children:x.description})]})]},x.name))})]})]})]})}),p=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-4 gap-6",children:[c.jsx("div",{className:"lg:col-span-1",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Channel Selection"}),c.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:d.map(x=>c.jsxs("button",{onClick:()=>i(x.id),className:`w-full p-3 rounded-lg transition-all text-left ${n===x.id?"bg-blue-50 border-2 border-blue-200":"hover:bg-gray-50 border-2 border-transparent"}`,children:[c.jsx("div",{className:"font-medium text-gray-900",children:x.name}),c.jsx("div",{className:"text-sm text-gray-500 capitalize",children:x.region})]},x.id))}),c.jsxs("div",{className:"mt-6 pt-4 border-t space-y-3",children:[c.jsx("button",{onClick:()=>r(!s),className:`w-full py-3 rounded-lg font-medium transition-all ${s?"bg-red-500 text-white hover:bg-red-600":"bg-green-500 text-white hover:bg-green-600"}`,children:s?"Stop Recording":"Start Recording"}),c.jsx("button",{onClick:h,disabled:!s,className:`w-full py-3 rounded-lg font-medium transition-all ${s?"bg-blue-500 text-white hover:bg-blue-600":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:c.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[c.jsx(Nx,{className:"h-4 w-4"}),c.jsx("span",{children:e("calculate_psd")})]})})]})]})}),c.jsx("div",{className:"lg:col-span-3",children:c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:["EEG Signal - ",n]}),c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(mo,{className:`h-5 w-5 ${s?"text-green-500 animate-pulse":"text-gray-400"}`}),c.jsx("span",{className:"text-sm text-gray-600",children:"70 μV/mm"})]})]}),c.jsx("div",{className:"bg-gray-900 rounded-lg p-4 h-64",children:c.jsx(Gm,{channel:n,isMonitoring:s,dominantWave:"alpha",showGrid:!0,speed:30})})]}),o&&c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:e("frequency_analysis")}),c.jsxs("div",{className:"text-sm text-gray-600",children:[e("dominant_frequency"),": ",a," Hz"]})]}),c.jsx("div",{className:"bg-gray-50 rounded-lg p-4 h-64",children:c.jsx(q_,{isVisible:o,dominantFrequency:a})})]})]})})]})}),g=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsx("div",{className:"space-y-6",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"EEG Analysis Workshop"}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-4",children:[c.jsx("h4",{className:"font-medium text-gray-700",children:"Analysis Steps:"}),c.jsxs("ol",{className:"list-decimal list-inside space-y-2 text-sm text-gray-600",children:[c.jsx("li",{children:"Record EEG signal from selected channel"}),c.jsx("li",{children:"Apply appropriate filtering (0.5-70 Hz)"}),c.jsx("li",{children:"Calculate Power Spectral Density"}),c.jsx("li",{children:"Identify dominant frequency bands"}),c.jsx("li",{children:"Interpret results in clinical context"})]}),c.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-blue-800 mb-2",children:"Clinical Applications:"}),c.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[c.jsx("li",{children:"• Epilepsy monitoring"}),c.jsx("li",{children:"• Sleep disorder diagnosis"}),c.jsx("li",{children:"• Brain-computer interfaces"}),c.jsx("li",{children:"• Cognitive assessment"})]})]})]}),c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-700 mb-3",children:"Real-time Analysis"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("div",{className:"flex items-center justify-between p-2 bg-white rounded",children:[c.jsx("span",{className:"text-sm",children:"Alpha Power"}),c.jsx("span",{className:"font-medium",children:"45.2 μV²"})]}),c.jsxs("div",{className:"flex items-center justify-between p-2 bg-white rounded",children:[c.jsx("span",{className:"text-sm",children:"Beta Power"}),c.jsx("span",{className:"font-medium",children:"23.1 μV²"})]}),c.jsxs("div",{className:"flex items-center justify-between p-2 bg-white rounded",children:[c.jsx("span",{className:"text-sm",children:"Theta Power"}),c.jsx("span",{className:"font-medium",children:"18.7 μV²"})]})]})]})]})]})})});return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&f(),t==="simulation"&&p(),t==="analysis"&&g()]})},J_=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState(0),[s,r]=T.useState(0),[o,l]=T.useState(0),[a,u]=T.useState(!1),[d,h]=T.useState(!1),[f,p]=T.useState(!1),[g,x]=T.useState(!1),b=120,m=80;T.useEffect(()=>{n<=b&&n>m&&f?u(!0):u(!1),n<=b&&s===0&&f&&r(Math.round(n)),n<=m&&o===0&&s>0&&f&&(l(Math.round(n)),x(!0))},[n,f,s,o]);const y=()=>{h(!0),p(!1);const k=setInterval(()=>{i(S=>S>=180?(clearInterval(k),h(!1),180):S+5)},200)},v=()=>{p(!0),h(!1);const k=setInterval(()=>{i(S=>S<=0?(clearInterval(k),p(!1),0):S-2)},200)},_=()=>{i(0),r(0),l(0),u(!1),h(!1),p(!1),x(!1)},w=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Blood Pressure Measurement"}),c.jsx("p",{className:"text-lg text-gray-600",children:"Learn the principles of non-invasive blood pressure measurement using oscillometric and auscultatory methods"})]}),c.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-red-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-red-800",children:"Auscultatory Method"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"The traditional method using a stethoscope to listen for Korotkoff sounds as the cuff pressure is gradually released."}),c.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-red-700 mb-2",children:"Korotkoff Sounds:"}),c.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[c.jsxs("li",{children:["• ",c.jsx("strong",{children:"Phase I:"})," First appearance (Systolic)"]}),c.jsxs("li",{children:["• ",c.jsx("strong",{children:"Phase II:"})," Murmur sounds"]}),c.jsxs("li",{children:["• ",c.jsx("strong",{children:"Phase III:"})," Crisp sounds"]}),c.jsxs("li",{children:["• ",c.jsx("strong",{children:"Phase IV:"})," Muffled sounds"]}),c.jsxs("li",{children:["• ",c.jsx("strong",{children:"Phase V:"})," Disappearance (Diastolic)"]})]})]})]}),c.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-blue-800",children:"Oscillometric Method"}),c.jsx("p",{className:"text-gray-700 mb-4",children:"Modern automated method that detects pressure oscillations in the cuff caused by arterial pulsations."}),c.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-blue-700 mb-2",children:"Key Features:"}),c.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[c.jsx("li",{children:"• Automated measurement"}),c.jsx("li",{children:"• No stethoscope required"}),c.jsx("li",{children:"• Digital display"}),c.jsx("li",{children:"• Suitable for noisy environments"})]})]})]})]}),c.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[c.jsx("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Measurement Process"}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-white rounded-lg",children:[c.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold",children:"1"}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium",children:"Cuff Inflation"}),c.jsx("p",{className:"text-sm text-gray-600",children:"Inflate to 20-30 mmHg above expected systolic"})]})]}),c.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-white rounded-lg",children:[c.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold",children:"2"}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium",children:"Gradual Deflation"}),c.jsx("p",{className:"text-sm text-gray-600",children:"Release pressure at 2-3 mmHg per second"})]})]}),c.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-white rounded-lg",children:[c.jsx("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold",children:"3"}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium",children:"Sound Detection"}),c.jsx("p",{className:"text-sm text-gray-600",children:"Listen for first and last Korotkoff sounds"})]})]})]}),c.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-yellow-800 mb-2",children:"Normal Values:"}),c.jsxs("div",{className:"text-sm text-yellow-700 space-y-1",children:[c.jsx("div",{children:"Systolic: 90-120 mmHg"}),c.jsx("div",{children:"Diastolic: 60-80 mmHg"}),c.jsx("div",{children:"Pulse Pressure: 30-50 mmHg"})]})]})]})]})]})}),j=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Blood Pressure Measurement"}),c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-700 mb-3",children:e("bp_cuff_pressure")}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"w-full bg-gray-200 rounded-full h-4",children:c.jsx("div",{className:"bg-red-500 h-4 rounded-full transition-all duration-300",style:{width:`${n/200*100}%`}})}),c.jsxs("div",{className:"text-center mt-2 text-2xl font-bold text-gray-800",children:[n," mmHg"]})]})]}),c.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[c.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[c.jsx("div",{className:"text-sm text-blue-600 mb-1",children:e("systolic_pressure")}),c.jsx("div",{className:"text-2xl font-bold text-blue-800",children:s||"--"}),c.jsx("div",{className:"text-xs text-blue-600",children:"mmHg"})]}),c.jsxs("div",{className:"bg-green-50 p-4 rounded-lg text-center",children:[c.jsx("div",{className:"text-sm text-green-600 mb-1",children:e("diastolic_pressure")}),c.jsx("div",{className:"text-2xl font-bold text-green-800",children:o||"--"}),c.jsx("div",{className:"text-xs text-green-600",children:"mmHg"})]})]}),c.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-purple-700 mb-2",children:e("korotkoff_sounds")}),c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Ax,{className:`h-8 w-8 ${a?"text-red-500 animate-pulse":"text-gray-400"}`}),c.jsxs("div",{className:"text-sm",children:[c.jsx("div",{className:`font-medium ${a?"text-red-600":"text-gray-500"}`,children:e(a?"sounds_present":"sounds_absent")}),c.jsx("div",{className:"text-xs text-gray-500",children:a?"Phase I-V audible":"No sounds detected"})]})]})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:y,disabled:d||f,className:`flex-1 py-3 rounded-lg font-medium transition-all ${d||f?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-red-500 text-white hover:bg-red-600"}`,children:c.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[c.jsx(Xp,{className:"h-4 w-4"}),c.jsx("span",{children:e("inflate_cuff")})]})}),c.jsx("button",{onClick:v,disabled:d||f||n===0,className:`flex-1 py-3 rounded-lg font-medium transition-all ${d||f||n===0?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-500 text-white hover:bg-blue-600"}`,children:c.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[c.jsx(Gp,{className:"h-4 w-4"}),c.jsx("span",{children:e("deflate_cuff")})]})})]}),c.jsx("button",{onClick:_,className:"w-full py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors",children:"Reset Measurement"}),g&&c.jsx("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Sa,{className:"h-6 w-6 text-green-600"}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium text-green-800",children:"Measurement Complete!"}),c.jsxs("p",{className:"text-sm text-green-700",children:["BP: ",s,"/",o," mmHg"]})]})]})})]})]}),c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Visual Representation"}),c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-700 mb-3",children:"Brachial Artery"}),c.jsx("div",{className:"relative bg-white rounded-lg p-4 h-32",children:c.jsxs("svg",{width:"100%",height:"100%",viewBox:"0 0 400 100",children:[c.jsx("rect",{x:"50",y:"30",width:"300",height:"40",fill:"#ff6b6b",opacity:"0.3",rx:"20"}),c.jsx("rect",{x:"50",y:"35",width:n>b?"0":"290",height:"30",fill:"#ff6b6b",rx:"15",className:"transition-all duration-300"}),c.jsx("rect",{x:"150",y:"20",width:"100",height:"60",fill:"#4f46e5",opacity:n>0?.4:.1,rx:"5"}),c.jsxs("text",{x:"200",y:"55",textAnchor:"middle",className:"text-sm font-medium",children:["Cuff: ",n," mmHg"]})]})})]}),c.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-blue-700 mb-2",children:"Pressure Waveform"}),c.jsx("div",{className:"bg-white p-3 rounded",children:c.jsxs("svg",{width:"100%",height:"100",viewBox:"0 0 400 100",children:[c.jsx("path",{d:`M 0 ${100-n/2} Q 100 ${100-b/2} 200 ${100-m/2} Q 300 ${100-m/2} 400 90`,fill:"none",stroke:"#3b82f6",strokeWidth:"2"}),c.jsx("circle",{cx:"200",cy:100-n/2,r:"4",fill:"#ef4444"}),c.jsx("line",{x1:"0",y1:100-b/2,x2:"400",y2:100-b/2,stroke:"#ef4444",strokeDasharray:"5,5",opacity:"0.5"}),c.jsx("line",{x1:"0",y1:100-m/2,x2:"400",y2:100-m/2,stroke:"#10b981",strokeDasharray:"5,5",opacity:"0.5"})]})})]})]})]})]})});return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&w(),t==="simulation"&&j()]})},ew=({activeTab:t})=>{const{t:e}=Je(),[n,i]=T.useState("normal"),[s,r]=T.useState(null),[o,l]=T.useState(""),a=[{id:"normal",labelKey:"normal_patient",systolic:118,diastolic:78,icon:"💚",color:"bg-green-100 text-green-800"},{id:"hypertensive",labelKey:"hypertensive_patient",systolic:165,diastolic:95,icon:"🔴",color:"bg-red-100 text-red-800"},{id:"hypotensive",labelKey:"hypotensive_patient",systolic:85,diastolic:55,icon:"🔵",color:"bg-blue-100 text-blue-800"}],u=(g,x)=>g<90||x<60?"low_bp":g>=140||x>=90?"high_bp":"normal_bp",d=()=>{const g=a.find(x=>x.id===n);if(g){const x=g.systolic+Math.round((Math.random()-.5)*10),b=g.diastolic+Math.round((Math.random()-.5)*8);r({systolic:x,diastolic:b}),l(u(x,b))}},h=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Hypertension & Hypotension"}),c.jsx("p",{className:"text-lg text-gray-600",children:"Understanding blood pressure abnormalities and their clinical significance"})]}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[c.jsxs("div",{className:"bg-red-50 p-6 rounded-lg",children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[c.jsx(Xp,{className:"h-8 w-8 text-red-600"}),c.jsx("h3",{className:"text-xl font-semibold text-red-800",children:"Hypertension"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsx("p",{className:"text-gray-700",children:"High blood pressure is a major risk factor for cardiovascular disease, stroke, and kidney disease."}),c.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-red-700 mb-2",children:"Classification (AHA/ACC 2017):"}),c.jsxs("div",{className:"space-y-2 text-sm",children:[c.jsxs("div",{className:"flex justify-between",children:[c.jsx("span",{children:"Normal:"}),c.jsx("span",{children:"< 120/80 mmHg"})]}),c.jsxs("div",{className:"flex justify-between",children:[c.jsx("span",{children:"Elevated:"}),c.jsx("span",{children:"120-129/<80 mmHg"})]}),c.jsxs("div",{className:"flex justify-between",children:[c.jsx("span",{children:"Stage 1:"}),c.jsx("span",{children:"130-139/80-89 mmHg"})]}),c.jsxs("div",{className:"flex justify-between",children:[c.jsx("span",{children:"Stage 2:"}),c.jsx("span",{children:"≥140/90 mmHg"})]}),c.jsxs("div",{className:"flex justify-between",children:[c.jsx("span",{children:"Crisis:"}),c.jsx("span",{children:">180/120 mmHg"})]})]})]}),c.jsxs("div",{className:"bg-red-100 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-red-800 mb-2",children:"Risk Factors:"}),c.jsxs("ul",{className:"text-sm text-red-700 space-y-1",children:[c.jsx("li",{children:"• Age and family history"}),c.jsx("li",{children:"• Obesity and physical inactivity"}),c.jsx("li",{children:"• High sodium intake"}),c.jsx("li",{children:"• Excessive alcohol consumption"}),c.jsx("li",{children:"• Stress and sleep disorders"})]})]})]})]}),c.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg",children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[c.jsx(Gp,{className:"h-8 w-8 text-blue-600"}),c.jsx("h3",{className:"text-xl font-semibold text-blue-800",children:"Hypotension"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsx("p",{className:"text-gray-700",children:"Low blood pressure can cause inadequate tissue perfusion and organ dysfunction."}),c.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-blue-700 mb-2",children:"Types:"}),c.jsxs("div",{className:"space-y-2 text-sm",children:[c.jsxs("div",{children:[c.jsx("span",{className:"font-medium",children:"Orthostatic:"}),c.jsx("span",{className:"ml-2",children:"Drop on standing"})]}),c.jsxs("div",{children:[c.jsx("span",{className:"font-medium",children:"Postprandial:"}),c.jsx("span",{className:"ml-2",children:"After eating"})]}),c.jsxs("div",{children:[c.jsx("span",{className:"font-medium",children:"Neurally mediated:"}),c.jsx("span",{className:"ml-2",children:"Vasovagal response"})]}),c.jsxs("div",{children:[c.jsx("span",{className:"font-medium",children:"Severe:"}),c.jsx("span",{className:"ml-2",children:"Shock state"})]})]})]}),c.jsxs("div",{className:"bg-blue-100 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-blue-800 mb-2",children:"Symptoms:"}),c.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[c.jsx("li",{children:"• Dizziness and lightheadedness"}),c.jsx("li",{children:"• Fatigue and weakness"}),c.jsx("li",{children:"• Nausea and confusion"}),c.jsx("li",{children:"• Blurred vision"}),c.jsx("li",{children:"• Fainting (syncope)"})]})]})]})]})]}),c.jsxs("div",{className:"bg-yellow-50 p-6 rounded-lg",children:[c.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[c.jsx(po,{className:"h-6 w-6 text-yellow-600"}),c.jsx("h3",{className:"text-xl font-semibold text-yellow-800",children:"Clinical Management"})]}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium text-yellow-700 mb-2",children:"Hypertension Management:"}),c.jsxs("ul",{className:"text-sm text-yellow-700 space-y-1",children:[c.jsx("li",{children:"• Lifestyle modifications (diet, exercise)"}),c.jsx("li",{children:"• Antihypertensive medications"}),c.jsx("li",{children:"• Regular monitoring"}),c.jsx("li",{children:"• Cardiovascular risk assessment"})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-medium text-yellow-700 mb-2",children:"Hypotension Management:"}),c.jsxs("ul",{className:"text-sm text-yellow-700 space-y-1",children:[c.jsx("li",{children:"• Identify and treat underlying causes"}),c.jsx("li",{children:"• Fluid and electrolyte balance"}),c.jsx("li",{children:"• Compression stockings"}),c.jsx("li",{children:"• Medications if necessary"})]})]})]})]})]})}),f=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"grid lg:grid-cols-3 gap-6",children:[c.jsx("div",{className:"lg:col-span-1",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:e("patient_profile")}),c.jsx("div",{className:"space-y-3",children:a.map(g=>c.jsx("button",{onClick:()=>{i(g.id),r(null),l("")},className:`w-full p-4 rounded-lg transition-all text-left ${n===g.id?"bg-blue-50 border-2 border-blue-200":"hover:bg-gray-50 border-2 border-transparent"}`,children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"text-2xl",children:g.icon}),c.jsxs("div",{children:[c.jsx("div",{className:"font-medium text-gray-900",children:e(g.labelKey)}),c.jsxs("div",{className:"text-sm text-gray-500",children:["Expected: ",g.systolic,"/",g.diastolic," mmHg"]})]})]})},g.id))}),c.jsx("div",{className:"mt-6 pt-4 border-t",children:c.jsx("button",{onClick:d,className:"w-full py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium",children:"Measure Blood Pressure"})})]})}),c.jsx("div",{className:"lg:col-span-2",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-6",children:"Blood Pressure Analysis"}),s?c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[c.jsxs("div",{className:"bg-red-50 p-4 rounded-lg text-center",children:[c.jsx("div",{className:"text-sm text-red-600 mb-1",children:"Systolic"}),c.jsx("div",{className:"text-3xl font-bold text-red-800",children:s.systolic}),c.jsx("div",{className:"text-xs text-red-600",children:"mmHg"})]}),c.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[c.jsx("div",{className:"text-sm text-blue-600 mb-1",children:"Diastolic"}),c.jsx("div",{className:"text-3xl font-bold text-blue-800",children:s.diastolic}),c.jsx("div",{className:"text-xs text-blue-600",children:"mmHg"})]})]}),c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-700 mb-2",children:e("classification")}),c.jsxs("div",{className:`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${o==="normal_bp"?"bg-green-100 text-green-800":o==="high_bp"?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:[o==="normal_bp"?c.jsx(Gn,{className:"h-5 w-5"}):c.jsx(po,{className:"h-5 w-5"}),c.jsx("span",{className:"font-medium",children:e(o)})]})]}),c.jsxs("div",{className:"bg-white border-2 border-gray-200 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-gray-700 mb-3",children:"Visual Pressure Scale"}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"h-8 bg-gradient-to-r from-blue-500 via-green-500 to-red-500 rounded-full"}),c.jsx("div",{className:"absolute top-0 w-2 h-8 bg-black rounded-full transform -translate-x-1",style:{left:`${Math.min(Math.max((s.systolic-60)/140*100,0),100)}%`}}),c.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[c.jsx("span",{children:"60"}),c.jsx("span",{children:"120"}),c.jsx("span",{children:"180"}),c.jsx("span",{children:"200"})]})]})]}),c.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-medium text-yellow-800 mb-2",children:"Clinical Recommendations:"}),c.jsxs("div",{className:"text-sm text-yellow-700",children:[o==="normal_bp"&&c.jsxs("ul",{className:"space-y-1",children:[c.jsx("li",{children:"• Continue healthy lifestyle"}),c.jsx("li",{children:"• Regular monitoring"}),c.jsx("li",{children:"• Maintain weight and exercise"})]}),o==="high_bp"&&c.jsxs("ul",{className:"space-y-1",children:[c.jsx("li",{children:"• Consider antihypertensive therapy"}),c.jsx("li",{children:"• Lifestyle modifications"}),c.jsx("li",{children:"• Regular follow-up"}),c.jsx("li",{children:"• Cardiovascular risk assessment"})]}),o==="low_bp"&&c.jsxs("ul",{className:"space-y-1",children:[c.jsx("li",{children:"• Evaluate for underlying causes"}),c.jsx("li",{children:"• Increase fluid intake"}),c.jsx("li",{children:"• Consider compression stockings"}),c.jsx("li",{children:"• Monitor for symptoms"})]})]})]})]}):c.jsxs("div",{className:"text-center py-12",children:[c.jsx("div",{className:"text-6xl mb-4",children:"🩺"}),c.jsx("h4",{className:"text-xl font-semibold text-gray-700 mb-2",children:"Select a patient and measure blood pressure"}),c.jsx("p",{className:"text-gray-500",children:'Choose a patient profile and click "Measure Blood Pressure" to begin'})]})]})})]})}),p=()=>c.jsx("div",{className:"p-6 max-w-6xl mx-auto",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[c.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Case Study Analysis"}),c.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-4",children:[c.jsx("h4",{className:"font-medium text-gray-700",children:"Patient Cases:"}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("div",{className:"p-4 bg-red-50 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-red-800",children:"Case 1: Hypertensive Crisis"}),c.jsx("p",{className:"text-sm text-red-700 mt-1",children:"65-year-old male, BP: 185/105 mmHg, chest pain, shortness of breath"}),c.jsxs("div",{className:"mt-2 text-xs text-red-600",children:[c.jsx("strong",{children:"Action:"})," Immediate medical attention required"]})]}),c.jsxs("div",{className:"p-4 bg-blue-50 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-blue-800",children:"Case 2: Orthostatic Hypotension"}),c.jsx("p",{className:"text-sm text-blue-700 mt-1",children:"72-year-old female, BP: 140/85 lying, 110/65 standing, dizziness"}),c.jsxs("div",{className:"mt-2 text-xs text-blue-600",children:[c.jsx("strong",{children:"Action:"})," Medication review, hydration assessment"]})]}),c.jsxs("div",{className:"p-4 bg-green-50 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-green-800",children:"Case 3: White Coat Hypertension"}),c.jsx("p",{className:"text-sm text-green-700 mt-1",children:"45-year-old male, clinic BP: 150/95, home BP: 125/80"}),c.jsxs("div",{className:"mt-2 text-xs text-green-600",children:[c.jsx("strong",{children:"Action:"})," Ambulatory monitoring, lifestyle counseling"]})]})]})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsx("h4",{className:"font-medium text-gray-700",children:"Analysis Framework:"}),c.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-gray-800 mb-2",children:"Assessment Steps:"}),c.jsxs("ol",{className:"list-decimal list-inside text-sm text-gray-600 space-y-1",children:[c.jsx("li",{children:"Verify measurement accuracy"}),c.jsx("li",{children:"Check for symptoms"}),c.jsx("li",{children:"Review medical history"}),c.jsx("li",{children:"Assess cardiovascular risk"}),c.jsx("li",{children:"Consider secondary causes"}),c.jsx("li",{children:"Plan appropriate intervention"})]})]}),c.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[c.jsx("h5",{className:"font-medium text-purple-800 mb-2",children:"Key Considerations:"}),c.jsxs("ul",{className:"text-sm text-purple-700 space-y-1",children:[c.jsx("li",{children:"• Age and comorbidities"}),c.jsx("li",{children:"• Medication effects"}),c.jsx("li",{children:"• Measurement technique"}),c.jsx("li",{children:"• Patient anxiety"}),c.jsx("li",{children:"• Environmental factors"})]})]})]})]})]})});return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t==="theory"&&h(),t==="simulation"&&f(),t==="analysis"&&p()]})},tw=({selectedModule:t})=>{const{t:e}=Je(),[n,i]=T.useState("theory");if(!t)return c.jsx("div",{className:"flex-1 flex items-center justify-center bg-gray-50",children:c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"text-6xl mb-4",children:"🔬"}),c.jsx("h2",{className:"text-2xl font-bold text-gray-700 mb-2",children:"Welcome to Biomedical Instrumentation Lab"}),c.jsx("p",{className:"text-gray-500",children:"Select a module from the navigation panel to begin learning"})]})});const s=Qp.find(l=>l.id===t);if(!s)return null;const r=[{id:"theory",label:e("theory_tab"),icon:Yp,available:s.hasTheory},{id:"simulation",label:e("simulation_tab"),icon:Dx,available:s.hasSimulation},{id:"analysis",label:e("analysis_tab"),icon:Kp,available:s.hasAnalysis}].filter(l=>l.available),o=()=>{const l={activeTab:n,moduleId:t};switch(t){case"ecg-fundamentals":return c.jsx(Fx,{...l});case"ecg-12-leads":return c.jsx(Y_,{...l});case"ecg-artifacts":return c.jsx(G_,{...l});case"ecg-pathology":return c.jsx(X_,{...l});case"eeg-fundamentals":return c.jsx(Q_,{...l});case"eeg-analysis":return c.jsx(Z_,{...l});case"bp-measurement":return c.jsx(J_,{...l});case"bp-conditions":return c.jsx(ew,{...l});default:return c.jsx("div",{children:"Module not found"})}};return c.jsxs("div",{className:"flex-1 flex flex-col bg-white",children:[c.jsx("div",{className:"bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("span",{className:"text-3xl",children:s.icon}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:e(s.titleKey)}),c.jsx("p",{className:"text-gray-600",children:s.description})]})]}),c.jsx("div",{className:"flex space-x-2",children:r.map(l=>c.jsxs("button",{onClick:()=>i(l.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all ${n===l.id?"bg-blue-500 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"}`,children:[c.jsx(l.icon,{className:"h-4 w-4"}),c.jsx("span",{children:l.label})]},l.id))})]})}),c.jsx("div",{className:"flex-1 overflow-auto",children:o()})]})},nw=()=>{const{t}=Je();return c.jsx("footer",{className:"bg-gray-800 text-white py-8 mt-12",children:c.jsx("div",{className:"container mx-auto px-4",children:c.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("p",{className:"text-lg font-medium mb-2",children:t("footer_author")}),c.jsx("p",{className:"text-sm text-gray-300",children:t("footer_copyright")})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6",children:[c.jsx("span",{className:"text-sm font-medium",children:t("footer_contact")}),c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("a",{href:"mailto:<EMAIL>",className:"flex items-center space-x-2 text-blue-400 hover:text-blue-300 transition-colors",children:[c.jsx(Ex,{className:"h-4 w-4"}),c.jsx("span",{className:"text-sm",children:"<EMAIL>"})]}),c.jsxs("div",{className:"flex items-center space-x-2 text-gray-300",children:[c.jsx(Lx,{className:"h-4 w-4"}),c.jsxs("div",{className:"flex flex-col sm:flex-row sm:space-x-2 text-sm",children:[c.jsx("a",{href:"tel:+249912867327",className:"hover:text-white transition-colors",children:"+249912867327"}),c.jsx("span",{className:"hidden sm:inline",children:"/"}),c.jsx("a",{href:"tel:+966538076790",className:"hover:text-white transition-colors",children:"+966538076790"})]})]})]})]})]})})})};function iw(){const{isLoading:t}=Je(),[e,n]=T.useState(null),[i,s]=T.useState(!1);return t?c.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),c.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):c.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[c.jsx(Ix,{}),c.jsxs("div",{className:"flex flex-1",children:[c.jsx(zx,{selectedModule:e,onModuleSelect:n,isCollapsed:i,onToggleCollapse:()=>s(!i)}),c.jsx(tw,{selectedModule:e})]}),c.jsx(nw,{})]})}$p(document.getElementById("root")).render(c.jsx(T.StrictMode,{children:c.jsx(iw,{})}));
