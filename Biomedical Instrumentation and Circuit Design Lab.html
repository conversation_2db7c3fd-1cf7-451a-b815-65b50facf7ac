<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biomedical Instrumentation & Circuit Design Lab</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Custom CSS for RTL support and animations */
        html[dir="rtl"] body {
            text-align: right;
        }
        html[dir="rtl"] .nav-panel {
            border-left: none;
            border-right: 1px solid #ccc;
        }
        
        /* ECG Animation */
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.05); opacity: 1; }
            100% { transform: scale(1); opacity: 0.7; }
        }
        
        .pulse {
            animation: pulse 1.5s infinite;
        }
        
        /* EEG Animation */
        @keyframes wave {
            0% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0); }
        }
        
        .wave {
            animation: wave 2s infinite;
        }
        
        /* Circuit components */
        .component {
            transition: all 0.3s ease;
            cursor: grab;
        }
        
        .component:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.5));
        }
        
        /* Smooth transitions */
        .content-transition {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        /* Loading animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        /* Dark theme enhancements */
        .dark-theme {
            --bg-primary: #1a1a2e;
            --bg-secondary: #16213e;
            --text-primary: #e6e6e6;
            --text-secondary: #b8b8b8;
            --accent: #4f46e5;
            --accent-hover: #6366f1;
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .nav-panel {
            background-color: var(--bg-secondary);
        }
        
        .module-card {
            background-color: var(--bg-secondary);
            transition: all 0.3s ease;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn-accent {
            background-color: var(--accent);
            color: white;
        }
        
        .btn-accent:hover {
            background-color: var(--accent-hover);
        }
    </style>
</head>
<body class="dark-theme font-sans flex flex-col min-h-screen">
    <!-- Header -->
    <header class="bg-indigo-900 text-white p-4 shadow-lg">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold" data-lang-key="app_title">Biomedical Instrumentation Lab</h1>
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <span class="mr-2" data-lang-key="language">Language:</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="languageToggle" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        <span class="ml-2 text-sm font-medium" id="languageLabel">English</span>
                    </label>
                </div>
                <div class="w-32 bg-gray-200 rounded-full h-2.5">
                    <div class="bg-indigo-500 h-2.5 rounded-full" style="width: 25%"></div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex flex-1 overflow-hidden">
        <!-- Navigation Panel -->
        <aside class="nav-panel w-64 p-4 border-r border-gray-700 overflow-y-auto transition-all duration-300">
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2" data-lang-key="difficulty_level">Difficulty Level</h3>
                <div class="flex flex-wrap gap-2">
                    <button class="difficulty-filter btn-accent px-3 py-1 rounded text-sm" data-level="all" data-lang-key="all">All</button>
                    <button class="difficulty-filter bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm" data-level="beginner" data-lang-key="beginner">Beginner</button>
                    <button class="difficulty-filter bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm" data-level="intermediate" data-lang-key="intermediate">Intermediate</button>
                    <button class="difficulty-filter bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm" data-level="advanced" data-lang-key="advanced">Advanced</button>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2" data-lang-key="modules">Modules</h3>
                <div class="space-y-2" id="moduleList">
                    <!-- ECG Modules -->
                    <div class="module-group">
                        <h4 class="font-medium text-indigo-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 pulse text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            <span data-lang-key="ecg_modules">ECG Modules</span>
                        </h4>
                        <ul class="ml-7 mt-1 space-y-1">
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="ecg-fundamentals" data-difficulty="beginner" data-lang-key="ecg_fundamentals">ECG Fundamentals</button></li>
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="ecg-leads" data-difficulty="beginner" data-lang-key="ecg_leads">ECG Leads</button></li>
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="ecg-artifacts" data-difficulty="intermediate" data-lang-key="ecg_artifacts">ECG Artifacts</button></li>
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="ecg-pathology" data-difficulty="advanced" data-lang-key="ecg_pathology">Pathological ECGs</button></li>
                        </ul>
                    </div>
                    
                    <!-- EEG Modules -->
                    <div class="module-group mt-4">
                        <h4 class="font-medium text-indigo-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 wave text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            <span data-lang-key="eeg_modules">EEG Modules</span>
                        </h4>
                        <ul class="ml-7 mt-1 space-y-1">
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="eeg-fundamentals" data-difficulty="beginner" data-lang-key="eeg_fundamentals">EEG Fundamentals</button></li>
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="eeg-analysis" data-difficulty="advanced" data-lang-key="eeg_analysis">EEG Analysis</button></li>
                        </ul>
                    </div>
                    
                    <!-- BP Modules -->
                    <div class="module-group mt-4">
                        <h4 class="font-medium text-indigo-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                            <span data-lang-key="bp_modules">Blood Pressure</span>
                        </h4>
                        <ul class="ml-7 mt-1 space-y-1">
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="bp-measurement" data-difficulty="beginner" data-lang-key="bp_measurement">BP Measurement</button></li>
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="bp-disorders" data-difficulty="intermediate" data-lang-key="bp_disorders">Hypertension/Hypotension</button></li>
                        </ul>
                    </div>
                    
                    <!-- Circuit Design Modules -->
                    <div class="module-group mt-4">
                        <h4 class="font-medium text-indigo-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                            </svg>
                            <span data-lang-key="circuit_modules">Circuit Design</span>
                        </h4>
                        <ul class="ml-7 mt-1 space-y-1">
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="inverting-amp" data-difficulty="intermediate" data-lang-key="inverting_amp">Inverting Amplifier</button></li>
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="low-pass-filter" data-difficulty="advanced" data-lang-key="low_pass_filter">Active Low-Pass Filter</button></li>
                            <li><button class="module-btn text-left hover:text-indigo-300 w-full py-1 px-2 rounded" data-module="instrumentation-amp" data-difficulty="advanced" data-lang-key="instrumentation_amp">Instrumentation Amplifier</button></li>
                        </ul>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="flex-1 overflow-y-auto p-6 bg-gray-900">
            <div id="loadingIndicator" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-gray-800 p-6 rounded-lg shadow-xl flex flex-col items-center">
                    <svg class="loading-spinner h-12 w-12 text-indigo-500 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-white" data-lang-key="loading">Loading module...</span>
                </div>
            </div>
            
            <!-- Default content (shown when no module is selected) -->
            <div id="defaultContent" class="content-transition">
                <div class="max-w-4xl mx-auto text-center">
                    <h2 class="text-3xl font-bold mb-6" data-lang-key="welcome_title">Welcome to Biomedical Instrumentation Lab</h2>
                    <p class="text-lg mb-8" data-lang-key="welcome_message">Select a module from the left panel to begin your learning journey. This interactive platform covers ECG, EEG, Blood Pressure monitoring, and Biomedical Circuit Design.</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
                        <!-- Beginner Modules -->
                        <div class="module-card p-6 rounded-lg shadow-md border border-gray-700">
                            <div class="flex items-center mb-4">
                                <div class="bg-green-500 rounded-full p-2 mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold" data-lang-key="beginner_level">Beginner Level</h3>
                            </div>
                            <p class="text-gray-300 mb-4" data-lang-key="beginner_description">Start with the basics of biomedical instrumentation. Learn about ECG fundamentals, EEG basics, and blood pressure measurement.</p>
                            <button class="difficulty-filter btn-accent px-4 py-2 rounded text-sm" data-level="beginner" data-lang-key="view_modules">View Modules</button>
                        </div>
                        
                        <!-- Intermediate Modules -->
                        <div class="module-card p-6 rounded-lg shadow-md border border-gray-700">
                            <div class="flex items-center mb-4">
                                <div class="bg-yellow-500 rounded-full p-2 mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold" data-lang-key="intermediate_level">Intermediate Level</h3>
                            </div>
                            <p class="text-gray-300 mb-4" data-lang-key="intermediate_description">Dive deeper into ECG artifacts, blood pressure disorders, and begin circuit design with inverting amplifiers.</p>
                            <button class="difficulty-filter btn-accent px-4 py-2 rounded text-sm" data-level="intermediate" data-lang-key="view_modules">View Modules</button>
                        </div>
                        
                        <!-- Advanced Modules -->
                        <div class="module-card p-6 rounded-lg shadow-md border border-gray-700">
                            <div class="flex items-center mb-4">
                                <div class="bg-red-500 rounded-full p-2 mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold" data-lang-key="advanced_level">Advanced Level</h3>
                            </div>
                            <p class="text-gray-300 mb-4" data-lang-key="advanced_description">Master pathological ECGs, EEG analysis, and complex circuit designs like active filters and instrumentation amplifiers.</p>
                            <button class="difficulty-filter btn-accent px-4 py-2 rounded text-sm" data-level="advanced" data-lang-key="view_modules">View Modules</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Dynamic content will be loaded here -->
            <div id="moduleContent" class="content-transition hidden"></div>
        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-300 py-6">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <p data-lang-key="footer_author">Author: Dr. Mohammed Yagoub Esmail, SUST - BME © 2025. All Rights Reserved.</p>
                <p class="mt-2">
                    <span data-lang-key="contact">Contact:</span> 
                    <a href="mailto:<EMAIL>" class="text-indigo-400 hover:text-indigo-300"><EMAIL></a> | 
                    <a href="tel:+249912867327" class="text-indigo-400 hover:text-indigo-300">+249912867327</a> / 
                    <a href="tel:+966538076790" class="text-indigo-400 hover:text-indigo-300">+966538076790</a>
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Language data
        const languageData = {
            en: {
                app_title: "Biomedical Instrumentation Lab",
                language: "Language:",
                difficulty_level: "Difficulty Level",
                all: "All",
                beginner: "Beginner",
                intermediate: "Intermediate",
                advanced: "Advanced",
                modules: "Modules",
                ecg_modules: "ECG Modules",
                ecg_fundamentals: "ECG Fundamentals",
                ecg_leads: "ECG Leads",
                ecg_artifacts: "ECG Artifacts",
                ecg_pathology: "Pathological ECGs",
                eeg_modules: "EEG Modules",
                eeg_fundamentals: "EEG Fundamentals",
                eeg_analysis: "EEG Analysis",
                bp_modules: "Blood Pressure",
                bp_measurement: "BP Measurement",
                bp_disorders: "Hypertension/Hypotension",
                circuit_modules: "Circuit Design",
                inverting_amp: "Inverting Amplifier",
                low_pass_filter: "Active Low-Pass Filter",
                instrumentation_amp: "Instrumentation Amplifier",
                welcome_title: "Welcome to Biomedical Instrumentation Lab",
                welcome_message: "Select a module from the left panel to begin your learning journey. This interactive platform covers ECG, EEG, Blood Pressure monitoring, and Biomedical Circuit Design.",
                beginner_level: "Beginner Level",
                beginner_description: "Start with the basics of biomedical instrumentation. Learn about ECG fundamentals, EEG basics, and blood pressure measurement.",
                intermediate_level: "Intermediate Level",
                intermediate_description: "Dive deeper into ECG artifacts, blood pressure disorders, and begin circuit design with inverting amplifiers.",
                advanced_level: "Advanced Level",
                advanced_description: "Master pathological ECGs, EEG analysis, and complex circuit designs like active filters and instrumentation amplifiers.",
                view_modules: "View Modules",
                loading: "Loading module...",
                footer_author: "Author: Dr. Mohammed Yagoub Esmail, SUST - BME © 2025. All Rights Reserved.",
                contact: "Contact:",
                // ECG Fundamentals Module
                ecg_fundamentals_title: "ECG Fundamentals",
                ecg_fundamentals_theory: "Electrocardiography (ECG) is the process of recording the electrical activity of the heart over a period of time using electrodes placed on the skin. These electrodes detect the tiny electrical changes on the skin that arise from the heart muscle's electrophysiologic pattern of depolarizing and repolarizing during each heartbeat.",
                einthoven_triangle: "Einthoven's Triangle",
                einthoven_triangle_desc: "Einthoven's triangle is an imaginary formation of three limb leads (I, II, and III) in a triangle used in electrocardiography, formed by the two shoulders and the pubis. The shape forms an inverted equilateral triangle with the heart at the center.",
                electrode_placement: "Electrode Placement",
                electrode_placement_desc: "Standard 12-lead ECGs use 10 electrodes placed in specific locations on the body. Correct placement is crucial for accurate readings. The limb electrodes are placed on the right arm (RA), left arm (LA), right leg (RL), and left leg (LL). The chest electrodes (V1-V6) are placed in specific intercostal spaces.",
                drag_electrodes: "Drag the electrodes to their correct positions",
                correct_placement: "Correct! All electrodes are in the right position.",
                try_again: "Try again. Some electrodes are not correctly placed.",
                // ECG Leads Module
                ecg_leads_title: "ECG Leads",
                lead_types: "Lead Types",
                bipolar_leads: "Bipolar Leads (I, II, III)",
                augmented_leads: "Augmented Leads (aVR, aVL, aVF)",
                chest_leads: "Chest Leads (V1-V6)",
                select_lead: "Select Lead to View:",
                normal_ecg: "Normal ECG Waveform",
                p_wave: "P Wave: Atrial depolarization",
                qrs_complex: "QRS Complex: Ventricular depolarization",
                t_wave: "T Wave: Ventricular repolarization",
                // ECG Artifacts Module
                ecg_artifacts_title: "ECG Artifacts",
                artifact_types: "Common ECG Artifacts",
                baseline_wander: "Baseline Wander",
                muscle_tremor: "Muscle Tremor (EMG noise)",
                powerline_interference: "50/60 Hz Powerline Interference",
                electrode_motion: "Electrode Motion Artifact",
                inject_artifact: "Inject Artifact:",
                clean_ecg: "Clean ECG",
                with_artifact: "ECG with Artifact",
                // BP Measurement Module
                bp_measurement_title: "Blood Pressure Measurement",
                korotkoff_sounds: "Korotkoff Sounds",
                systolic_pressure: "Systolic Pressure",
                diastolic_pressure: "Diastolic Pressure",
                inflate_cuff: "Inflate Cuff",
                deflate_cuff: "Slowly Deflate Cuff",
                bp_reading: "BP Reading:",
                // Circuit Design Modules
                circuit_design_title: "Circuit Design Workbench",
                component_palette: "Component Palette",
                resistor: "Resistor",
                capacitor: "Capacitor",
                op_amp: "Op-Amp",
                voltage_source: "Voltage Source",
                ground: "Ground",
                wire_mode: "Wire Mode",
                run_simulation: "Run Simulation",
                stop_simulation: "Stop Simulation",
                reset_circuit: "Reset Circuit",
                check_circuit: "Check My Circuit",
                circuit_feedback: "Circuit Feedback:",
                inverting_amp_task: "Build an inverting amplifier with gain of -10 using a 10kΩ feedback resistor",
                low_pass_task: "Design a low-pass filter with cutoff frequency of 40Hz to remove 50Hz noise",
                inst_amp_task: "Build a 3-op-amp instrumentation amplifier for differential signals"
            },
            ar: {
                app_title: "مختبر الأجهزة الطبية الحيوية",
                language: "اللغة:",
                difficulty_level: "مستوى الصعوبة",
                all: "الكل",
                beginner: "مبتدئ",
                intermediate: "متوسط",
                advanced: "متقدم",
                modules: "الوحدات",
                ecg_modules: "وحدات تخطيط القلب",
                ecg_fundamentals: "أساسيات تخطيط القلب",
                ecg_leads: "أقطاب تخطيط القلب",
                ecg_artifacts: "تشوهات تخطيط القلب",
                ecg_pathology: "تخطيط القلب المرضي",
                eeg_modules: "وحدات تخطيط الدماغ",
                eeg_fundamentals: "أساسيات تخطيط الدماغ",
                eeg_analysis: "تحليل تخطيط الدماغ",
                bp_modules: "ضغط الدم",
                bp_measurement: "قياس ضغط الدم",
                bp_disorders: "ارتفاع/انخفاض ضغط الدم",
                circuit_modules: "تصميم الدوائر",
                inverting_amp: "مضخم عاكس",
                low_pass_filter: "مرشح تمرير منخفض",
                instrumentation_amp: "مضخم أجهزة القياس",
                welcome_title: "مرحبًا بكم في مختبر الأجهزة الطبية الحيوية",
                welcome_message: "اختر وحدة من اللوحة الجانبية لبدء رحلة التعلم. هذه المنصة التفاعلية تغطي تخطيط القلب، تخطيط الدماغ، مراقبة ضغط الدم، وتصميم الدوائر الطبية الحيوية.",
                beginner_level: "المستوى المبتدئ",
                beginner_description: "ابدأ بأساسيات الأجهزة الطبية الحيوية. تعلم أساسيات تخطيط القلب، تخطيط الدماغ، وقياس ضغط الدم.",
                intermediate_level: "المستوى المتوسط",
                intermediate_description: "تعمق في تشوهات تخطيط القلب، اضطرابات ضغط الدم، وابدأ تصميم الدوائر مع المضخمات العاكسة.",
                advanced_level: "المستوى المتقدم",
                advanced_description: "أتقن تخطيط القلب المرضي، تحليل تخطيط الدماغ، وتصميم الدوائر المعقدة مثل المرشحات النشطة ومضخمات أجهزة القياس.",
                view_modules: "عرض الوحدات",
                loading: "جارٍ تحميل الوحدة...",
                footer_author: "المؤلف: د. محمد يعقوب إسماعيل، جامعة السودان للعلوم والتكنولوجيا - هندسة طبية حيوية © 2025. جميع الحقوق محفوظة.",
                contact: "للتواصل:",
                // ECG Fundamentals Module
                ecg_fundamentals_title: "أساسيات تخطيط القلب",
                ecg_fundamentals_theory: "تخطيط القلب الكهربائي هو عملية تسجيل النشاط الكهربائي للقلب على مدى فترة من الزمن باستخدام أقطاب كهربائية موضوعة على الجلد. تكتشف هذه الأقطاب التغيرات الكهربائية الصغيرة على الجلد الناتجة عن نمط إزالة الاستقطاب وإعادة الاستقطاب الكهروفسيولوجي لعضلة القلب خلال كل نبضة.",
                einthoven_triangle: "مثلث إينتهوفن",
                einthoven_triangle_desc: "مثلث إينتهوفن هو تكوين خيالي لثلاثة أقطاب طرفية (I، II، III) في مثلث يستخدم في تخطيط القلب الكهربائي، يتكون من الكتفين والعانة. يشكل الشكل مثلثًا متساوي الأضلاع مقلوبًا مع القلب في المركز.",
                electrode_placement: "وضع الأقطاب الكهربائية",
                electrode_placement_desc: "يستخدم تخطيط القلب القياسي المكون من 12 قطبًا 10 أقطاب كهربائية موضوعة في مواقع محددة على الجسم. يعد الوضع الصحيح أمرًا بالغ الأهمية للحصول على قراءات دقيقة. توضع الأقطاب الطرفية على الذراع الأيمن (RA) والذراع الأيسر (LA) والساق اليمنى (RL) والساق اليسرى (LL). توضع الأقطاب الصدرية (V1-V6) في مسافات محددة بين الأضلاع.",
                drag_electrodes: "اسحب الأقطاب إلى مواقعها الصحيحة",
                correct_placement: "صحيح! جميع الأقطاب في الموضع الصحيح.",
                try_again: "حاول مرة أخرى. بعض الأقطاب ليست في الموضع الصحيح.",
                // ECG Leads Module
                ecg_leads_title: "أقطاب تخطيط القلب",
                lead_types: "أنواع الأقطاب",
                bipolar_leads: "أقطاب ثنائية القطب (I, II, III)",
                augmented_leads: "أقطاب معززة (aVR, aVL, aVF)",
                chest_leads: "أقطاب صدرية (V1-V6)",
                select_lead: "اختر القطب لعرضه:",
                normal_ecg: "موجة تخطيط قلب طبيعية",
                p_wave: "موجة P: إزالة استقطاب الأذين",
                qrs_complex: "مركب QRS: إزالة استقطاب البطين",
                t_wave: "موجة T: إعادة استقطاب البطين",
                // ECG Artifacts Module
                ecg_artifacts_title: "تشوهات تخطيط القلب",
                artifact_types: "تشوهات تخطيط القلب الشائعة",
                baseline_wander: "تذبذب الخط الأساسي",
                muscle_tremor: "رعشة العضلات (ضوضاء EMG)",
                powerline_interference: "تدخل تردد الشبكة 50/60 هرتز",
                electrode_motion: "تشوه حركة القطب",
                inject_artifact: "حقن التشوه:",
                clean_ecg: "تخطيط قلب نظيف",
                with_artifact: "تخطيط قلب مع تشوه",
                // BP Measurement Module
                bp_measurement_title: "قياس ضغط الدم",
                korotkoff_sounds: "أصوات كوروتكوف",
                systolic_pressure: "الضغط الانقباضي",
                diastolic_pressure: "الضغط الانبساطي",
                inflate_cuff: "نفخ الكفة",
                deflate_cuff: "تفريغ الكفة ببطء",
                bp_reading: "قراءة ضغط الدم:",
                // Circuit Design Modules
                circuit_design_title: "منصة تصميم الدوائر",
                component_palette: "قائمة المكونات",
                resistor: "مقاومة",
                capacitor: "مكثف",
                op_amp: "مضخم عملياتي",
                voltage_source: "مصدر جهد",
                ground: "أرضي",
                wire_mode: "وضع التوصيل",
                run_simulation: "تشغيل المحاكاة",
                stop_simulation: "إيقاف المحاكاة",
                reset_circuit: "إعادة تعيين الدائرة",
                check_circuit: "تحقق من دوارتي",
                circuit_feedback: "ملاحظات الدائرة:",
                inverting_amp_task: "قم ببناء مضخم عاكس بكسب -10 باستخدام مقاومة تغذية مرتدة 10 كيلو أوم",
                low_pass_task: "صمم مرشح تمرير منخفض بتردد قطع 40 هرتز لإزالة ضوضاء 50 هرتز",
                inst_amp_task: "قم ببناء مضخم أجهزة قياس بثلاث مضخمات عملياتية للإشارات التفاضلية"
            }
        };

        // Current language
        let currentLanguage = 'en';

        // Load language function
        function loadLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('preferredLanguage', lang);
            
            // Update all elements with data-lang-key attributes
            document.querySelectorAll('[data-lang-key]').forEach(element => {
                const key = element.getAttribute('data-lang-key');
                if (languageData[lang][key]) {
                    element.textContent = languageData[lang][key];
                }
            });
            
            // Update language toggle label
            document.getElementById('languageLabel').textContent = lang === 'en' ? 'English' : 'العربية';
            
            // Update RTL direction
            if (lang === 'ar') {
                document.documentElement.dir = 'rtl';
                document.documentElement.lang = 'ar';
            } else {
                document.documentElement.dir = 'ltr';
                document.documentElement.lang = 'en';
            }
            
            // Refresh any module content if loaded
            const activeModule = document.querySelector('.module-btn.active');
            if (activeModule) {
                loadModule(activeModule.dataset.module);
            }
        }

        // Language toggle event
        document.getElementById('languageToggle').addEventListener('change', function() {
            const newLang = this.checked ? 'ar' : 'en';
            loadLanguage(newLang);
        });

        // Check for saved language preference
        const savedLanguage = localStorage.getItem('preferredLanguage');
        if (savedLanguage) {
            currentLanguage = savedLanguage;
            document.getElementById('languageToggle').checked = savedLanguage === 'ar';
            loadLanguage(savedLanguage);
        }

        // Difficulty filter functionality
        document.querySelectorAll('.difficulty-filter').forEach(button => {
            button.addEventListener('click', function() {
                const level = this.dataset.level;
                
                // Update active state
                document.querySelectorAll('.difficulty-filter').forEach(btn => {
                    btn.classList.remove('btn-accent');
                    btn.classList.add('bg-gray-700', 'hover:bg-gray-600');
                });
                
                this.classList.add('btn-accent');
                this.classList.remove('bg-gray-700', 'hover:bg-gray-600');
                
                // Filter modules
                document.querySelectorAll('.module-btn').forEach(moduleBtn => {
                    if (level === 'all' || moduleBtn.dataset.difficulty === level) {
                        moduleBtn.parentElement.style.display = 'block';
                    } else {
                        moduleBtn.parentElement.style.display = 'none';
                    }
                });
            });
        });

        // Module loading functionality
        function loadModule(moduleId) {
            // Show loading indicator
            document.getElementById('loadingIndicator').classList.remove('hidden');
            
            // Hide default content
            document.getElementById('defaultContent').classList.add('hidden');
            
            // Clear any previous module content
            const moduleContent = document.getElementById('moduleContent');
            moduleContent.innerHTML = '';
            moduleContent.classList.remove('hidden');
            
            // Set active state on module button
            document.querySelectorAll('.module-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-indigo-900', 'text-white');
            });
            document.querySelector(`.module-btn[data-module="${moduleId}"]`).classList.add('active', 'bg-indigo-900', 'text-white');
            
            // Simulate loading delay (in real app, this would be fetching data)
            setTimeout(() => {
                // Hide loading indicator
                document.getElementById('loadingIndicator').classList.add('hidden');
                
                // Load module content based on moduleId
                let content = '';
                
                switch(moduleId) {
                    case 'ecg-fundamentals':
                        content = `
                            <div class="max-w-6xl mx-auto">
                                <h2 class="text-2xl font-bold mb-6" data-lang-key="ecg_fundamentals_title">ECG Fundamentals</h2>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <!-- Theory Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="theory">Theory</h3>
                                        <p class="mb-4" data-lang-key="ecg_fundamentals_theory">${languageData[currentLanguage].ecg_fundamentals_theory}</p>
                                        
                                        <div class="mb-6">
                                            <h4 class="font-medium mb-2" data-lang-key="einthoven_triangle">${languageData[currentLanguage].einthoven_triangle}</h4>
                                            <p class="text-gray-300 mb-3" data-lang-key="einthoven_triangle_desc">${languageData[currentLanguage].einthoven_triangle_desc}</p>
                                            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Einthoven_triangle.svg/1200px-Einthoven_triangle.svg.png" alt="Einthoven's Triangle" class="w-full rounded">
                                        </div>
                                        
                                        <div>
                                            <h4 class="font-medium mb-2" data-lang-key="electrode_placement">${languageData[currentLanguage].electrode_placement}</h4>
                                            <p class="text-gray-300 mb-3" data-lang-key="electrode_placement_desc">${languageData[currentLanguage].electrode_placement_desc}</p>
                                            <img src="https://www.researchgate.net/profile/Joel-Parish/publication/334607746/figure/fig1/AS:782909853691904@1563997093304/12-lead-ECG-electrode-placement.png" alt="Electrode Placement" class="w-full rounded">
                                        </div>
                                    </div>
                                    
                                    <!-- Simulation Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="simulation">Simulation</h3>
                                        <p class="mb-4" data-lang-key="drag_electrodes">${languageData[currentLanguage].drag_electrodes}</p>
                                        
                                        <div class="relative bg-gray-700 rounded-lg p-4 h-96">
                                            <div id="ecgPlacementArea" class="relative w-full h-full border border-dashed border-gray-500 rounded">
                                                <!-- Electrodes will be positioned here -->
                                                <div class="absolute bg-red-500 rounded-full w-8 h-8 cursor-move electrode" style="top: 20%; left: 30%;" data-position="RA"></div>
                                                <div class="absolute bg-blue-500 rounded-full w-8 h-8 cursor-move electrode" style="top: 20%; left: 70%;" data-position="LA"></div>
                                                <div class="absolute bg-green-500 rounded-full w-8 h-8 cursor-move electrode" style="top: 80%; left: 30%;" data-position="RL"></div>
                                                <div class="absolute bg-yellow-500 rounded-full w-8 h-8 cursor-move electrode" style="top: 80%; left: 70%;" data-position="LL"></div>
                                                
                                                <!-- Body outline -->
                                                <div class="absolute inset-0 flex items-center justify-center">
                                                    <svg viewBox="0 0 100 100" class="w-full h-full">
                                                        <circle cx="50" cy="30" r="15" fill="none" stroke="#4B5563" stroke-width="1"/>
                                                        <path d="M50 45 L50 80" stroke="#4B5563" stroke-width="1" fill="none"/>
                                                        <path d="M50 60 L30 80" stroke="#4B5563" stroke-width="1" fill="none"/>
                                                        <path d="M50 60 L70 80" stroke="#4B5563" stroke-width="1" fill="none"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            
                                            <button id="checkPlacement" class="btn-accent px-4 py-2 rounded mt-4" data-lang-key="check_placement">Check Placement</button>
                                            <p id="placementFeedback" class="mt-2 hidden"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        break;
                        
                    case 'ecg-leads':
                        content = `
                            <div class="max-w-6xl mx-auto">
                                <h2 class="text-2xl font-bold mb-6" data-lang-key="ecg_leads_title">ECG Leads</h2>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <!-- Theory Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="theory">Theory</h3>
                                        
                                        <div class="mb-6">
                                            <h4 class="font-medium mb-2" data-lang-key="lead_types">${languageData[currentLanguage].lead_types}</h4>
                                            
                                            <div class="space-y-3">
                                                <div>
                                                    <h5 class="font-medium text-indigo-300" data-lang-key="bipolar_leads">${languageData[currentLanguage].bipolar_leads}</h5>
                                                    <p class="text-gray-300">Measure electrical potential between two electrodes (I: LA-RA, II: LL-RA, III: LL-LA)</p>
                                                </div>
                                                
                                                <div>
                                                    <h5 class="font-medium text-indigo-300" data-lang-key="augmented_leads">${languageData[currentLanguage].augmented_leads}</h5>
                                                    <p class="text-gray-300">Unipolar leads that compare one limb electrode against the other two</p>
                                                </div>
                                                
                                                <div>
                                                    <h5 class="font-medium text-indigo-300" data-lang-key="chest_leads">${languageData[currentLanguage].chest_leads}</h5>
                                                    <p class="text-gray-300">Unipolar leads placed on the chest to provide a horizontal plane view</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <h4 class="font-medium mb-2" data-lang-key="normal_ecg">${languageData[currentLanguage].normal_ecg}</h4>
                                            <div class="space-y-2">
                                                <p class="text-gray-300"><span class="font-medium" data-lang-key="p_wave">${languageData[currentLanguage].p_wave}</span> - Represents atrial depolarization</p>
                                                <p class="text-gray-300"><span class="font-medium" data-lang-key="qrs_complex">${languageData[currentLanguage].qrs_complex}</span> - Represents ventricular depolarization</p>
                                                <p class="text-gray-300"><span class="font-medium" data-lang-key="t_wave">${languageData[currentLanguage].t_wave}</span> - Represents ventricular repolarization</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Simulation Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="simulation">Simulation</h3>
                                        
                                        <div class="mb-4">
                                            <label class="block mb-2" data-lang-key="select_lead">${languageData[currentLanguage].select_lead}</label>
                                            <select id="leadSelector" class="bg-gray-700 border border-gray-600 rounded px-3 py-2 w-full">
                                                <option value="I">Lead I (LA-RA)</option>
                                                <option value="II">Lead II (LL-RA)</option>
                                                <option value="III">Lead III (LL-LA)</option>
                                                <option value="aVR">aVR</option>
                                                <option value="aVL">aVL</option>
                                                <option value="aVF">aVF</option>
                                                <option value="V1">V1</option>
                                                <option value="V2">V2</option>
                                                <option value="V3">V3</option>
                                                <option value="V4">V4</option>
                                                <option value="V5">V5</option>
                                                <option value="V6">V6</option>
                                            </select>
                                        </div>
                                        
                                        <div class="bg-gray-700 rounded-lg p-4">
                                            <canvas id="ecgChart" height="250"></canvas>
                                        </div>
                                        
                                        <div class="mt-4 grid grid-cols-3 gap-2">
                                            <button class="ecg-control btn-accent px-3 py-2 rounded text-sm" data-speed="0.5">0.5x</button>
                                            <button class="ecg-control bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded text-sm" data-speed="1">1x</button>
                                            <button class="ecg-control bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded text-sm" data-speed="2">2x</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        break;
                        
                    case 'ecg-artifacts':
                        content = `
                            <div class="max-w-6xl mx-auto">
                                <h2 class="text-2xl font-bold mb-6" data-lang-key="ecg_artifacts_title">ECG Artifacts</h2>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <!-- Theory Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="theory">Theory</h3>
                                        
                                        <div class="mb-6">
                                            <h4 class="font-medium mb-2" data-lang-key="artifact_types">${languageData[currentLanguage].artifact_types}</h4>
                                            
                                            <div class="space-y-4">
                                                <div>
                                                    <h5 class="font-medium text-indigo-300" data-lang-key="baseline_wander">${languageData[currentLanguage].baseline_wander}</h5>
                                                    <p class="text-gray-300">Slow, undulating baseline caused by patient movement, respiration, or poor electrode contact</p>
                                                </div>
                                                
                                                <div>
                                                    <h5 class="font-medium text-indigo-300" data-lang-key="muscle_tremor">${languageData[currentLanguage].muscle_tremor}</h5>
                                                    <p class="text-gray-300">High-frequency noise from skeletal muscle activity, resembling atrial fibrillation</p>
                                                </div>
                                                
                                                <div>
                                                    <h5 class="font-medium text-indigo-300" data-lang-key="powerline_interference">${languageData[currentLanguage].powerline_interference}</h5>
                                                    <p class="text-gray-300">60Hz (or 50Hz) interference from electrical equipment, appearing as fine oscillations</p>
                                                </div>
                                                
                                                <div>
                                                    <h5 class="font-medium text-indigo-300" data-lang-key="electrode_motion">${languageData[currentLanguage].electrode_motion}</h5>
                                                    <p class="text-gray-300">Abrupt baseline shifts from electrode movement, often mimicking ectopic beats</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Simulation Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="simulation">Simulation</h3>
                                        
                                        <div class="mb-4">
                                            <label class="block mb-2" data-lang-key="inject_artifact">${languageData[currentLanguage].inject_artifact}</label>
                                            <select id="artifactSelector" class="bg-gray-700 border border-gray-600 rounded px-3 py-2 w-full">
                                                <option value="none" data-lang-key="clean_ecg">${languageData[currentLanguage].clean_ecg}</option>
                                                <option value="wander" data-lang-key="baseline_wander">${languageData[currentLanguage].baseline_wander}</option>
                                                <option value="tremor" data-lang-key="muscle_tremor">${languageData[currentLanguage].muscle_tremor}</option>
                                                <option value="powerline" data-lang-key="powerline_interference">${languageData[currentLanguage].powerline_interference}</option>
                                                <option value="motion" data-lang-key="electrode_motion">${languageData[currentLanguage].electrode_motion}</option>
                                            </select>
                                        </div>
                                        
                                        <div class="bg-gray-700 rounded-lg p-4 mb-4">
                                            <canvas id="artifactChart" height="250"></canvas>
                                        </div>
                                        
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block mb-2" data-lang-key="clean_ecg">${languageData[currentLanguage].clean_ecg}</label>
                                                <div class="bg-gray-700 rounded-lg p-4">
                                                    <canvas id="cleanEcgChart" height="150"></canvas>
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block mb-2" data-lang-key="with_artifact">${languageData[currentLanguage].with_artifact}</label>
                                                <div class="bg-gray-700 rounded-lg p-4">
                                                    <canvas id="artifactEcgChart" height="150"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        break;
                        
                    case 'bp-measurement':
                        content = `
                            <div class="max-w-6xl mx-auto">
                                <h2 class="text-2xl font-bold mb-6" data-lang-key="bp_measurement_title">Blood Pressure Measurement</h2>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <!-- Theory Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="theory">Theory</h3>
                                        
                                        <div class="mb-6">
                                            <h4 class="font-medium mb-2" data-lang-key="korotkoff_sounds">${languageData[currentLanguage].korotkoff_sounds}</h4>
                                            <p class="text-gray-300">Korotkoff sounds are the sounds heard through a stethoscope as the cuff pressure is gradually released. The first sound corresponds to systolic pressure, and the disappearance of sounds corresponds to diastolic pressure.</p>
                                        </div>
                                        
                                        <div>
                                            <h4 class="font-medium mb-2">Measurement Procedure</h4>
                                            <ol class="list-decimal pl-5 space-y-2 text-gray-300">
                                                <li>Position the cuff snugly around the upper arm at heart level</li>
                                                <li>Palpate the brachial artery and place the stethoscope over it</li>
                                                <li>Inflate the cuff to about 180 mmHg (or 30 mmHg above estimated systolic)</li>
                                                <li>Slowly deflate the cuff at 2-3 mmHg per second</li>
                                                <li>Note the pressure when the first Korotkoff sound is heard (systolic)</li>
                                                <li>Note the pressure when the sounds disappear (diastolic)</li>
                                            </ol>
                                        </div>
                                    </div>
                                    
                                    <!-- Simulation Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="simulation">Simulation</h3>
                                        
                                        <div class="flex flex-col items-center">
                                            <div class="relative w-64 h-96 mb-6">
                                                <!-- Arm with cuff -->
                                                <div class="absolute w-32 h-64 bg-gray-600 rounded-full" style="left: 50%; transform: translateX(-50%);"></div>
                                                <div id="bpCuff" class="absolute w-40 h-12 bg-blue-700 rounded-lg" style="left: 50%; top: 30%; transform: translateX(-50%); transition: height 0.3s ease;">
                                                    <div class="absolute w-full h-1 bg-blue-800 top-1/2 transform -translate-y-1/2"></div>
                                                </div>
                                                
                                                <!-- Pressure gauge -->
                                                <div class="absolute w-32 h-32 bg-gray-800 rounded-full border-4 border-gray-700" style="right: 0; top: 0;">
                                                    <div class="absolute inset-0 flex items-center justify-center">
                                                        <div class="w-24 h-24 bg-gray-900 rounded-full border-2 border-gray-700"></div>
                                                    </div>
                                                    <div id="gaugeNeedle" class="absolute w-1/2 h-1 bg-red-500 origin-right" style="left: 50%; top: 50%; transform: rotate(-45deg); transform-origin: right center;"></div>
                                                    <div class="absolute inset-0 flex items-center justify-center text-xs">
                                                        <span class="absolute top-2">300</span>
                                                        <span class="absolute bottom-2">0</span>
                                                        <span class="absolute left-2">150</span>
                                                        <span class="absolute right-2">150</span>
                                                    </div>
                                                </div>
                                                
                                                <!-- Stethoscope -->
                                                <div class="absolute w-20 h-20" style="left: 60%; top: 40%;">
                                                    <div class="absolute w-16 h-16 bg-gray-400 rounded-full"></div>
                                                    <div class="absolute w-4 h-6 bg-gray-500" style="left: 16px; top: 16px;"></div>
                                                </div>
                                                
                                                <!-- Korotkoff sound indicator -->
                                                <div id="korotkoffIndicator" class="absolute w-6 h-6 bg-red-500 rounded-full opacity-0" style="left: 70%; top: 45%; transition: opacity 0.3s ease;"></div>
                                            </div>
                                            
                                            <div class="w-full max-w-md">
                                                <div class="flex justify-between mb-2">
                                                    <span data-lang-key="systolic_pressure">${languageData[currentLanguage].systolic_pressure}: <span id="systolicValue">--</span> mmHg</span>
                                                    <span data-lang-key="diastolic_pressure">${languageData[currentLanguage].diastolic_pressure}: <span id="diastolicValue">--</span> mmHg</span>
                                                </div>
                                                
                                                <div class="grid grid-cols-2 gap-4 mb-4">
                                                    <button id="inflateBtn" class="btn-accent px-4 py-2 rounded" data-lang-key="inflate_cuff">${languageData[currentLanguage].inflate_cuff}</button>
                                                    <button id="deflateBtn" class="bg-gray-600 hover:bg-gray-500 px-4 py-2 rounded" disabled data-lang-key="deflate_cuff">${languageData[currentLanguage].deflate_cuff}</button>
                                                </div>
                                                
                                                <div class="bg-gray-700 rounded-lg p-4">
                                                    <canvas id="bpChart" height="150"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        break;
                        
                    case 'inverting-amp':
                        content = `
                            <div class="max-w-6xl mx-auto">
                                <h2 class="text-2xl font-bold mb-6" data-lang-key="inverting_amp">Inverting Amplifier</h2>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                    <!-- Theory Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="theory">Theory</h3>
                                        
                                        <div class="mb-6">
                                            <p class="text-gray-300">An inverting amplifier is a basic op-amp circuit configuration that produces an amplified output signal that is 180° out of phase with the input signal. The gain is determined by the ratio of the feedback resistor (Rf) to the input resistor (Rin).</p>
                                        </div>
                                        
                                        <div class="mb-6">
                                            <h4 class="font-medium mb-2">Key Characteristics</h4>
                                            <ul class="list-disc pl-5 space-y-2 text-gray-300">
                                                <li>Gain = -Rf/Rin (negative sign indicates phase inversion)</li>
                                                <li>Input impedance ≈ Rin (relatively low)</li>
                                                <li>Virtual ground at the inverting input (when op-amp is ideal)</li>
                                                <li>Commonly used in signal processing and biomedical instrumentation</li>
                                            </ul>
                                        </div>
                                        
                                        <div>
                                            <h4 class="font-medium mb-2" data-lang-key="inverting_amp_task">${languageData[currentLanguage].inverting_amp_task}</h4>
                                            <p class="text-gray-300">Calculate the required input resistor value to achieve a gain of -10 with a 10kΩ feedback resistor.</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Simulation Section -->
                                    <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
                                        <h3 class="text-xl font-semibold mb-4" data-lang-key="simulation">Simulation</h3>
                                        
                                        <div class="mb-4">
                                            <div class="flex justify-between mb-2">
                                                <span data-lang-key="component_palette">${languageData[currentLanguage].component_palette}</span>
                                                <button id="wireModeBtn" class="bg-gray-600 hover:bg-gray-500 px-3 py-1 rounded text-sm" data-lang-key="wire_mode">${languageData[currentLanguage].wire_mode}</button>
                                            </div>
                                            
                                            <div class="grid grid-cols-3 gap-2 mb-4">
                                                <button class="component-btn bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-sm" data-component="resistor" data-lang-key="resistor">${languageData[currentLanguage].resistor}</button>
                                                <button class="component-btn bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-sm" data-component="capacitor" data-lang-key="capacitor">${languageData[currentLanguage].capacitor}</button>
                                                <button class="component-btn bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-sm" data-component="opamp" data-lang-key="op_amp">${languageData[currentLanguage].op_amp}</button>
                                                <button class="component-btn bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-sm" data-component="source" data-lang-key="voltage_source">${languageData[currentLanguage].voltage_source}</button>
                                                <button class="component-btn bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-sm" data-component="ground" data-lang-key="ground">${languageData[currentLanguage].ground}</button>
                                            </div>
                                        </div>
                                        
                                        <div class="relative bg-gray-700 rounded-lg p-4 h-64 mb-4" id="circuitCanvas">
                                            <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
                                            <!-- Circuit components will be added here -->
                                        </div>
                                        
                                        <div class="grid grid-cols-4 gap-2 mb-4">
                                            <button id="runSimulation" class="btn-accent px-3 py-2 rounded text-sm col-span-2" data-lang-key="run_simulation">${languageData[currentLanguage].run_simulation}</button>
                                            <button id="stopSimulation" class="bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded text-sm" data-lang-key="stop_simulation">${languageData[currentLanguage].stop_simulation}</button>
                                            <button id="resetCircuit" class="bg-gray-600 hover:bg-gray-500 px-3 py-2 rounded text-sm" data-lang-key="reset_circuit">${languageData[currentLanguage].reset_circuit}</button>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <button id="checkCircuit" class="btn-accent px-4 py-2 rounded w-full" data-lang-key="check_circuit">${languageData[currentLanguage].check_circuit}</button>
                                            <p id="circuitFeedback" class="mt-2 text-sm hidden" data-lang-key="circuit_feedback">${languageData[currentLanguage].circuit_feedback}</p>
                                        </div>
                                        
                                        <div class="bg-gray-700 rounded-lg p-4">
                                            <canvas id="circuitChart" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        break;
                        
                    default:
                        content = `
                            <div class="max-w-4xl mx-auto text-center py-12">
                                <h2 class="text-3xl font-bold mb-6">Module Coming Soon</h2>
                                <p class="text-lg mb-8">This module is currently under development. Please check back later or try another module.</p>
                                <button id="backToHome" class="btn-accent px-6 py-3 rounded-lg">Back to Home</button>
                            </div>
                        `;
                }
                
                moduleContent.innerHTML = content;
                
                // Initialize module-specific functionality
                initializeModule(moduleId);
            }, 800); // Simulated loading delay
        }

        // Initialize module-specific functionality
        function initializeModule(moduleId) {
            switch(moduleId) {
                case 'ecg-fundamentals':
                    // ECG electrode placement simulation
                    const electrodes = document.querySelectorAll('.electrode');
                    const checkBtn = document.getElementById('checkPlacement');
                    const feedback = document.getElementById('placementFeedback');
                    
                    let isDragging = false;
                    let currentElectrode = null;
                    
                    electrodes.forEach(electrode => {
                        electrode.addEventListener('mousedown', (e) => {
                            isDragging = true;
                            currentElectrode = electrode;
                            electrode.style.cursor = 'grabbing';
                            e.preventDefault(); // Prevent text selection
                        });
                    });
                    
                    document.addEventListener('mousemove', (e) => {
                        if (!isDragging || !currentElectrode) return;
                        
                        const container = document.getElementById('ecgPlacementArea');
                        const containerRect = container.getBoundingClientRect();
                        
                        // Calculate position relative to container
                        let x = e.clientX - containerRect.left;
                        let y = e.clientY - containerRect.top;
                        
                        // Constrain to container bounds
                        x = Math.max(10, Math.min(containerRect.width - 10, x));
                        y = Math.max(10, Math.min(containerRect.height - 10, y));
                        
                        // Update position
                        currentElectrode.style.left = `${x}px`;
                        currentElectrode.style.top = `${y}px`;
                    });
                    
                    document.addEventListener('mouseup', () => {
                        isDragging = false;
                        if (currentElectrode) {
                            currentElectrode.style.cursor = 'grab';
                            currentElectrode = null;
                        }
                    });
                    
                    checkBtn.addEventListener('click', () => {
                        // Simplified check - in real app would verify positions
                        const correctPositions = {
                            RA: { x: 100, y: 50 },
                            LA: { x: 300, y: 50 },
                            RL: { x: 100, y: 250 },
                            LL: { x: 300, y: 250 }
                        };
                        
                        let allCorrect = true;
                        
                        electrodes.forEach(electrode => {
                            const position = electrode.dataset.position;
                            const rect = electrode.getBoundingClientRect();
                            const containerRect = document.getElementById('ecgPlacementArea').getBoundingClientRect();
                            
                            const x = rect.left - containerRect.left + rect.width/2;
                            const y = rect.top - containerRect.top + rect.height/2;
                            
                            const target = correctPositions[position];
                            const distance = Math.sqrt(Math.pow(x - target.x, 2) + Math.pow(y - target.y, 2));
                            
                            if (distance > 40) {
                                allCorrect = false;
                            }
                        });
                        
                        if (allCorrect) {
                            feedback.textContent = languageData[currentLanguage].correct_placement;
                            feedback.classList.remove('hidden', 'text-red-500');
                            feedback.classList.add('text-green-500');
                        } else {
                            feedback.textContent = languageData[currentLanguage].try_again;
                            feedback.classList.remove('hidden', 'text-green-500');
                            feedback.classList.add('text-red-500');
                        }
                        
                        feedback.classList.remove('hidden');
                    });
                    break;
                    
                case 'ecg-leads':
                    // ECG leads chart
                    const leadSelector = document.getElementById('leadSelector');
                    const ecgChartCtx = document.getElementById('ecgChart').getContext('2d');
                    
                    // Sample ECG data for different leads
                    const ecgData = {
                        'I': generateECGWaveform(1.0, 0.8),
                        'II': generateECGWaveform(1.5, 1.0),
                        'III': generateECGWaveform(1.2, 0.7),
                        'aVR': generateECGWaveform(-0.5, -0.3),
                        'aVL': generateECGWaveform(0.8, 0.5),
                        'aVF': generateECGWaveform(1.3, 0.9),
                        'V1': generateECGWaveform(0.3, 0.2, true),
                        'V2': generateECGWaveform(0.5, 0.3, true),
                        'V3': generateECGWaveform(0.7, 0.4, true),
                        'V4': generateECGWaveform(1.0, 0.6, true),
                        'V5': generateECGWaveform(1.2, 0.8, true),
                        'V6': generateECGWaveform(1.0, 0.7, true)
                    };
                    
                    let ecgChart = new Chart(ecgChartCtx, {
                        type: 'line',
                        data: {
                            labels: Array.from({length: 500}, (_, i) => i),
                            datasets: [{
                                label: 'ECG',
                                data: ecgData['II'],
                                borderColor: '#4f46e5',
                                borderWidth: 2,
                                pointRadius: 0,
                                fill: false
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                duration: 0
                            },
                            scales: {
                                x: {
                                    display: false
                                },
                                y: {
                                    min: -2,
                                    max: 2,
                                    grid: {
                                        color: '#
</body>
</html>